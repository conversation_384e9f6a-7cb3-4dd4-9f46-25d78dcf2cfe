(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9992],{13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},14186:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,asChild:n=!1,...o}=e,c=n?s.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(l({variant:r}),t),...o})}},29869:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:r,size:n,className:t})),...c})}},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47924:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},57434:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155),s=r(12115),n=r(59434);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),ref:t,...i})});i.displayName="Input"},62811:(e,t,r)=>{Promise.resolve().then(r.bind(r,72378))},63743:(e,t,r)=>{"use strict";r.d(t,{Fw:()=>u,NM:()=>x,Nj:()=>c,Tr:()=>o,mU:()=>a,ng:()=>d,t2:()=>l});let a={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}},s={DISPONIBILE:a.STATUS.SUCCESS,IN_USO:a.STATUS.WARNING,TERMINATA:a.NEUTRAL,OVER:a.STATUS.ERROR,VUOTA:a.NEUTRAL,ERRORE:a.STATUS.ERROR},n={DA_INSTALLARE:a.NEUTRAL,INSTALLATO:a.STATUS.SUCCESS,COLLEGATO_PARTENZA:a.STATUS.WARNING,COLLEGATO_ARRIVO:a.STATUS.WARNING,COLLEGATO:a.STATUS.SUCCESS,CERTIFICATO:a.STATUS.SUCCESS,SPARE:a.STATUS.WARNING,ERRORE:a.STATUS.ERROR},i={ATTIVA:a.STATUS.SUCCESS,COMPLETATA:a.STATUS.SUCCESS,ANNULLATA:a.NEUTRAL,IN_CORSO:a.STATUS.WARNING,ERRORE:a.STATUS.ERROR},l=e=>{let t=s[null==e?void 0:e.toUpperCase()]||s.ERRORE;return{badge:"".concat(t.bg," ").concat(t.text," rounded-full px-3 py-1 text-xs font-medium"),text:t.text,bg:t.bg,border:t.border,hex:t.hex}},o=e=>{let t=n[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||n.ERRORE;return{badge:"".concat(t.bg," ").concat(t.text," rounded-full px-3 py-1 text-xs font-medium"),text:t.text,bg:t.bg,border:t.border,hex:t.hex}},c=()=>({button:"inline-flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-semibold ".concat(a.PRIMARY.text," ").concat(a.NEUTRAL.bg_white," ").concat(a.PRIMARY.border," ").concat(a.PRIMARY.hover," ").concat(a.PRIMARY.active," transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md border-2"),text:a.PRIMARY.text,border:a.PRIMARY.border,hover:a.PRIMARY.hover}),d=()=>({button:"inline-flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-medium ".concat(a.PRIMARY.text," bg-blue-50 border border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 cursor-pointer"),text:a.PRIMARY.text,bg:"bg-blue-50",hover:"hover:bg-blue-100"}),x=()=>({text:"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ".concat(a.NEUTRAL.text_light),color:a.NEUTRAL.text_light}),u=e=>{let t=i[null==e?void 0:e.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:"".concat(t.bg," ").concat(t.text," ").concat(t.border),button:"".concat(t.bg," ").concat(t.text," ").concat(t.border," ").concat(t.hover),alert:"".concat(t.bg," ").concat(t.text," ").concat(t.border),text:t.text,bg:t.bg,border:t.border,hover:t.hover,hex:t.hex}};a.STATUS.ERROR,a.STATUS.WARNING,a.NEUTRAL,a.NEUTRAL},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},69037:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},72378:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var a=r(95155),s=r(12115),n=r(66695),i=r(30285),l=r(26126),o=r(63743),c=r(62523),d=r(85127),x=r(40283),u=r(25731),h=r(57434),m=r(91788),b=r(29869),v=r(84616),p=r(40646),g=r(85339),f=r(14186),j=r(47924),A=r(51154),N=r(92657),y=r(13717),R=r(69037);function w(){let[e,t]=(0,s.useState)(""),[r,w]=(0,s.useState)("all"),[S,T]=(0,s.useState)([]),[C,k]=(0,s.useState)(!0),[E,_]=(0,s.useState)(""),{user:L,cantiere:U}=(0,x.A)();(0,s.useEffect)(()=>{I()},[]);let I=async()=>{try{k(!0),_("");let e=(null==U?void 0:U.id_cantiere)||(null==L?void 0:L.id_utente);if(!e)return void _("Cantiere non selezionato");let t=await u.FH.get("/certificazioni/".concat(e));T(t)}catch(r){var e,t;_((null==(t=r.response)||null==(e=t.data)?void 0:e.detail)||"Errore durante il caricamento delle certificazioni")}finally{k(!1)}},z=e=>{let t="NEUTRAL",r=e||"Da Verificare";switch(null==e?void 0:e.toLowerCase()){case"conforme":case"pass":case"ok":t="SUCCESS",r="Conforme";break;case"non conforme":case"fail":case"ko":t="ERROR",r="Non Conforme";break;case"in corso":case"pending":t="WARNING",r="In Corso";break;default:t="NEUTRAL"}let s=(0,o.getSoftColorClasses)(t);return(0,a.jsx)(l.E,{className:s.badge,children:r})},O=S.filter(t=>{var a,s,n,i,l,o,c,d,x;let u=(null==(a=t.id_cavo)?void 0:a.toLowerCase().includes(e.toLowerCase()))||(null==(s=t.operatore)?void 0:s.toLowerCase().includes(e.toLowerCase()))||(null==(n=t.strumento_utilizzato)?void 0:n.toLowerCase().includes(e.toLowerCase())),h=!0;if("all"!==r)switch(r){case"conforme":h=(null==(i=t.risultato)?void 0:i.toLowerCase().includes("conforme"))||(null==(l=t.risultato)?void 0:l.toLowerCase())==="pass";break;case"non_conforme":h=(null==(o=t.risultato)?void 0:o.toLowerCase().includes("non conforme"))||(null==(c=t.risultato)?void 0:c.toLowerCase())==="fail";break;case"in_corso":h=(null==(d=t.risultato)?void 0:d.toLowerCase().includes("corso"))||(null==(x=t.risultato)?void 0:x.toLowerCase())==="pending"}return u&&h}),M={totali:S.length,conformi:S.filter(e=>{var t,r;return(null==(t=e.risultato)?void 0:t.toLowerCase().includes("conforme"))||(null==(r=e.risultato)?void 0:r.toLowerCase())==="pass"}).length,non_conformi:S.filter(e=>{var t,r;return(null==(t=e.risultato)?void 0:t.toLowerCase().includes("non conforme"))||(null==(r=e.risultato)?void 0:r.toLowerCase())==="fail"}).length,in_corso:S.filter(e=>{var t,r;return(null==(t=e.risultato)?void 0:t.toLowerCase().includes("corso"))||(null==(r=e.risultato)?void 0:r.toLowerCase())==="pending"}).length};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,a.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-600"}),"Certificazioni"]}),(0,a.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa delle certificazioni e test dei cavi"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,a.jsxs)(i.$,{size:"sm",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:M.totali})]}),(0,a.jsx)(h.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:M.conformi})]}),(0,a.jsx)(p.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-600",children:M.non_conformi})]}),(0,a.jsx)(g.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:M.in_corso})]}),(0,a.jsx)(f.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(c.p,{placeholder:"Cerca per ID cavo, operatore o strumento...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,a.jsx)("div",{className:"flex gap-2",children:["all","conforme","non_conforme","in_corso"].map(e=>(0,a.jsx)(i.$,{variant:r===e?"default":"outline",size:"sm",onClick:()=>w(e),children:"all"===e?"Tutte":"conforme"===e?"Conformi":"non_conforme"===e?"Non Conformi":"In Corso"},e))})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{children:["Elenco Certificazioni (",O.length,")"]}),(0,a.jsx)(n.BT,{children:"Gestione completa delle certificazioni con risultati e dettagli tecnici"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"ID Cavo"}),(0,a.jsx)(d.nd,{children:"Data Certificazione"}),(0,a.jsx)(d.nd,{children:"Risultato"}),(0,a.jsx)(d.nd,{children:"Operatore"}),(0,a.jsx)(d.nd,{children:"Strumento"}),(0,a.jsx)(d.nd,{children:"Note"}),(0,a.jsx)(d.nd,{children:"Azioni"})]})}),(0,a.jsx)(d.BF,{children:C?(0,a.jsx)(d.Hj,{children:(0,a.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)(A.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})})}):E?(0,a.jsx)(d.Hj,{children:(0,a.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),E]})})}):0===O.length?(0,a.jsx)(d.Hj,{children:(0,a.jsx)(d.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):O.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{className:"font-medium",children:e.id_cavo}),(0,a.jsx)(d.nA,{children:new Date(e.data_certificazione).toLocaleDateString("it-IT")}),(0,a.jsx)(d.nA,{children:z(e.risultato)}),(0,a.jsx)(d.nA,{children:e.operatore||"-"}),(0,a.jsx)(d.nA,{children:e.strumento_utilizzato||"-"}),(0,a.jsx)(d.nA,{children:(0,a.jsx)("div",{className:"max-w-xs truncate",title:e.note,children:e.note||"-"})}),(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(R.A,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]})})}},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85127:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",t),...r})})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("data-[state=selected]:bg-muted border-b",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},91788:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[3455,3464,283,8441,1684,7358],()=>t(62811)),_N_E=e.O()}]);