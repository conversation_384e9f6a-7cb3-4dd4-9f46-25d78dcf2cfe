(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{7958:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>er});var t=s(95155),i=s(12115);s(40975);var r=s(66695),l=s(59434),n=s(51154);let c=i.forwardRef((e,a)=>{let{className:s,variant:i="primary",size:r="md",loading:c=!1,glow:d=!1,icon:o,children:m,disabled:x,...u}=e,h=x||c;return(0,t.jsxs)("button",{className:(0,l.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[i],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[r],d&&"quick"!==i&&"btn-glow",h&&"opacity-50 cursor-not-allowed hover:shadow-none",s),disabled:h,ref:a,...u,children:[(0,t.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,t.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[c?(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin btn-icon"}):o?(0,t.jsx)("span",{className:"btn-icon",children:o}):null,m]})]})});c.displayName="AnimatedButton";let d=e=>(0,t.jsx)(c,{variant:"primary",...e}),o=e=>(0,t.jsx)(c,{variant:"secondary",...e}),m=e=>(0,t.jsx)(c,{variant:"danger",...e});var x=s(1243),u=s(54416),h=s(13717),p=s(14186),g=s(40646),j=s(62525),b=s(30285);function v(e){let{user:a,onEdit:s,onToggleStatus:r,onDelete:l}=e,[n,c]=(0,i.useState)(!1);return n?(0,t.jsxs)("div",{className:"flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-xs text-red-700 font-medium",children:"Eliminare?"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,t.jsx)(b.$,{size:"sm",variant:"destructive",onClick:e=>{e.preventDefault(),e.stopPropagation(),c(!1),l()},className:"h-6 px-2 text-xs",children:"S\xec"}),(0,t.jsx)(b.$,{size:"sm",variant:"outline",onClick:e=>{e.preventDefault(),e.stopPropagation(),c(!1)},className:"h-6 px-2 text-xs",children:(0,t.jsx)(u.A,{className:"h-3 w-3"})})]})]}):(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),s()},type:"button",className:"p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1","aria-label":"Modifica utente ".concat(a.username),children:(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Modifica utente"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),(!a.abilitato||window.confirm("Sei sicuro di voler disabilitare l'utente \"".concat(a.username,"\"?\n\nL'utente non potr\xe0 pi\xf9 accedere al sistema.")))&&r()},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-slate-50 focus:ring-slate-500"),"aria-label":a.abilitato?"Disabilita utente ".concat(a.username):"Abilita utente ".concat(a.username),children:a.abilitato?(0,t.jsx)(p.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),"owner"!==a.ruolo&&(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:a.abilitato?"Disabilita utente":"Abilita utente"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),c(!0)},disabled:"owner"===a.ruolo,type:"button",className:"p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ".concat("owner"===a.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-red-50 focus:ring-red-500"),"aria-label":"Elimina utente ".concat(a.username),children:(0,t.jsx)(j.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})}),"owner"!==a.ruolo&&(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Elimina utente"})]})]})}var N=s(26126),f=s(62523),w=s(63743),y=s(85127),z=s(17313),A=s(40283),E=s(35695),C=s(25731),S=s(85057),T=s(59409),_=s(47262),R=s(61610),k=s(85339),D=s(71007),I=s(75525),V=s(78749),U=s(92657),P=s(23227),L=s(28883),Z=s(4516),B=s(381),G=s(81284),O=s(4229);function F(e){let{user:a,onSave:s,onCancel:l}=e,[n,c]=(0,i.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[m,x]=(0,i.useState)({}),[j,v]=(0,i.useState)(!1),[w,y]=(0,i.useState)(""),[z,A]=(0,i.useState)(""),[E,F]=(0,i.useState)(!1),[$,J]=(0,i.useState)(0),[X,M]=(0,i.useState)({}),[q,H]=(0,i.useState)(!1),Q=e=>{let a=0;return e.length>=8&&(a+=25),/[a-z]/.test(e)&&(a+=25),/[A-Z]/.test(e)&&(a+=25),/[0-9]/.test(e)&&(a+=25),/[^A-Za-z0-9]/.test(e)&&(a+=25),Math.min(a,100)},W=(e,s)=>{let t="pending";switch(e){case"username":t=s&&s.length>=3?"valid":"invalid";break;case"password":t=a?!s||s.length>=8?"valid":"invalid":s&&s.length>=8?"valid":"invalid";break;case"ragione_sociale":t=s&&s.length>=2?"valid":"invalid";break;case"email":t=s?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s)?"valid":"invalid":"valid";break;default:t="valid"}return M(a=>({...a,[e]:t})),t};(0,i.useEffect)(()=>{if(a){let e={username:a.username||"",password:"",ruolo:a.ruolo||"user",data_scadenza:a.data_scadenza?a.data_scadenza.split("T")[0]:"",abilitato:void 0===a.abilitato||a.abilitato,ragione_sociale:a.ragione_sociale||"",indirizzo:a.indirizzo||"",nazione:a.nazione||"",email:a.email||"",vat:a.vat||"",referente_aziendale:a.referente_aziendale||""};c(e),Object.entries(e).forEach(e=>{let[a,s]=e;W(a,s)})}},[a]),(0,i.useEffect)(()=>{H((a?["username","ragione_sociale"]:["username","password","ragione_sociale"]).every(e=>"valid"===X[e]))},[X,a]);let Y=(e,a)=>{c(s=>({...s,[e]:a})),W(e,a),"password"===e&&J(Q(a)),m[e]&&x(a=>({...a,[e]:""})),z&&A(""),w&&y("")},K=()=>{let e=(0,R.GN)({username:n.username,password:a?void 0:n.password,ragione_sociale:n.ragione_sociale,email:n.email,vat:n.vat,indirizzo:n.indirizzo,nazione:n.nazione,referente_aziendale:n.referente_aziendale});return x(e.errors),e.isValid},ee=async e=>{e.preventDefault();let t="user-form-".concat((null==a?void 0:a.id_utente)||"new","-").concat(Date.now());if(!(0,R.Eb)(t,5,6e4))return void y("Troppi tentativi. Riprova tra un minuto.");if(K()){v(!0),y("");try{let e,t={...n};a||(t.ruolo="user"),a&&!t.password.trim()&&delete t.password,t.data_scadenza&&(t.data_scadenza=t.data_scadenza),e=a?await C.dG.updateUser(a.id_utente,t):await C.dG.createUser(t),A(a?"Utente aggiornato con successo!":"Nuovo utente creato con successo!"),setTimeout(()=>{s(e)},1500)}catch(e){var i,r;y((null==(r=e.response)||null==(i=r.data)?void 0:i.detail)||e.message||"Errore durante il salvataggio dell'utente")}finally{v(!1)}}},ea=e=>{let{status:a}=e;return"valid"===a?(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-500"}):"invalid"===a?(0,t.jsx)(k.A,{className:"h-4 w-4 text-red-500"}):null};return(0,t.jsxs)(r.Zp,{className:"shadow-lg",children:[(0,t.jsx)(r.aR,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:a?(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,t.jsx)(D.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(r.ZB,{className:"text-xl",children:a?"Modifica Utente: ".concat(a.username):"Crea Nuovo Utente Standard"}),(0,t.jsx)(r.BT,{children:a?"Aggiorna le informazioni dell'utente esistente":"Inserisci i dati per creare un nuovo utente nel sistema"})]})]})}),(0,t.jsxs)(r.Wu,{className:"p-6",children:[w&&(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,t.jsx)(k.A,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-red-600",children:w})]}),z&&(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-green-600",children:z})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[(0,t.jsx)("span",{children:"Completamento form"}),(0,t.jsx)("span",{children:q?"✓ Completo":"In corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ".concat(q?"bg-green-500":"bg-blue-500"),style:{width:"".concat(q?100:60,"%")}})})]}),(0,t.jsxs)("form",{onSubmit:ee,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(I.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Credenziali di Accesso"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"username",className:"flex items-center gap-2",children:["Username *",(0,t.jsx)(ea,{status:X.username||"pending"})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(f.p,{id:"username",value:n.username,onChange:e=>Y("username",e.target.value),disabled:j,className:"".concat(m.username?"border-red-500":"valid"===X.username?"border-green-500":""," transition-colors duration-200"),placeholder:"Inserisci username univoco"})}),m.username&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.username]}),"valid"===X.username&&!m.username&&(0,t.jsxs)("p",{className:"text-sm text-green-600 flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"h-3 w-3"}),"Username valido"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"password",className:"flex items-center gap-2",children:[a?"Nuova Password (lascia vuoto per non modificare)":"Password *",(0,t.jsx)(ea,{status:X.password||"pending"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.p,{id:"password",type:E?"text":"password",value:n.password,onChange:e=>Y("password",e.target.value),disabled:j,className:"".concat(m.password?"border-red-500":"valid"===X.password?"border-green-500":""," pr-10 transition-colors duration-200"),placeholder:a?"Lascia vuoto per mantenere la password attuale":"Inserisci password sicura"}),(0,t.jsx)(b.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>F(!E),disabled:j,children:E?(0,t.jsx)(V.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(U.A,{className:"h-4 w-4 text-gray-400"})})]}),m.password&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.password]}),(0,t.jsx)(()=>n.password?(0,t.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,t.jsx)("span",{className:"text-slate-600",children:"Forza password:"}),(0,t.jsx)("span",{className:"font-medium ".concat($<50?"text-red-600":$<75?"text-yellow-600":"text-green-600"),children:$<25?"Molto debole":$<50?"Debole":$<75?"Media":$<100?"Forte":"Molto forte"})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat($<50?"bg-red-500":$<75?"bg-yellow-500":"bg-green-500"),style:{width:"".concat($,"%")}})})]}):null,{})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(P.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Informazioni Aziendali"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"ragione_sociale",className:"flex items-center gap-2",children:["Ragione Sociale *",(0,t.jsx)(ea,{status:X.ragione_sociale||"pending"})]}),(0,t.jsx)(f.p,{id:"ragione_sociale",value:n.ragione_sociale,onChange:e=>Y("ragione_sociale",e.target.value),disabled:j,className:"".concat(m.ragione_sociale?"border-red-500":"valid"===X.ragione_sociale?"border-green-500":""," transition-colors duration-200"),placeholder:"Nome dell'azienda o organizzazione"}),m.ragione_sociale&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.ragione_sociale]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"email",className:"flex items-center gap-2",children:[(0,t.jsx)(L.A,{className:"h-4 w-4"}),"Email",(0,t.jsx)(ea,{status:X.email||"pending"})]}),(0,t.jsx)(f.p,{id:"email",type:"email",value:n.email,onChange:e=>Y("email",e.target.value),disabled:j,className:"".concat(m.email?"border-red-500":"valid"===X.email?"border-green-500":""," transition-colors duration-200"),placeholder:"<EMAIL>"}),m.email&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),m.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"indirizzo",className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),"Indirizzo"]}),(0,t.jsx)(f.p,{id:"indirizzo",value:n.indirizzo,onChange:e=>Y("indirizzo",e.target.value),disabled:j,placeholder:"Via, numero civico, citt\xe0",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"nazione",children:"Nazione"}),(0,t.jsx)(f.p,{id:"nazione",value:n.nazione,onChange:e=>Y("nazione",e.target.value),disabled:j,placeholder:"Italia",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"vat",children:"Partita IVA"}),(0,t.jsx)(f.p,{id:"vat",value:n.vat,onChange:e=>Y("vat",e.target.value),disabled:j,placeholder:"*************",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,t.jsx)(f.p,{id:"referente_aziendale",value:n.referente_aziendale,onChange:e=>Y("referente_aziendale",e.target.value),disabled:j,placeholder:"Nome e cognome del referente",className:"transition-colors duration-200"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(B.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Configurazioni Account"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"data_scadenza",className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),"Data Scadenza"]}),(0,t.jsx)(f.p,{id:"data_scadenza",type:"date",value:n.data_scadenza,onChange:e=>Y("data_scadenza",e.target.value),disabled:j,className:"transition-colors duration-200"}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:"Lascia vuoto per account senza scadenza"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"ruolo",children:"Ruolo Utente"}),a?(0,t.jsxs)(T.l6,{value:n.ruolo,onValueChange:e=>Y("ruolo",e),disabled:j,children:[(0,t.jsx)(T.bq,{className:"transition-colors duration-200",children:(0,t.jsx)(T.yv,{})}),(0,t.jsxs)(T.gC,{children:[(0,t.jsx)(T.eb,{value:"user",children:"User Standard"}),(0,t.jsx)(T.eb,{value:"cantieri_user",children:"Cantieri User"})]})]}):(0,t.jsxs)("div",{className:"px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2",children:[(0,t.jsx)(N.E,{variant:"outline",className:"bg-blue-100 text-blue-700",children:"User Standard"}),(0,t.jsx)("span",{children:"Ruolo predefinito per nuovi utenti"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-slate-50 rounded-lg",children:[(0,t.jsx)(_.S,{id:"abilitato",checked:n.abilitato,onCheckedChange:e=>Y("abilitato",e),disabled:j||a&&"owner"===a.ruolo}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(S.J,{htmlFor:"abilitato",className:"font-medium",children:"Account Abilitato"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"L'utente pu\xf2 accedere al sistema e utilizzare le funzionalit\xe0"})]}),n.abilitato?(0,t.jsx)(N.E,{className:"bg-green-100 text-green-700",children:"Attivo"}):(0,t.jsx)(N.E,{variant:"outline",className:"bg-red-100 text-red-700",children:"Disabilitato"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200",children:[(0,t.jsx)("div",{className:"text-sm text-slate-600",children:q?(0,t.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"Form completato correttamente"]}):(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),"Completa i campi obbligatori per continuare"]})}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)(o,{type:"button",onClick:l,disabled:j,icon:(0,t.jsx)(u.A,{className:"h-4 w-4"}),className:"min-w-[120px]",children:"Annulla"}),(0,t.jsx)(d,{type:"submit",loading:j,disabled:!q,icon:(0,t.jsx)(O.A,{className:"h-4 w-4"}),glow:q,className:"min-w-[120px]",children:j?"Salvataggio...":a?"Aggiorna Utente":"Crea Utente"})]})]})]})]})]})}var $=s(54213),J=s(53904);function X(){let[e,a]=(0,i.useState)(null),[s,l]=(0,i.useState)(!1),[c,o]=(0,i.useState)(""),m=async()=>{l(!0),o("");try{let e=await C.dG.getDatabaseData();a(e)}catch(a){var e,s;o((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati del database")}finally{l(!1)}};(0,i.useEffect)(()=>{m()},[]);let x=(e,a,s)=>{if(!a||0===a.length)return(0,t.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",s]});let i=Object.keys(a[0]);return(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,t.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900",children:s}),(0,t.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,t.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,t.jsxs)(y.XI,{children:[(0,t.jsx)(y.A0,{className:"sticky top-0 bg-slate-50",children:(0,t.jsx)(y.Hj,{children:i.map(e=>(0,t.jsx)(y.nd,{className:"font-medium",children:e},e))})}),(0,t.jsx)(y.BF,{children:a.map((e,a)=>(0,t.jsx)(y.Hj,{children:i.map(a=>(0,t.jsx)(y.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,t.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},u=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,t.jsx)(d,{size:"sm",onClick:m,loading:s,icon:(0,t.jsx)(J.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,t.jsxs)(r.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(U.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),s?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,t.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):c?(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,t.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,t.jsx)("p",{className:"text-red-600",children:c})]}):e?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),u.map(a=>e[a.key]&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),x(a.key,e[a.key],a.title)]},a.key)),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:u.map(a=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,t.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,t.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}var M=s(40133);function q(){let{user:e}=(0,A.A)(),[a,s]=(0,i.useState)(""),[l,n]=(0,i.useState)(!1),[c,d]=(0,i.useState)(""),[u,h]=(0,i.useState)(!1),[g,v]=(0,i.useState)(!1),[N,w]=(0,i.useState)(""),[y,z]=(0,i.useState)(""),[E,T]=(0,i.useState)(10),[R,k]=(0,i.useState)(!1),[D,P]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e;return R&&E>0?e=setInterval(()=>{T(e=>e-1)},1e3):0===E&&(P(!0),k(!1)),()=>clearInterval(e)},[R,E]);let L=async()=>{if(!D)return void w("Devi completare il countdown di sicurezza");v(!0),w(""),z("");try{if(!c.trim())throw Error("Password amministratore richiesta");await C.dG.resetDatabase(),z("Database resettato con successo! Tutti i dati sono stati eliminati."),s(""),n(!1),d(""),P(!1),k(!1),T(10)}catch(s){var e,a;w((null==(a=s.response)||null==(e=a.data)?void 0:e.detail)||s.message||"Errore durante il reset del database")}finally{v(!1)}},Z="RESET DATABASE"===a&&l&&c.trim()&&!g&&!R,B=D&&!g;return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(M.A,{className:"h-5 w-5"}),"Reset Database"]})}),(0,t.jsxs)(r.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(x.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,t.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,t.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,t.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,t.jsx)("li",{children:"Tutti i cavi installati"}),(0,t.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,t.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,t.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,t.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),N&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:N})}),y&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-green-600",children:y})}),(0,t.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,t.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,t.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,t.jsx)(f.p,{id:"confirm-text",value:a,onChange:e=>s(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:g||R,className:"RESET DATABASE"===a?"border-green-500":""})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(_.S,{id:"confirm-checkbox",checked:l,onCheckedChange:n,disabled:g||R}),(0,t.jsx)(S.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"admin-password",className:"text-sm font-medium flex items-center gap-2",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 text-blue-600"}),"3. Inserisci la tua password di amministratore per confermare l'identit\xe0"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.p,{id:"admin-password",type:u?"text":"password",value:c,onChange:e=>d(e.target.value),placeholder:"Password amministratore",disabled:g||R,className:c.trim()?"border-green-500":""}),(0,t.jsx)(b.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0",onClick:()=>h(!u),disabled:g||R,children:u?(0,t.jsx)(V.A,{className:"h-4 w-4"}):(0,t.jsx)(U.A,{className:"h-4 w-4"})})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-3",children:"Stato Conferma:"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("RESET DATABASE"===a?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===a?"✓ Corretto":"✗ Richiesto"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(l?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Checkbox confermata: ",l?"✓ S\xec":"✗ Richiesta"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(c.trim()?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{children:["Password amministratore: ",c.trim()?"✓ Inserita":"✗ Richiesta"]})]}),R&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)("span",{className:"text-orange-700 font-medium",children:["Countdown di sicurezza: ",E," secondi"]})]}),D&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-700 font-medium",children:"⚠️ Pronto per il reset - Ultima possibilit\xe0 di annullare"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[!D&&!R&&(0,t.jsx)(m,{onClick:()=>{"RESET DATABASE"===a&&l&&c.trim()?(k(!0),T(10),P(!1),w("")):w("Completa tutti i passaggi di conferma prima di procedere")},disabled:!Z,className:"w-full",size:"lg",icon:(0,t.jsx)(p.A,{className:"h-5 w-5"}),children:"INIZIA COUNTDOWN DI SICUREZZA (10 secondi)"}),R&&(0,t.jsxs)("div",{className:"w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,t.jsx)(p.A,{className:"h-6 w-6 text-orange-600 animate-pulse"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-orange-700",children:["Countdown: ",E," secondi"]})]}),(0,t.jsx)("p",{className:"text-sm text-orange-600 mt-2",children:"Il pulsante di reset si attiver\xe0 al termine del countdown"})]}),D&&(0,t.jsx)(m,{onClick:L,disabled:!B,className:"w-full animate-pulse",size:"lg",loading:g,icon:(0,t.jsx)(j.A,{className:"h-5 w-5"}),glow:!0,children:g?"RESET IN CORSO...":"\uD83D\uDEA8 RESET DATABASE - ELIMINA TUTTI I DATI \uD83D\uDEA8"}),!Z&&!D&&!R&&(0,t.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per iniziare il countdown"}),D&&(0,t.jsx)(o,{onClick:()=>{P(!1),k(!1),T(10)},className:"w-full",size:"lg",disabled:g,children:"ANNULLA RESET"})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,t.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,t.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,t.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,t.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,t.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var H=s(3493),Q=s(43332),W=s(48136),Y=s(57434),K=s(84616);function ee(){let[e,a]=(0,i.useState)("categorie");return(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,t.jsxs)(r.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(H.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,t.jsxs)(z.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,t.jsxs)(z.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(z.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),"Categorie"]}),(0,t.jsxs)(z.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,t.jsx)(W.A,{className:"h-4 w-4"}),"Produttori"]}),(0,t.jsxs)(z.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,t.jsx)(Y.A,{className:"h-4 w-4"}),"Standard"]}),(0,t.jsxs)(z.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,t.jsxs)(z.av,{value:"categorie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,t.jsxs)(z.av,{value:"produttori",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(W.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,t.jsxs)(z.av,{value:"standard",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Y.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,t.jsxs)(z.av,{value:"tipologie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(K.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(H.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,t.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var ea=s(17580),es=s(12318),et=s(47924),ei=s(70306);function er(){let e=(0,E.useRouter)(),[a,s]=(0,i.useState)("visualizza-utenti"),[l,c]=(0,i.useState)(""),[o,m]=(0,i.useState)([]),[x,u]=(0,i.useState)([]),[p,g]=(0,i.useState)(!0),[j,b]=(0,i.useState)(""),[S,T]=(0,i.useState)(null),[_,R]=(0,i.useState)({open:!1,message:"",severity:"success"}),{user:k,impersonateUser:D}=(0,A.A)();(0,i.useEffect)(()=>{I()},[a]);let I=async()=>{try{if(g(!0),b(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await C.dG.getUsers();m(e)}else if("cantieri"===a){let e=await C._I.getCantieri();u(e)}}catch(a){var e,s;b((null==(s=a.response)||null==(e=s.data)?void 0:e.detail)||a.message||"Errore durante il caricamento dei dati")}finally{g(!1)}},V=e=>{T(e),s("modifica-utente")},U=async e=>{try{await C.dG.toggleUserStatus(e),I()}catch(e){var a,s;b((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante la modifica dello stato utente")}},P=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await C.dG.deleteUser(e),I()}catch(e){var a,s;b((null==(s=e.response)||null==(a=s.data)?void 0:a.detail)||"Errore durante l'eliminazione dell'utente")}},L=e=>{T(null),s("visualizza-utenti"),I()},Z=()=>{T(null),s("visualizza-utenti")},B=async a=>{try{await D(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){var s,t;b((null==(t=e.response)||null==(s=t.data)?void 0:s.detail)||e.message||"Errore durante l'impersonificazione")}},G=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let s=(0,w.getSoftColorClasses)(a);return(0,t.jsx)(N.E,{className:s.badge,children:e})},O=(e,a)=>{let s="SUCCESS",i="Attivo",r="●";if(e)if(a){let e=new Date(a),t=new Date;e<t?(s="ERROR",i="Scaduto",r="⚠"):e.getTime()-t.getTime()<6048e5?(s="WARNING",i="In Scadenza",r="⏰"):r="✓"}else r="✓";else s="ERROR",i="Disabilitato",r="●";let l=(0,w.getSoftColorClasses)(s);return(0,t.jsxs)(N.E,{className:"".concat(l.badge," flex items-center gap-1"),children:[(0,t.jsx)("span",{className:"text-xs",role:"img","aria-hidden":"true",children:r}),(0,t.jsx)("span",{children:i})]})},J=o.filter(e=>{var a,s,t;return(null==(a=e.username)?void 0:a.toLowerCase().includes(l.toLowerCase()))||(null==(s=e.ragione_sociale)?void 0:s.toLowerCase().includes(l.toLowerCase()))||(null==(t=e.email)?void 0:t.toLowerCase().includes(l.toLowerCase()))});return k&&"owner"===k.ruolo?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,t.jsxs)(z.tU,{value:a,onValueChange:s,className:"w-full",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1",children:(0,t.jsxs)(z.j7,{className:"grid w-full ".concat(S?"grid-cols-5":"grid-cols-4"," gap-1 h-auto bg-transparent p-0"),children:[(0,t.jsxs)(z.Xi,{value:"visualizza-utenti",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(ea.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Gestione Utenti"})]}),(0,t.jsxs)(z.Xi,{value:"crea-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(es.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Crea Nuovo Utente"})]}),S&&(0,t.jsxs)(z.Xi,{value:"modifica-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Modifica Utente"})]}),(0,t.jsxs)(z.Xi,{value:"database-tipologie-cavi",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Database Tipologie Cavi"})]}),(0,t.jsxs)(z.Xi,{value:"visualizza-database-raw",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)($.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Gestione Dati Avanzata"})]})]})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 px-4 py-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-sm font-medium text-red-800",children:"Impostazioni Avanzate e Pericolose"})]}),(0,t.jsx)(z.j7,{className:"h-auto bg-transparent p-0",children:(0,t.jsxs)(z.Xi,{value:"reset-database",className:"admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium",children:[(0,t.jsx)(M.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Reset Database"})]})})]})})]}),(0,t.jsxs)(z.av,{value:"visualizza-utenti",className:"space-y-4",children:[j&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:j})}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(ea.A,{className:"h-5 w-5 text-blue-600"}),"Lista Utenti"]}),(0,t.jsx)(r.BT,{children:"Gestisci tutti gli utenti del sistema CABLYS"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(et.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,t.jsx)(f.p,{placeholder:"Cerca per username, email o ragione sociale...",value:l,onChange:e=>c(e.target.value),className:"pl-10 w-80"})]}),(0,t.jsxs)(N.E,{variant:"outline",className:"text-xs",children:[J.length," utenti"]})]})]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(y.XI,{children:[(0,t.jsx)(y.A0,{children:(0,t.jsxs)(y.Hj,{children:[(0,t.jsx)(y.nd,{className:"w-[50px] text-center",children:"ID"}),(0,t.jsx)(y.nd,{className:"w-[100px]",children:"Username"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Password"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Ruolo"}),(0,t.jsx)(y.nd,{className:"w-[180px]",children:"Ragione Sociale"}),(0,t.jsx)(y.nd,{className:"w-[160px]",children:"Email"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"VAT"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Nazione"}),(0,t.jsx)(y.nd,{className:"w-[120px]",children:"Referente"}),(0,t.jsx)(y.nd,{className:"w-[90px] text-center",children:"Scadenza"}),(0,t.jsx)(y.nd,{className:"w-[80px] text-center",children:"Stato"}),(0,t.jsx)(y.nd,{className:"w-[100px] text-center",children:"Azioni"})]})}),(0,t.jsx)(y.BF,{children:p?(0,t.jsx)(y.Hj,{children:(0,t.jsx)(y.nA,{colSpan:12,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===o.length?(0,t.jsx)(y.Hj,{children:(0,t.jsx)(y.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):J.map(e=>(0,t.jsxs)(y.Hj,{className:"users-table-row",children:[(0,t.jsx)(y.nA,{className:"text-center",children:(0,t.jsxs)(N.E,{variant:"outline",className:"text-xs font-mono",children:["#",e.id_utente]})}),(0,t.jsx)(y.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,t.jsx)(y.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,t.jsx)("div",{className:"flex gap-1",children:[...Array(8)].map((e,a)=>(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-400 rounded-full"},a))}),(0,t.jsx)("div",{className:"w-2 h-2 rounded-full ml-2 ".concat(e.password_plain?"bg-green-500":"bg-red-500"),title:e.password_plain?"Password configurata":"Password non configurata"})]})}),(0,t.jsx)(y.nA,{className:"text-center",children:G(e.ruolo)}),(0,t.jsx)(y.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,t.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,t.jsx)(y.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,t.jsx)(y.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,t.jsx)(y.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,t.jsx)(y.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,t.jsx)(y.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,t.jsx)(y.nA,{className:"text-center",children:O(e.abilitato,e.data_scadenza)}),(0,t.jsx)(y.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(v,{user:e,onEdit:()=>V(e),onToggleStatus:()=>U(e.id_utente),onDelete:()=>P(e.id_utente)}),(0,t.jsx)(d,{size:"sm",onClick:()=>B(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,t.jsx)(ei.A,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,t.jsx)(z.av,{value:"crea-utente",className:"space-y-4",children:(0,t.jsx)(F,{user:null,onSave:L,onCancel:Z})}),S&&(0,t.jsx)(z.av,{value:"modifica-utente",className:"space-y-4",children:(0,t.jsx)(F,{user:S,onSave:L,onCancel:Z})}),(0,t.jsx)(z.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,t.jsx)(ee,{})}),(0,t.jsx)(z.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,t.jsx)(X,{})}),(0,t.jsx)(z.av,{value:"reset-database",className:"space-y-4",children:(0,t.jsx)(q,{})})]})})}):(window.location.replace("/login"),null)}},17313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>n});var t=s(95155),i=s(12115),r=s(30064),l=s(59434);let n=r.bL,c=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...i})});c.displayName=r.B8.displayName;let d=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...i})});d.displayName=r.l9.displayName;let o=i.forwardRef((e,a)=>{let{className:s,...i}=e;return(0,t.jsx)(r.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...i})});o.displayName=r.UC.displayName},40975:()=>{},42221:(e,a,s)=>{Promise.resolve().then(s.bind(s,7958))},47262:(e,a,s)=>{"use strict";s.d(a,{S:()=>n});var t=s(95155);s(12115);var i=s(76981),r=s(5196),l=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(i.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...s,children:(0,t.jsx)(i.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},61610:(e,a,s)=>{"use strict";s.d(a,{Eb:()=>h,GN:()=>p});let t=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,i=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,r=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(t,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},c=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},d=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},o=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:255;return l(e).length>a?{isValid:!1,error:"Testo troppo lungo (max ".concat(a," caratteri)")}:r.test(e)||i.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0}},m=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},x=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},u=new Map,h=(e,a,s)=>{let t=Date.now(),i=u.get(e);return!i||t>i.resetTime?(u.set(e,{count:1,resetTime:t+s}),!0):!(i.count>=a)&&(i.count++,!0)},p=e=>{let a={},s=n(e.username);if(s.isValid||(a.username=s.error),e.password){let s=c(e.password);s.isValid||(a.password=s.error)}let t=m(e.ragione_sociale);if(t.isValid||(a.ragione_sociale=t.error),e.email){let s=d(e.email);s.isValid||(a.email=s.error)}if(e.vat){let s=x(e.vat);s.isValid||(a.vat=s.error)}if(e.indirizzo){let s=o(e.indirizzo,200);s.isValid||(a.indirizzo=s.error)}if(e.nazione){let s=o(e.nazione,50);s.isValid||(a.nazione=s.error)}if(e.referente_aziendale){let s=o(e.referente_aziendale,100);s.isValid||(a.referente_aziendale=s.error)}return{isValid:0===Object.keys(a).length,errors:a}}}},e=>{var a=a=>e(e.s=a);e.O(0,[8902,3455,3464,4295,1587,1807,8148,283,1642,8441,1684,7358],()=>a(42221)),_N_E=e.O()}]);