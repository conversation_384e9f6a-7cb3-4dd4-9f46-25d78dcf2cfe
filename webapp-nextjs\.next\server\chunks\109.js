"use strict";exports.id=109,exports.ids=[109],exports.modules={6211:(e,t,r)=>{r.d(t,{A0:()=>n,BF:()=>i,Hj:()=>d,XI:()=>o,nA:()=>c,nd:()=>l});var a=r(60687);r(43210);var s=r(4780);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function n({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function d({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("data-[state=selected]:bg-muted border-b",e),...t})}function l({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},15079:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>b,gC:()=>x,l6:()=>l,yv:()=>c});var a=r(60687);r(43210);var s=r(97822),o=r(78272),n=r(13964),i=r(3589),d=r(4780);function l({...e}){return(0,a.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(s.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...n}){return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:t,position:r="popper",...o}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...o,children:[(0,a.jsx)(g,{}),(0,a.jsx)(s.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(p,{})]})})}function b({className:e,children:t,...r}){return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:t})]})}function g({className:e,...t}){return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function p({className:e,...t}){return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}},15391:(e,t,r)=>{r.d(t,{Fw:()=>u,NM:()=>c,Nj:()=>l,Tr:()=>d,mU:()=>a,t2:()=>i});let a={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}},s={DISPONIBILE:a.STATUS.SUCCESS,IN_USO:a.STATUS.WARNING,TERMINATA:a.NEUTRAL,OVER:a.STATUS.ERROR,VUOTA:a.NEUTRAL,ERRORE:a.STATUS.ERROR},o={DA_INSTALLARE:a.NEUTRAL,INSTALLATO:a.STATUS.SUCCESS,COLLEGATO_PARTENZA:a.STATUS.WARNING,COLLEGATO_ARRIVO:a.STATUS.WARNING,COLLEGATO:a.STATUS.SUCCESS,CERTIFICATO:a.STATUS.SUCCESS,SPARE:a.STATUS.WARNING,ERRORE:a.STATUS.ERROR},n={ATTIVA:a.STATUS.SUCCESS,COMPLETATA:a.STATUS.SUCCESS,ANNULLATA:a.NEUTRAL,IN_CORSO:a.STATUS.WARNING,ERRORE:a.STATUS.ERROR},i=e=>{let t=s[e?.toUpperCase()]||s.ERRORE;return{badge:`${t.bg} ${t.text} rounded-full px-3 py-1 text-xs font-medium`,text:t.text,bg:t.bg,border:t.border,hex:t.hex}},d=e=>{let t=o[e?.toUpperCase().replace(/\s+/g,"_")]||o.ERRORE;return{badge:`${t.bg} ${t.text} rounded-full px-3 py-1 text-xs font-medium`,text:t.text,bg:t.bg,border:t.border,hex:t.hex}},l=()=>({button:`inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${a.PRIMARY.text} ${a.NEUTRAL.bg_white} ${a.PRIMARY.border} ${a.PRIMARY.hover} ${a.PRIMARY.active} transition-colors cursor-pointer`,text:a.PRIMARY.text,border:a.PRIMARY.border,hover:a.PRIMARY.hover}),c=()=>({text:`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${a.NEUTRAL.text_light}`,color:a.NEUTRAL.text_light}),u=e=>{let t=n[e?.toUpperCase().replace(/\s+/g,"_")]||n.ERRORE;return{badge:`${t.bg} ${t.text} ${t.border}`,button:`${t.bg} ${t.text} ${t.border} ${t.hover}`,alert:`${t.bg} ${t.text} ${t.border}`,text:t.text,bg:t.bg,border:t.border,hover:t.hover,hex:t.hex}};a.STATUS.ERROR,a.STATUS.WARNING,a.NEUTRAL,a.NEUTRAL},44493:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>n});var a=r(60687);r(43210);var s=r(4780);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},70440:(e,t,r)=>{r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80013:(e,t,r)=>{r.d(t,{J:()=>n});var a=r(60687);r(43210);var s=r(78148),o=r(4780);function n({className:e,...t}){return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},89667:(e,t,r)=>{r.d(t,{p:()=>n});var a=r(60687),s=r(43210),o=r(4780);let n=s.forwardRef(({className:e,type:t,...r},s)=>(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:s,...r}));n.displayName="Input"},96834:(e,t,r)=>{r.d(t,{E:()=>d});var a=r(60687);r(43210);var s=r(8730),o=r(24224),n=r(4780);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...o}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(i({variant:t}),e),...o})}}};