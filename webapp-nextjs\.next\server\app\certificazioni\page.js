(()=>{var e={};e.id=992,e.ids=[992],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>n,BF:()=>o,Hj:()=>l,XI:()=>i,nA:()=>d,nd:()=>c});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm border-collapse",e),...t})})}function n({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("data-[state=selected]:bg-muted border-b",e),...t})}function c({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10002:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["certificazioni",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71792)),"C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/certificazioni/page",pathname:"/certificazioni",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15391:(e,t,r)=>{"use strict";r.d(t,{Fw:()=>u,NM:()=>x,Nj:()=>c,Tr:()=>l,mU:()=>s,ng:()=>d,t2:()=>o});let s={PRIMARY:{bg:"bg-blue-50",text:"text-blue-600",border:"border-blue-300",hover:"hover:bg-blue-50",active:"hover:border-blue-400",hex:"#007bff"},NEUTRAL:{text_dark:"text-gray-800",text_medium:"text-gray-600",text_light:"text-gray-500",bg_white:"bg-white",bg_light:"bg-gray-50",border:"border-gray-300",hex_dark:"#343A40",hex_medium:"#6c757d",hex_light:"#DEE2E6"},STATUS:{SUCCESS:{bg:"bg-green-50",text:"text-green-700",border:"border-green-200",hex:"#28A745"},WARNING:{bg:"bg-orange-50",text:"text-orange-700",border:"border-orange-200",hex:"#FD7E14"},ERROR:{bg:"bg-red-50",text:"text-red-700",border:"border-red-200",hex:"#DC3545"}}},a={DISPONIBILE:s.STATUS.SUCCESS,IN_USO:s.STATUS.WARNING,TERMINATA:s.NEUTRAL,OVER:s.STATUS.ERROR,VUOTA:s.NEUTRAL,ERRORE:s.STATUS.ERROR},i={DA_INSTALLARE:s.NEUTRAL,INSTALLATO:s.STATUS.SUCCESS,COLLEGATO_PARTENZA:s.STATUS.WARNING,COLLEGATO_ARRIVO:s.STATUS.WARNING,COLLEGATO:s.STATUS.SUCCESS,CERTIFICATO:s.STATUS.SUCCESS,SPARE:s.STATUS.WARNING,ERRORE:s.STATUS.ERROR},n={ATTIVA:s.STATUS.SUCCESS,COMPLETATA:s.STATUS.SUCCESS,ANNULLATA:s.NEUTRAL,IN_CORSO:s.STATUS.WARNING,ERRORE:s.STATUS.ERROR},o=e=>{let t=a[e?.toUpperCase()]||a.ERRORE;return{badge:`${t.bg} ${t.text} rounded-full px-3 py-1 text-xs font-medium`,text:t.text,bg:t.bg,border:t.border,hex:t.hex}},l=e=>{let t=i[e?.toUpperCase().replace(/\s+/g,"_")]||i.ERRORE;return{badge:`${t.bg} ${t.text} rounded-full px-3 py-1 text-xs font-medium`,text:t.text,bg:t.bg,border:t.border,hex:t.hex}},c=()=>({button:`inline-flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-semibold ${s.PRIMARY.text} ${s.NEUTRAL.bg_white} ${s.PRIMARY.border} ${s.PRIMARY.hover} ${s.PRIMARY.active} transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md border-2`,text:s.PRIMARY.text,border:s.PRIMARY.border,hover:s.PRIMARY.hover}),d=()=>({button:`inline-flex items-center gap-1 px-3 py-1.5 rounded-md text-xs font-medium ${s.PRIMARY.text} bg-blue-50 border border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 cursor-pointer`,text:s.PRIMARY.text,bg:"bg-blue-50",hover:"hover:bg-blue-100"}),x=()=>({text:`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${s.NEUTRAL.text_light}`,color:s.NEUTRAL.text_light}),u=e=>{let t=n[e?.toUpperCase().replace(/\s+/g,"_")]||n.ERRORE;return{badge:`${t.bg} ${t.text} ${t.border}`,button:`${t.bg} ${t.text} ${t.border} ${t.hover}`,alert:`${t.bg} ${t.text} ${t.border}`,text:t.text,bg:t.bg,border:t.border,hover:t.hover,hex:t.hex}};s.STATUS.ERROR,s.STATUS.WARNING,s.NEUTRAL,s.NEUTRAL},16023:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},44765:(e,t,r)=>{Promise.resolve().then(r.bind(r,71792))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56381:(e,t,r)=>{Promise.resolve().then(r.bind(r,62358))},62358:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(60687),a=r(43210),i=r(44493),n=r(29523),o=r(96834),l=r(15391),c=r(89667),d=r(6211),x=r(63213);r(62185);var u=r(10022),p=r(31158),h=r(16023),m=r(96474),b=r(5336),f=r(93613),g=r(48730),v=r(99270),j=r(41862),A=r(13861),N=r(63143),R=r(86561);function w(){let[e,t]=(0,a.useState)(""),[r,w]=(0,a.useState)("all"),[S,C]=(0,a.useState)([]),[y,T]=(0,a.useState)(!0),[E,_]=(0,a.useState)(""),{user:L,cantiere:U}=(0,x.A)(),k=e=>{let t="NEUTRAL",r=e||"Da Verificare";switch(e?.toLowerCase()){case"conforme":case"pass":case"ok":t="SUCCESS",r="Conforme";break;case"non conforme":case"fail":case"ko":t="ERROR",r="Non Conforme";break;case"in corso":case"pending":t="WARNING",r="In Corso";break;default:t="NEUTRAL"}let a=(0,l.getSoftColorClasses)(t);return(0,s.jsx)(o.E,{className:a.badge,children:r})},I=S.filter(t=>{let s=t.id_cavo?.toLowerCase().includes(e.toLowerCase())||t.operatore?.toLowerCase().includes(e.toLowerCase())||t.strumento_utilizzato?.toLowerCase().includes(e.toLowerCase()),a=!0;if("all"!==r)switch(r){case"conforme":a=t.risultato?.toLowerCase().includes("conforme")||t.risultato?.toLowerCase()==="pass";break;case"non_conforme":a=t.risultato?.toLowerCase().includes("non conforme")||t.risultato?.toLowerCase()==="fail";break;case"in_corso":a=t.risultato?.toLowerCase().includes("corso")||t.risultato?.toLowerCase()==="pending"}return s&&a}),z={totali:S.length,conformi:S.filter(e=>e.risultato?.toLowerCase().includes("conforme")||e.risultato?.toLowerCase()==="pass").length,non_conformi:S.filter(e=>e.risultato?.toLowerCase().includes("non conforme")||e.risultato?.toLowerCase()==="fail").length,in_corso:S.filter(e=>e.risultato?.toLowerCase().includes("corso")||e.risultato?.toLowerCase()==="pending").length};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-slate-900 flex items-center gap-3",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),"Certificazioni"]}),(0,s.jsx)("p",{className:"text-slate-600 mt-1",children:"Gestione completa delle certificazioni e test dei cavi"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Esporta"]}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Importa"]}),(0,s.jsxs)(n.$,{size:"sm",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Nuova Certificazione"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Totali"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-slate-900",children:z.totali})]}),(0,s.jsx)(u.A,{className:"h-8 w-8 text-blue-500"})]})})}),(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Conformi"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-green-600",children:z.conformi})]}),(0,s.jsx)(b.A,{className:"h-8 w-8 text-green-500"})]})})}),(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"Non Conformi"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-red-600",children:z.non_conformi})]}),(0,s.jsx)(f.A,{className:"h-8 w-8 text-red-500"})]})})}),(0,s.jsx)(i.Zp,{children:(0,s.jsx)(i.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"In Corso"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:z.in_corso})]}),(0,s.jsx)(g.A,{className:"h-8 w-8 text-yellow-500"})]})})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(v.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(c.p,{placeholder:"Cerca per ID cavo, operatore o strumento...",value:e,onChange:e=>t(e.target.value),className:"w-full"})}),(0,s.jsx)("div",{className:"flex gap-2",children:["all","conforme","non_conforme","in_corso"].map(e=>(0,s.jsx)(n.$,{variant:r===e?"default":"outline",size:"sm",onClick:()=>w(e),children:"all"===e?"Tutte":"conforme"===e?"Conformi":"non_conforme"===e?"Non Conformi":"In Corso"},e))})]})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsxs)(i.ZB,{children:["Elenco Certificazioni (",I.length,")"]}),(0,s.jsx)(i.BT,{children:"Gestione completa delle certificazioni con risultati e dettagli tecnici"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(d.XI,{children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nd,{children:"ID Cavo"}),(0,s.jsx)(d.nd,{children:"Data Certificazione"}),(0,s.jsx)(d.nd,{children:"Risultato"}),(0,s.jsx)(d.nd,{children:"Operatore"}),(0,s.jsx)(d.nd,{children:"Strumento"}),(0,s.jsx)(d.nd,{children:"Note"}),(0,s.jsx)(d.nd,{children:"Azioni"})]})}),(0,s.jsx)(d.BF,{children:y?(0,s.jsx)(d.Hj,{children:(0,s.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),"Caricamento certificazioni..."]})})}):E?(0,s.jsx)(d.Hj,{children:(0,s.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),E]})})}):0===I.length?(0,s.jsx)(d.Hj,{children:(0,s.jsx)(d.nA,{colSpan:7,className:"text-center py-8 text-slate-500",children:"Nessuna certificazione trovata"})}):I.map(e=>(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nA,{className:"font-medium",children:e.id_cavo}),(0,s.jsx)(d.nA,{children:new Date(e.data_certificazione).toLocaleDateString("it-IT")}),(0,s.jsx)(d.nA,{children:k(e.risultato)}),(0,s.jsx)(d.nA,{children:e.operatore||"-"}),(0,s.jsx)(d.nA,{children:e.strumento_utilizzato||"-"}),(0,s.jsx)(d.nA,{children:(0,s.jsx)("div",{className:"max-w-xs truncate",title:e.note,children:e.note||"-"})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex gap-1",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(A.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(N.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(R.A,{className:"h-4 w-4"})})]})})]},e.id_certificazione))})]})})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\certificazioni\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\certificazioni\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86561:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687),a=r(43210),i=r(4780);let n=a.forwardRef(({className:e,type:t,...r},a)=>(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:a,...r}));n.displayName="Input"},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,991,658,223],()=>r(10002));module.exports=s})();