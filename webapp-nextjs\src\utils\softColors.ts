/**
 * PALETTE COLORI CENTRALIZZATA v3.0 - CABLYS
 * Versione ristretta e professionale per eliminare l'effetto "Arlecchino"
 * Data: 30 Giugno 2025
 */

// COLORI PRIMARI E NEUTRI (Uso rigoroso e limitato)
export const CABLYS_COLORS = {
  // COLORE PRIMARIO - Blu CABLYS (Unico colore per azioni e elementi cliccabili)
  PRIMARY: {
    bg: 'bg-blue-50',           // #eff6ff - Sfondo molto chiaro per hover
    text: 'text-blue-600',      // #2563eb - Testo/bordi (equivalente a #007bff)
    border: 'border-blue-300',  // #93c5fd - Bordi
    hover: 'hover:bg-blue-50',  // #eff6ff - Hover state
    active: 'hover:border-blue-400', // #60a5fa - Bordo attivo
    hex: '#007bff'              // Blu primario CABLYS
  },

  // COLORI NEUTRI - Grigio (Per testo, disabilitato, non disponibile)
  NEUTRAL: {
    text_dark: 'text-gray-800',     // #1f2937 - Testo principale
    text_medium: 'text-gray-600',   // #4b5563 - Testo secondario
    text_light: 'text-gray-500',    // #6b7280 - Testo disabilitato
    bg_white: 'bg-white',           // #ffffff - Sfondo bianco
    bg_light: 'bg-gray-50',         // #f9fafb - Sfondo hover leggero
    border: 'border-gray-300',      // #d1d5db - Bordi divisori
    hex_dark: '#343A40',            // Grigio antracite per massima leggibilità
    hex_medium: '#6c757d',          // Grigio medio
    hex_light: '#DEE2E6'            // Grigio chiaro per bordi
  },

  // COLORI DI STATO (Desaturati e pastello per badge informativi)
  STATUS: {
    // Successo/Positivo - Verde desaturato
    SUCCESS: {
      bg: 'bg-green-50',          // #f0fdf4 - Verde molto pallido
      text: 'text-green-700',     // #15803d - Verde scuro per testo
      border: 'border-green-200', // #bbf7d0 - Bordo verde chiaro
      hex: '#28A745'              // Verde pulito
    },

    // Avviso/Attenzione - Arancione desaturato
    WARNING: {
      bg: 'bg-orange-50',         // #fff7ed - Arancione molto pallido
      text: 'text-orange-700',    // #c2410c - Arancione scuro per testo
      border: 'border-orange-200', // #fed7aa - Bordo arancione chiaro
      hex: '#FD7E14'              // Arancione energico
    },

    // Errore/Pericolo - Rosso desaturato
    ERROR: {
      bg: 'bg-red-50',            // #fef2f2 - Rosso molto pallido
      text: 'text-red-700',       // #b91c1c - Rosso scuro per testo
      border: 'border-red-200',   // #fecaca - Bordo rosso chiaro
      hex: '#DC3545'              // Rosso chiaro e diretto
    }
  }
}

/**
 * MAPPATURE STATI BOBINE - Palette Centralizzata v3.0
 * Usa solo colori di stato desaturati per badge informativi
 */
export const BOBINA_COLORS = {
  DISPONIBILE: CABLYS_COLORS.STATUS.SUCCESS,
  IN_USO: CABLYS_COLORS.STATUS.WARNING,      // Arancione per "in uso"
  TERMINATA: CABLYS_COLORS.NEUTRAL,          // Grigio neutro
  OVER: CABLYS_COLORS.STATUS.ERROR,          // Rosso per "over"
  VUOTA: CABLYS_COLORS.NEUTRAL,              // Grigio neutro per vuota
  ERRORE: CABLYS_COLORS.STATUS.ERROR
}

/**
 * MAPPATURE STATI CAVI - Palette Centralizzata v3.0
 * Usa solo colori di stato desaturati per badge informativi
 */
export const CAVO_COLORS = {
  DA_INSTALLARE: CABLYS_COLORS.NEUTRAL,      // Grigio neutro
  INSTALLATO: CABLYS_COLORS.STATUS.SUCCESS,  // Verde per successo
  COLLEGATO_PARTENZA: CABLYS_COLORS.STATUS.WARNING, // Arancione per parziale
  COLLEGATO_ARRIVO: CABLYS_COLORS.STATUS.WARNING,   // Arancione per parziale
  COLLEGATO: CABLYS_COLORS.STATUS.SUCCESS,   // Verde per completo
  CERTIFICATO: CABLYS_COLORS.STATUS.SUCCESS, // Verde per certificato
  SPARE: CABLYS_COLORS.STATUS.WARNING,       // Arancione per spare
  ERRORE: CABLYS_COLORS.STATUS.ERROR
}

/**
 * MAPPATURE STATI COMANDE - Palette Centralizzata v3.0
 * Usa solo colori di stato desaturati per badge informativi
 */
export const COMANDA_COLORS = {
  ATTIVA: CABLYS_COLORS.STATUS.SUCCESS,
  COMPLETATA: CABLYS_COLORS.STATUS.SUCCESS,
  ANNULLATA: CABLYS_COLORS.NEUTRAL,
  IN_CORSO: CABLYS_COLORS.STATUS.WARNING,
  ERRORE: CABLYS_COLORS.STATUS.ERROR
}

/**
 * FUNZIONI UTILITY - Palette Centralizzata v3.0
 * Restituiscono classi CSS per badge informativi (NON cliccabili)
 */

export const getBobinaColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS
  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE

  return {
    // Badge informativo a pillola (senza hover - NON cliccabile)
    badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hex: color.hex
  }
}

export const getCavoColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_') as keyof typeof CAVO_COLORS
  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE

  return {
    // Badge informativo a pillola (senza hover - NON cliccabile)
    badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hex: color.hex
  }
}

/**
 * UTILITY PER PULSANTI AZIONE - Palette Centralizzata v3.0
 * Restituisce classi per pulsanti cliccabili con stile outline
 */
export const getPrimaryActionClasses = () => {
  return {
    // Pulsante outline primario (blu CABLYS)
    button: `inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${CABLYS_COLORS.PRIMARY.text} ${CABLYS_COLORS.NEUTRAL.bg_white} ${CABLYS_COLORS.PRIMARY.border} ${CABLYS_COLORS.PRIMARY.hover} ${CABLYS_COLORS.PRIMARY.active} transition-colors cursor-pointer`,
    text: CABLYS_COLORS.PRIMARY.text,
    border: CABLYS_COLORS.PRIMARY.border,
    hover: CABLYS_COLORS.PRIMARY.hover
  }
}

/**
 * UTILITY PER ELEMENTI NON DISPONIBILI - Palette Centralizzata v3.0
 * Restituisce classi per testo statico non cliccabile
 */
export const getUnavailableClasses = () => {
  return {
    // Testo grigio statico (NON cliccabile)
    text: `inline-flex items-center gap-1 px-2 py-1 text-xs font-medium ${CABLYS_COLORS.NEUTRAL.text_light}`,
    color: CABLYS_COLORS.NEUTRAL.text_light
  }
}

export const getComandaColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_') as keyof typeof COMANDA_COLORS
  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE
  
  return {
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex
  }
}

/**
 * UTILITY PER PERCENTUALI DI PROGRESSO - Palette Centralizzata v3.0
 */
export const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return CABLYS_COLORS.STATUS.SUCCESS
  if (percentage >= 70) return CABLYS_COLORS.STATUS.SUCCESS
  if (percentage >= 50) return CABLYS_COLORS.STATUS.WARNING
  if (percentage >= 30) return CABLYS_COLORS.STATUS.WARNING
  return CABLYS_COLORS.STATUS.ERROR
}

/**
 * MAPPATURE PRIORITÀ - Palette Centralizzata v3.0
 */
export const PRIORITY_COLORS = {
  ALTA: CABLYS_COLORS.STATUS.ERROR,
  MEDIA: CABLYS_COLORS.STATUS.WARNING,
  BASSA: CABLYS_COLORS.NEUTRAL,
  NORMALE: CABLYS_COLORS.NEUTRAL
}

export const getPriorityColorClasses = (priority: string) => {
  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS
  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE

  return {
    // Badge informativo a pillola (senza hover - NON cliccabile)
    badge: `${color.bg} ${color.text} rounded-full px-3 py-1 text-xs font-medium`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hex: color.hex
  }
}
