{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/hooks/useCantiere.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Cantiere } from '@/types'\n\ninterface UseCantiereResult {\n  cantiereId: number | null\n  cantiere: Cantiere | null\n  isValidCantiere: boolean\n  isLoading: boolean\n  error: string | null\n  validateCantiere: (id: number | string) => boolean\n  clearError: () => void\n}\n\n/**\n * Hook personalizzato per la gestione robusta del cantiere selezionato\n * Gestisce validazione, errori e sincronizzazione con AuthContext\n */\nexport function useCantiere(): UseCantiereResult {\n  const { cantiere, isLoading: authLoading } = useAuth()\n  const [cantiereId, setCantiereId] = useState<number | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // Funzione per validare un ID cantiere\n  const validateCantiere = (id: number | string): boolean => {\n    if (id === null || id === undefined) return false\n    \n    const numId = typeof id === 'string' ? parseInt(id, 10) : id\n    \n    if (isNaN(numId) || numId <= 0) {\n      console.warn('🏗️ useCantiere: ID cantiere non valido:', id)\n      return false\n    }\n    \n    return true\n  }\n\n  // Effetto per sincronizzare con AuthContext e localStorage\n  useEffect(() => {\n    if (authLoading) {\n      console.log('🏗️ useCantiere: Autenticazione in corso...')\n      return\n    }\n\n    setIsLoading(true)\n    setError(null)\n\n    try {\n      let selectedId: number | null = null\n\n      // Priorità 1: Cantiere dal context di autenticazione (login cantiere diretto)\n      if (cantiere?.id_cantiere && validateCantiere(cantiere.id_cantiere)) {\n        selectedId = cantiere.id_cantiere\n        console.log('🏗️ useCantiere: Usando cantiere dal context (login cantiere):', selectedId)\n      } else {\n        // Priorità 2: Cantiere dal localStorage (cantiere_data per login cantiere)\n        const cantiereData = localStorage.getItem('cantiere_data')\n        if (cantiereData) {\n          try {\n            const parsedData = JSON.parse(cantiereData)\n            if (parsedData.id_cantiere && validateCantiere(parsedData.id_cantiere)) {\n              selectedId = parsedData.id_cantiere\n              console.log('🏗️ useCantiere: Usando cantiere da cantiere_data:', selectedId)\n            }\n          } catch (parseError) {\n            console.warn('🏗️ useCantiere: Errore parsing cantiere_data:', parseError)\n          }\n        }\n\n        // Priorità 3: Cantiere dal localStorage (selectedCantiereId per selezione manuale)\n        if (!selectedId) {\n          const storedId = localStorage.getItem('selectedCantiereId')\n          if (storedId && validateCantiere(storedId)) {\n            selectedId = parseInt(storedId, 10)\n            console.log('🏗️ useCantiere: Usando cantiere da selectedCantiereId:', selectedId)\n          }\n        }\n      }\n\n      if (selectedId) {\n        setCantiereId(selectedId)\n        console.log('🏗️ useCantiere: Cantiere valido impostato:', selectedId)\n      } else {\n        console.warn('🏗️ useCantiere: Nessun cantiere valido trovato')\n        setCantiereId(null)\n        setError('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n      }\n    } catch (err) {\n      console.error('🏗️ useCantiere: Errore nella gestione cantiere:', err)\n      setError('Errore nella gestione del cantiere selezionato.')\n      setCantiereId(null)\n    } finally {\n      setIsLoading(false)\n    }\n  }, [cantiere, authLoading])\n\n  const clearError = () => setError(null)\n\n  return {\n    cantiereId,\n    cantiere,\n    isValidCantiere: cantiereId !== null && cantiereId > 0,\n    isLoading,\n    error,\n    validateCantiere,\n    clearError\n  }\n}\n\n/**\n * Hook semplificato che restituisce solo l'ID del cantiere valido o null\n */\nexport function useCantiereId(): number | null {\n  const { cantiereId } = useCantiere()\n  return cantiereId\n}\n\n/**\n * Hook che forza la presenza di un cantiere valido\n * Lancia un errore se non c'è un cantiere selezionato\n */\nexport function useRequiredCantiere(): { cantiereId: number; cantiere: Cantiere | null } {\n  const { cantiereId, cantiere, isLoading, error } = useCantiere()\n\n  if (isLoading) {\n    throw new Error('Caricamento cantiere in corso...')\n  }\n\n  if (error) {\n    throw new Error(error)\n  }\n\n  if (!cantiereId || cantiereId <= 0) {\n    throw new Error('Nessun cantiere selezionato. Seleziona un cantiere per continuare.')\n  }\n\n  return { cantiereId, cantiere }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;AAoBO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,uCAAuC;IACvC,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,QAAQ,OAAO,WAAW,OAAO;QAE5C,MAAM,QAAQ,OAAO,OAAO,WAAW,SAAS,IAAI,MAAM;QAE1D,IAAI,MAAM,UAAU,SAAS,GAAG;YAC9B,QAAQ,IAAI,CAAC,4CAA4C;YACzD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,IAAI,aAA4B;YAEhC,8EAA8E;YAC9E,IAAI,UAAU,eAAe,iBAAiB,SAAS,WAAW,GAAG;gBACnE,aAAa,SAAS,WAAW;gBACjC,QAAQ,GAAG,CAAC,kEAAkE;YAChF,OAAO;gBACL,2EAA2E;gBAC3E,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,IAAI,cAAc;oBAChB,IAAI;wBACF,MAAM,aAAa,KAAK,KAAK,CAAC;wBAC9B,IAAI,WAAW,WAAW,IAAI,iBAAiB,WAAW,WAAW,GAAG;4BACtE,aAAa,WAAW,WAAW;4BACnC,QAAQ,GAAG,CAAC,sDAAsD;wBACpE;oBACF,EAAE,OAAO,YAAY;wBACnB,QAAQ,IAAI,CAAC,kDAAkD;oBACjE;gBACF;gBAEA,mFAAmF;gBACnF,IAAI,CAAC,YAAY;oBACf,MAAM,WAAW,aAAa,OAAO,CAAC;oBACtC,IAAI,YAAY,iBAAiB,WAAW;wBAC1C,aAAa,SAAS,UAAU;wBAChC,QAAQ,GAAG,CAAC,2DAA2D;oBACzE;gBACF;YACF;YAEA,IAAI,YAAY;gBACd,cAAc;gBACd,QAAQ,GAAG,CAAC,+CAA+C;YAC7D,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,cAAc;gBACd,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oDAAoD;YAClE,SAAS;YACT,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,aAAa,IAAM,SAAS;IAElC,OAAO;QACL;QACA;QACA,iBAAiB,eAAe,QAAQ,aAAa;QACrD;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,OAAO;AACT;AAMO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEnD,IAAI,WAAW;QACb,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,OAAO;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,cAAc,cAAc,GAAG;QAClC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAY;IAAS;AAChC", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cantiere/CantiereErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertCircle, Construction, RefreshCw, ArrowLeft } from 'lucide-react'\nimport { useRouter } from 'next/navigation'\nimport { useCantiere } from '@/hooks/useCantiere'\n\ninterface CantiereErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n  showBackButton?: boolean\n  backUrl?: string\n}\n\n/**\n * Componente per gestire errori relativi alla selezione del cantiere\n * Mostra messaggi di errore appropriati e opzioni di recupero\n */\nexport function CantiereErrorBoundary({ \n  children, \n  fallback, \n  showBackButton = true, \n  backUrl = '/cantieri' \n}: CantiereErrorBoundaryProps) {\n  const router = useRouter()\n  const { cantiereId, cantiere, isValidCantiere, isLoading, error, clearError } = useCantiere()\n\n  // Se stiamo caricando, mostra un loader\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          <Card>\n            <CardContent className=\"flex items-center justify-center p-8\">\n              <div className=\"text-center\">\n                <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600\" />\n                <p className=\"text-lg font-medium text-gray-700\">Caricamento cantiere...</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Verifica della selezione cantiere in corso</p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se c'è un errore o il cantiere non è valido, mostra l'errore\n  if (error || !isValidCantiere) {\n    const errorMessage = error || 'Nessun cantiere selezionato'\n    \n    if (fallback) {\n      return <>{fallback}</>\n    }\n\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6\">\n        <div className=\"max-w-4xl mx-auto space-y-6\">\n          \n          {/* Header con errore */}\n          <Card className=\"border-red-200 bg-red-50\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-red-800\">\n                <AlertCircle className=\"h-6 w-6 mr-2\" />\n                Problema con la selezione del cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <Alert variant=\"destructive\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription>\n                  <strong>Errore:</strong> {errorMessage}\n                </AlertDescription>\n              </Alert>\n            </CardContent>\n          </Card>\n\n          {/* Informazioni di debug */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center text-gray-700\">\n                <Construction className=\"h-5 w-5 mr-2\" />\n                Informazioni cantiere\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-600\">ID Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiereId || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Nome Cantiere:</span>\n                  <span className=\"ml-2 text-gray-800\">{cantiere?.commessa || 'Non disponibile'}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">Cantiere Valido:</span>\n                  <span className={`ml-2 ${isValidCantiere ? 'text-green-600' : 'text-red-600'}`}>\n                    {isValidCantiere ? 'Sì' : 'No'}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-600\">localStorage ID:</span>\n                  <span className=\"ml-2 text-gray-800\">\n                    {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereId') || 'Non presente' : 'N/A'}\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Azioni di recupero */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-gray-700\">Azioni disponibili</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex flex-wrap gap-3\">\n                \n                {/* Pulsante per tornare ai cantieri */}\n                {showBackButton && (\n                  <Button \n                    onClick={() => router.push(backUrl)}\n                    variant=\"default\"\n                    className=\"flex items-center\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                    Seleziona Cantiere\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire errore */}\n                {error && (\n                  <Button \n                    onClick={clearError}\n                    variant=\"outline\"\n                    className=\"flex items-center\"\n                  >\n                    <RefreshCw className=\"h-4 w-4 mr-2\" />\n                    Riprova\n                  </Button>\n                )}\n\n                {/* Pulsante per pulire localStorage */}\n                <Button \n                  onClick={() => {\n                    localStorage.removeItem('selectedCantiereId')\n                    localStorage.removeItem('selectedCantiereName')\n                    localStorage.removeItem('cantiere_data')\n                    window.location.reload()\n                  }}\n                  variant=\"outline\"\n                  className=\"flex items-center text-orange-600 border-orange-300 hover:bg-orange-50\"\n                >\n                  <AlertCircle className=\"h-4 w-4 mr-2\" />\n                  Reset Dati Cantiere\n                </Button>\n              </div>\n\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Suggerimento:</strong> Se il problema persiste, prova a selezionare nuovamente un cantiere \n                  dalla pagina principale o contatta l'amministratore del sistema.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    )\n  }\n\n  // Se tutto è ok, renderizza i children\n  return <>{children}</>\n}\n\n/**\n * Hook per utilizzare il CantiereErrorBoundary in modo condizionale\n */\nexport function useCantiereErrorBoundary() {\n  const { isValidCantiere, isLoading, error } = useCantiere()\n  \n  return {\n    shouldShowError: !isLoading && (!isValidCantiere || error),\n    isLoading,\n    error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AARA;;;;;;;;AAqBO,SAAS,sBAAsB,EACpC,QAAQ,EACR,QAAQ,EACR,iBAAiB,IAAI,EACrB,UAAU,WAAW,EACM;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAE1F,wCAAwC;IACxC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxD;IAEA,+DAA+D;IAC/D,IAAI,SAAS,CAAC,iBAAiB;QAC7B,MAAM,eAAe,SAAS;QAE9B,IAAI,UAAU;YACZ,qBAAO;0BAAG;;QACZ;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI5C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;sDACb,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;;8DACf,8OAAC;8DAAO;;;;;;gDAAgB;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,kNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI7C,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAAsB,cAAc;;;;;;;;;;;;sDAEtD,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DAAsB,UAAU,YAAY;;;;;;;;;;;;sDAE9D,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAW,CAAC,KAAK,EAAE,kBAAkB,mBAAmB,gBAAgB;8DAC3E,kBAAkB,OAAO;;;;;;;;;;;;sDAG9B,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,8OAAC;oDAAK,WAAU;8DACb,6EAA+F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1G,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAgB;;;;;;;;;;;0CAEvC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAI,WAAU;;4CAGZ,gCACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAMzC,uBACC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAM1C,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS;oDACP,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,aAAa,UAAU,CAAC;oDACxB,OAAO,QAAQ,CAAC,MAAM;gDACxB;gDACA,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAK5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;8DAAO;;;;;;gDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS9C;IAEA,uCAAuC;IACvC,qBAAO;kBAAG;;AACZ;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAExD,OAAO;QACL,iBAAiB,CAAC,aAAa,CAAC,CAAC,mBAAmB,KAAK;QACzD;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm border-collapse\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"data-[state=selected]:bg-muted border-b\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;YAC9D,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2CACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        data-slot=\"input\"\n        className={cn(\n          \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n          \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\n\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/FilterableTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo } from 'react'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from '@/components/ui/table'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport {\n  Filter,\n  ChevronDown,\n  ChevronUp,\n  ArrowUpDown,\n  ArrowUp,\n  ArrowDown,\n  X,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport interface ColumnDef {\n  field: string\n  headerName: string\n  dataType?: 'text' | 'number' | 'date'\n  align?: 'left' | 'center' | 'right'\n  width?: number\n  disableFilter?: boolean\n  disableSort?: boolean\n  headerStyle?: React.CSSProperties\n  cellStyle?: React.CSSProperties\n  renderHeader?: () => React.ReactNode\n  renderCell?: (row: any) => React.ReactNode\n  getFilterValue?: (row: any) => string\n}\n\ninterface FilterableTableProps {\n  data: any[]\n  columns: ColumnDef[]\n  loading?: boolean\n  emptyMessage?: string\n  onFilteredDataChange?: (filteredData: any[]) => void\n  renderRow?: (row: any, index: number) => React.ReactNode\n  className?: string\n  pagination?: boolean\n  defaultRowsPerPage?: number\n}\n\ninterface SortConfig {\n  key: string | null\n  direction: 'asc' | 'desc' | null\n}\n\ninterface FilterConfig {\n  [key: string]: {\n    type: 'text' | 'select' | 'number'\n    value: string | string[]\n    operator?: 'contains' | 'equals' | 'gt' | 'lt' | 'gte' | 'lte'\n  }\n}\n\nexport default function FilterableTable({\n  data = [],\n  columns = [],\n  loading = false,\n  emptyMessage = 'Nessun dato disponibile',\n  onFilteredDataChange,\n  renderRow,\n  className,\n  pagination = true,\n  defaultRowsPerPage = 25\n}: FilterableTableProps) {\n  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: null })\n  const [filters, setFilters] = useState<FilterConfig>({})\n  const [openFilters, setOpenFilters] = useState<{ [key: string]: boolean }>({})\n  const [currentPage, setCurrentPage] = useState(0)\n  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage)\n\n  // Get unique values for select filters\n  const getUniqueValues = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.getFilterValue) {\n      // Use custom filter value function if available\n      return [...new Set(data.map(item => column.getFilterValue!(item)).filter(Boolean))].sort()\n    }\n    // Default behavior: use field value directly\n    return [...new Set(data.map(item => item[field]).filter(Boolean))].sort()\n  }\n\n  // Apply filters and sorting\n  const filteredAndSortedData = useMemo(() => {\n    let filtered = [...data]\n\n    // Apply filters\n    Object.entries(filters).forEach(([field, filterConfig]) => {\n      if (!filterConfig.value || \n          (Array.isArray(filterConfig.value) && filterConfig.value.length === 0) ||\n          (typeof filterConfig.value === 'string' && filterConfig.value.trim() === '')) {\n        return\n      }\n\n      filtered = filtered.filter(item => {\n        const column = columns.find(col => col.field === field)\n        const itemValue = column?.getFilterValue ? column.getFilterValue(item) : item[field]\n\n        if (filterConfig.type === 'select') {\n          const selectedValues = Array.isArray(filterConfig.value) ? filterConfig.value : [filterConfig.value]\n          return selectedValues.includes(itemValue)\n        }\n\n        if (filterConfig.type === 'text') {\n          const searchValue = (filterConfig.value as string).toLowerCase()\n          const cellValue = String(itemValue || '').toLowerCase()\n\n          if (filterConfig.operator === 'equals') {\n            return cellValue === searchValue\n          }\n          return cellValue.includes(searchValue)\n        }\n        \n        if (filterConfig.type === 'number') {\n          const numValue = parseFloat(itemValue)\n          const filterValue = parseFloat(filterConfig.value as string)\n          \n          if (isNaN(numValue) || isNaN(filterValue)) return false\n          \n          switch (filterConfig.operator) {\n            case 'equals': return numValue === filterValue\n            case 'gt': return numValue > filterValue\n            case 'lt': return numValue < filterValue\n            case 'gte': return numValue >= filterValue\n            case 'lte': return numValue <= filterValue\n            default: return numValue === filterValue\n          }\n        }\n        \n        return true\n      })\n    })\n\n    // Apply sorting\n    if (sortConfig.key && sortConfig.direction) {\n      filtered.sort((a, b) => {\n        const aValue = a[sortConfig.key!]\n        const bValue = b[sortConfig.key!]\n        \n        // Handle null/undefined values\n        if (aValue == null && bValue == null) return 0\n        if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1\n        if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1\n        \n        // Determine if values are numbers\n        const aNum = parseFloat(aValue)\n        const bNum = parseFloat(bValue)\n        const isNumeric = !isNaN(aNum) && !isNaN(bNum)\n        \n        let comparison = 0\n        if (isNumeric) {\n          comparison = aNum - bNum\n        } else {\n          comparison = String(aValue).localeCompare(String(bValue))\n        }\n        \n        return sortConfig.direction === 'asc' ? comparison : -comparison\n      })\n    }\n\n    return filtered\n  }, [data, filters, sortConfig])\n\n  // Calculate paginated data\n  const paginatedData = useMemo(() => {\n    if (!pagination) return filteredAndSortedData\n\n    const startIndex = currentPage * rowsPerPage\n    const endIndex = startIndex + rowsPerPage\n    return filteredAndSortedData.slice(startIndex, endIndex)\n  }, [filteredAndSortedData, currentPage, rowsPerPage, pagination])\n\n  // Reset page when filters change\n  useEffect(() => {\n    setCurrentPage(0)\n  }, [filters])\n\n  // Calculate pagination info\n  const totalPages = Math.ceil(filteredAndSortedData.length / rowsPerPage)\n  const startRow = currentPage * rowsPerPage + 1\n  const endRow = Math.min((currentPage + 1) * rowsPerPage, filteredAndSortedData.length)\n\n  // Notify parent of filtered data changes\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredAndSortedData)\n    }\n  }, [filteredAndSortedData]) // Rimuoviamo onFilteredDataChange dalle dipendenze per evitare loop infiniti\n\n  const handleSort = (field: string) => {\n    const column = columns.find(col => col.field === field)\n    if (column?.disableSort) return\n\n    setSortConfig(prev => {\n      if (prev.key === field) {\n        if (prev.direction === 'asc') return { key: field, direction: 'desc' }\n        if (prev.direction === 'desc') return { key: null, direction: null }\n      }\n      return { key: field, direction: 'asc' }\n    })\n  }\n\n  const updateFilter = (field: string, filterConfig: Partial<FilterConfig[string]>) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: { ...prev[field], ...filterConfig }\n    }))\n  }\n\n  const clearFilter = (field: string) => {\n    setFilters(prev => {\n      const newFilters = { ...prev }\n      delete newFilters[field]\n      return newFilters\n    })\n  }\n\n  const clearAllFilters = () => {\n    setFilters({})\n  }\n\n  const getSortIcon = (field: string) => {\n    if (sortConfig.key !== field) return <ArrowUpDown className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'asc') return <ArrowUp className=\"h-3 w-3\" />\n    if (sortConfig.direction === 'desc') return <ArrowDown className=\"h-3 w-3\" />\n    return <ArrowUpDown className=\"h-3 w-3\" />\n  }\n\n  const hasActiveFilters = Object.keys(filters).length > 0\n\n  if (loading) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center\">Caricamento...</div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className={className}>\n      {/* Active filters display */}\n      {hasActiveFilters && (\n        <div className=\"mb-4 flex flex-wrap gap-2 items-center\">\n          <span className=\"text-sm text-muted-foreground\">Filtri attivi:</span>\n          {Object.entries(filters).map(([field, filterConfig]) => {\n            const column = columns.find(col => col.field === field)\n            if (!column) return null\n            \n            const displayValue = Array.isArray(filterConfig.value) \n              ? filterConfig.value.join(', ')\n              : String(filterConfig.value)\n            \n            return (\n              <Badge key={field} variant=\"secondary\" className=\"gap-1\">\n                {column.headerName}: {displayValue}\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-auto p-0 hover:bg-transparent\"\n                  onClick={() => clearFilter(field)}\n                >\n                  <X className=\"h-3 w-3\" />\n                </Button>\n              </Badge>\n            )\n          })}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={clearAllFilters}\n            className=\"h-6 px-2 text-xs\"\n          >\n            Pulisci tutti\n          </Button>\n        </div>\n      )}\n\n      {/* Table */}\n      <Card>\n        <CardContent className=\"p-0\">\n          <Table>\n            <TableHeader>\n              <TableRow className=\"bg-mariner-50 hover:bg-mariner-50\">\n                {columns.map((column) => (\n                  <TableHead\n                    key={column.field}\n                    className={cn(\n                      \"font-semibold text-mariner-900 border-b border-mariner-200\",\n                      column.align === 'center' && \"text-center\",\n                      column.align === 'right' && \"text-right\"\n                    )}\n                    style={{ width: column.width, ...column.headerStyle }}\n                  >\n                    {column.renderHeader ? (\n                      column.renderHeader()\n                    ) : (\n                      <div className=\"relative group\">\n                        <div className=\"flex items-center justify-between w-full\">\n                          <span className=\"truncate\">{column.headerName}</span>\n\n                          {/* Compact icons container - only visible on hover */}\n                          <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                            {/* Sort button */}\n                            {!column.disableSort && (\n                              <Button\n                                variant=\"ghost\"\n                                size=\"sm\"\n                                className=\"h-4 w-4 p-0 hover:bg-mariner-100\"\n                                onClick={() => handleSort(column.field)}\n                              >\n                                {getSortIcon(column.field)}\n                              </Button>\n                            )}\n\n                            {/* Filter button */}\n                            {!column.disableFilter && (\n                              <Popover\n                                open={openFilters[column.field]}\n                                onOpenChange={(open) => setOpenFilters(prev => ({ ...prev, [column.field]: open }))}\n                              >\n                                <PopoverTrigger asChild>\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    className={cn(\n                                      \"h-4 w-4 p-0 hover:bg-mariner-100\",\n                                      filters[column.field] && \"text-mariner-600 opacity-100\"\n                                    )}\n                                  >\n                                    <Filter className=\"h-2.5 w-2.5\" />\n                                  </Button>\n                                </PopoverTrigger>\n                                <PopoverContent className=\"w-64\" align=\"start\">\n                                  <FilterContent\n                                    column={column}\n                                    data={data}\n                                    currentFilter={filters[column.field]}\n                                    onFilterChange={(filterConfig) => updateFilter(column.field, filterConfig)}\n                                    onClearFilter={() => clearFilter(column.field)}\n                                    getUniqueValues={() => getUniqueValues(column.field)}\n                                  />\n                                </PopoverContent>\n                              </Popover>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Active filter indicator */}\n                        {filters[column.field] && (\n                          <div className=\"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full\"></div>\n                        )}\n                      </div>\n                    )}\n                  </TableHead>\n                ))}\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {paginatedData.length > 0 ? (\n                paginatedData.map((row, index) => (\n                  renderRow ? (\n                    renderRow(row, currentPage * rowsPerPage + index)\n                  ) : (\n                    <TableRow\n                      key={index}\n                      className=\"hover:bg-mariner-50 border-b border-mariner-100\"\n                    >\n                      {columns.map((column) => (\n                        <TableCell\n                          key={column.field}\n                          className={cn(\n                            \"py-2 px-4\",\n                            column.align === 'center' && \"text-center\",\n                            column.align === 'right' && \"text-right\"\n                          )}\n                          style={column.cellStyle}\n                        >\n                          {column.renderCell ? column.renderCell(row) : row[column.field]}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  )\n                ))\n              ) : (\n                <TableRow>\n                  <TableCell colSpan={columns.length} className=\"text-center py-8 text-muted-foreground\">\n                    {emptyMessage}\n                  </TableCell>\n                </TableRow>\n              )}\n            </TableBody>\n          </Table>\n        </CardContent>\n      </Card>\n\n      {/* Pagination Controls */}\n      {pagination && filteredAndSortedData.length > 0 && (\n        <div className=\"flex items-center justify-between mt-4\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              Righe per pagina:\n            </span>\n            <Select\n              value={rowsPerPage.toString()}\n              onValueChange={(value) => {\n                setRowsPerPage(Number(value))\n                setCurrentPage(0)\n              }}\n            >\n              <SelectTrigger className=\"w-20\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"10\">10</SelectItem>\n                <SelectItem value=\"25\">25</SelectItem>\n                <SelectItem value=\"50\">50</SelectItem>\n                <SelectItem value=\"100\">100</SelectItem>\n                <SelectItem value={filteredAndSortedData.length.toString()}>Tutto</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-sm text-muted-foreground\">\n              {filteredAndSortedData.length > 0 ? `${startRow}-${endRow} di ${filteredAndSortedData.length}` : '0 di 0'}\n            </span>\n\n            <div className=\"flex items-center space-x-1\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(0)}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}\n                disabled={currentPage === 0}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setCurrentPage(totalPages - 1)}\n                disabled={currentPage >= totalPages - 1}\n                className=\"h-8 w-8 p-0\"\n              >\n                <ChevronsRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Filter content component\ninterface FilterContentProps {\n  column: ColumnDef\n  data: any[]\n  currentFilter?: FilterConfig[string]\n  onFilterChange: (filterConfig: Partial<FilterConfig[string]>) => void\n  onClearFilter: () => void\n  getUniqueValues: () => any[]\n}\n\nfunction FilterContent({\n  column,\n  currentFilter,\n  onFilterChange,\n  onClearFilter,\n  getUniqueValues\n}: FilterContentProps) {\n  const [localValue, setLocalValue] = useState(currentFilter?.value || '')\n  const [operator, setOperator] = useState(currentFilter?.operator || 'contains')\n\n  const uniqueValues = getUniqueValues()\n  const isSelectType = column.dataType !== 'number' && uniqueValues.length <= 20\n  const isNumberType = column.dataType === 'number'\n\n  const applyFilter = () => {\n    if (isSelectType) {\n      onFilterChange({\n        type: 'select',\n        value: Array.isArray(localValue) ? localValue : [localValue]\n      })\n    } else if (isNumberType) {\n      onFilterChange({\n        type: 'number',\n        value: localValue as string,\n        operator\n      })\n    } else {\n      onFilterChange({\n        type: 'text',\n        value: localValue as string,\n        operator\n      })\n    }\n  }\n\n  return (\n    <div className=\"space-y-3\">\n      <div className=\"font-medium text-sm\">Filtra {column.headerName}</div>\n      \n      {isSelectType ? (\n        <div className=\"space-y-2 max-h-48 overflow-y-auto\">\n          {uniqueValues.map(value => (\n            <div key={value} className=\"flex items-center space-x-2\">\n              <Checkbox\n                id={`filter-${value}`}\n                checked={Array.isArray(localValue) ? localValue.includes(value) : localValue === value}\n                onCheckedChange={(checked) => {\n                  if (Array.isArray(localValue)) {\n                    setLocalValue(checked \n                      ? [...localValue, value]\n                      : localValue.filter(v => v !== value)\n                    )\n                  } else {\n                    setLocalValue(checked ? [value] : [])\n                  }\n                }}\n              />\n              <label htmlFor={`filter-${value}`} className=\"text-sm\">\n                {value}\n              </label>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n                <SelectItem value=\"gt\">Maggiore di</SelectItem>\n                <SelectItem value=\"lt\">Minore di</SelectItem>\n                <SelectItem value=\"gte\">Maggiore o uguale</SelectItem>\n                <SelectItem value=\"lte\">Minore o uguale</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          {!isNumberType && (\n            <Select value={operator} onValueChange={setOperator}>\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          )}\n          \n          <Input\n            placeholder={`Cerca ${column.headerName.toLowerCase()}...`}\n            value={localValue as string}\n            onChange={(e) => setLocalValue(e.target.value)}\n            onKeyDown={(e) => e.key === 'Enter' && applyFilter()}\n          />\n        </div>\n      )}\n      \n      <div className=\"flex gap-2\">\n        <Button size=\"sm\" onClick={applyFilter}>\n          Applica\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onClearFilter}>\n          Pulisci\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAzCA;;;;;;;;;;;;;AAmFe,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,UAAU,EAAE,EACZ,UAAU,KAAK,EACf,eAAe,yBAAyB,EACxC,oBAAoB,EACpB,SAAS,EACT,SAAS,EACT,aAAa,IAAI,EACjB,qBAAqB,EAAE,EACF;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,KAAK;QAAM,WAAW;IAAK;IACtF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,gBAAgB;YAC1B,gDAAgD;YAChD,OAAO;mBAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,OAAO,cAAc,CAAE,OAAO,MAAM,CAAC;aAAU,CAAC,IAAI;QAC1F;QACA,6CAA6C;QAC7C,OAAO;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;SAAU,CAAC,IAAI;IACzE;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,WAAW;eAAI;SAAK;QAExB,gBAAgB;QAChB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,OAAO,aAAa;YACpD,IAAI,CAAC,aAAa,KAAK,IAClB,MAAM,OAAO,CAAC,aAAa,KAAK,KAAK,aAAa,KAAK,CAAC,MAAM,KAAK,KACnE,OAAO,aAAa,KAAK,KAAK,YAAY,aAAa,KAAK,CAAC,IAAI,OAAO,IAAK;gBAChF;YACF;YAEA,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;gBACjD,MAAM,YAAY,QAAQ,iBAAiB,OAAO,cAAc,CAAC,QAAQ,IAAI,CAAC,MAAM;gBAEpF,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,KAAK,IAAI,aAAa,KAAK,GAAG;wBAAC,aAAa,KAAK;qBAAC;oBACpG,OAAO,eAAe,QAAQ,CAAC;gBACjC;gBAEA,IAAI,aAAa,IAAI,KAAK,QAAQ;oBAChC,MAAM,cAAc,AAAC,aAAa,KAAK,CAAY,WAAW;oBAC9D,MAAM,YAAY,OAAO,aAAa,IAAI,WAAW;oBAErD,IAAI,aAAa,QAAQ,KAAK,UAAU;wBACtC,OAAO,cAAc;oBACvB;oBACA,OAAO,UAAU,QAAQ,CAAC;gBAC5B;gBAEA,IAAI,aAAa,IAAI,KAAK,UAAU;oBAClC,MAAM,WAAW,WAAW;oBAC5B,MAAM,cAAc,WAAW,aAAa,KAAK;oBAEjD,IAAI,MAAM,aAAa,MAAM,cAAc,OAAO;oBAElD,OAAQ,aAAa,QAAQ;wBAC3B,KAAK;4BAAU,OAAO,aAAa;wBACnC,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAM,OAAO,WAAW;wBAC7B,KAAK;4BAAO,OAAO,YAAY;wBAC/B,KAAK;4BAAO,OAAO,YAAY;wBAC/B;4BAAS,OAAO,aAAa;oBAC/B;gBACF;gBAEA,OAAO;YACT;QACF;QAEA,gBAAgB;QAChB,IAAI,WAAW,GAAG,IAAI,WAAW,SAAS,EAAE;YAC1C,SAAS,IAAI,CAAC,CAAC,GAAG;gBAChB,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBACjC,MAAM,SAAS,CAAC,CAAC,WAAW,GAAG,CAAE;gBAEjC,+BAA+B;gBAC/B,IAAI,UAAU,QAAQ,UAAU,MAAM,OAAO;gBAC7C,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,CAAC,IAAI;gBACjE,IAAI,UAAU,MAAM,OAAO,WAAW,SAAS,KAAK,QAAQ,IAAI,CAAC;gBAEjE,kCAAkC;gBAClC,MAAM,OAAO,WAAW;gBACxB,MAAM,OAAO,WAAW;gBACxB,MAAM,YAAY,CAAC,MAAM,SAAS,CAAC,MAAM;gBAEzC,IAAI,aAAa;gBACjB,IAAI,WAAW;oBACb,aAAa,OAAO;gBACtB,OAAO;oBACL,aAAa,OAAO,QAAQ,aAAa,CAAC,OAAO;gBACnD;gBAEA,OAAO,WAAW,SAAS,KAAK,QAAQ,aAAa,CAAC;YACxD;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAM;QAAS;KAAW;IAE9B,2BAA2B;IAC3B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,aAAa,cAAc;QACjC,MAAM,WAAW,aAAa;QAC9B,OAAO,sBAAsB,KAAK,CAAC,YAAY;IACjD,GAAG;QAAC;QAAuB;QAAa;QAAa;KAAW;IAEhE,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC;KAAQ;IAEZ,4BAA4B;IAC5B,MAAM,aAAa,KAAK,IAAI,CAAC,sBAAsB,MAAM,GAAG;IAC5D,MAAM,WAAW,cAAc,cAAc;IAC7C,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,aAAa,sBAAsB,MAAM;IAErF,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,sBAAsB;YACxB,qBAAqB;QACvB;IACF,GAAG;QAAC;KAAsB,EAAE,6EAA6E;;IAEzG,MAAM,aAAa,CAAC;QAClB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;QACjD,IAAI,QAAQ,aAAa;QAEzB,cAAc,CAAA;YACZ,IAAI,KAAK,GAAG,KAAK,OAAO;gBACtB,IAAI,KAAK,SAAS,KAAK,OAAO,OAAO;oBAAE,KAAK;oBAAO,WAAW;gBAAO;gBACrE,IAAI,KAAK,SAAS,KAAK,QAAQ,OAAO;oBAAE,KAAK;oBAAM,WAAW;gBAAK;YACrE;YACA,OAAO;gBAAE,KAAK;gBAAO,WAAW;YAAM;QACxC;IACF;IAEA,MAAM,eAAe,CAAC,OAAe;QACnC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;oBAAE,GAAG,IAAI,CAAC,MAAM;oBAAE,GAAG,YAAY;gBAAC;YAC7C,CAAC;IACH;IAEA,MAAM,cAAc,CAAC;QACnB,WAAW,CAAA;YACT,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,MAAM;YACxB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,GAAG,KAAK,OAAO,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC5D,IAAI,WAAW,SAAS,KAAK,OAAO,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9D,IAAI,WAAW,SAAS,KAAK,QAAQ,qBAAO,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QACjE,qBAAO,8OAAC,wNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;IAEvD,IAAI,SAAS;QACX,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BAAc;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAW;;YAEb,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;oBAC/C,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,OAAO,aAAa;wBACjD,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;wBACjD,IAAI,CAAC,QAAQ,OAAO;wBAEpB,MAAM,eAAe,MAAM,OAAO,CAAC,aAAa,KAAK,IACjD,aAAa,KAAK,CAAC,IAAI,CAAC,QACxB,OAAO,aAAa,KAAK;wBAE7B,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAa,SAAQ;4BAAY,WAAU;;gCAC9C,OAAO,UAAU;gCAAC;gCAAG;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,YAAY;8CAE3B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;kCACA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0CACJ,8OAAC,iIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4CAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;4CAE9B,OAAO;gDAAE,OAAO,OAAO,KAAK;gDAAE,GAAG,OAAO,WAAW;4CAAC;sDAEnD,OAAO,YAAY,GAClB,OAAO,YAAY,mBAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAY,OAAO,UAAU;;;;;;0EAG7C,8OAAC;gEAAI,WAAU;;oEAEZ,CAAC,OAAO,WAAW,kBAClB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,WAAW,OAAO,KAAK;kFAErC,YAAY,OAAO,KAAK;;;;;;oEAK5B,CAAC,OAAO,aAAa,kBACpB,8OAAC,mIAAA,CAAA,UAAO;wEACN,MAAM,WAAW,CAAC,OAAO,KAAK,CAAC;wEAC/B,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,CAAC,OAAO,KAAK,CAAC,EAAE;gFAAK,CAAC;;0FAEjF,8OAAC,mIAAA,CAAA,iBAAc;gFAAC,OAAO;0FACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,OAAO,CAAC,OAAO,KAAK,CAAC,IAAI;8FAG3B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGtB,8OAAC,mIAAA,CAAA,iBAAc;gFAAC,WAAU;gFAAO,OAAM;0FACrC,cAAA,8OAAC;oFACC,QAAQ;oFACR,MAAM;oFACN,eAAe,OAAO,CAAC,OAAO,KAAK,CAAC;oFACpC,gBAAgB,CAAC,eAAiB,aAAa,OAAO,KAAK,EAAE;oFAC7D,eAAe,IAAM,YAAY,OAAO,KAAK;oFAC7C,iBAAiB,IAAM,gBAAgB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAS9D,OAAO,CAAC,OAAO,KAAK,CAAC,kBACpB,8OAAC;wDAAI,WAAU;;;;;;;;;;;;2CAhEhB,OAAO,KAAK;;;;;;;;;;;;;;;0CAwEzB,8OAAC,iIAAA,CAAA,YAAS;0CACP,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,KAAK,QACtB,YACE,UAAU,KAAK,cAAc,cAAc,uBAE3C,8OAAC,iIAAA,CAAA,WAAQ;wCAEP,WAAU;kDAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;gDAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;gDAE9B,OAAO,OAAO,SAAS;0DAEtB,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAO,GAAG,CAAC,OAAO,KAAK,CAAC;+CAR1D,OAAO,KAAK;;;;;uCALhB;;;;8DAoBX,8OAAC,iIAAA,CAAA,WAAQ;8CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wCAAC,SAAS,QAAQ,MAAM;wCAAE,WAAU;kDAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUd,cAAc,sBAAsB,MAAM,GAAG,mBAC5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAGhD,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,YAAY,QAAQ;gCAC3B,eAAe,CAAC;oCACd,eAAe,OAAO;oCACtB,eAAe;gCACjB;;kDAEA,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAK;;;;;;0DACvB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAO,sBAAsB,MAAM,CAAC,QAAQ;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CACb,sBAAsB,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,IAAI,EAAE,sBAAsB,MAAM,EAAE,GAAG;;;;;;0CAGnG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;wCACzD,UAAU,gBAAgB;wCAC1B,WAAU;kDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO;wCACtE,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe,aAAa;wCAC3C,UAAU,eAAe,aAAa;wCACtC,WAAU;kDAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;AAYA,SAAS,cAAc,EACrB,MAAM,EACN,aAAa,EACb,cAAc,EACd,aAAa,EACb,eAAe,EACI;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,SAAS;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY;IAEpE,MAAM,eAAe;IACrB,MAAM,eAAe,OAAO,QAAQ,KAAK,YAAY,aAAa,MAAM,IAAI;IAC5E,MAAM,eAAe,OAAO,QAAQ,KAAK;IAEzC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,eAAe;gBACb,MAAM;gBACN,OAAO,MAAM,OAAO,CAAC,cAAc,aAAa;oBAAC;iBAAW;YAC9D;QACF,OAAO,IAAI,cAAc;YACvB,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF,OAAO;YACL,eAAe;gBACb,MAAM;gBACN,OAAO;gBACP;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAAsB;oBAAQ,OAAO,UAAU;;;;;;;YAE7D,6BACC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,oIAAA,CAAA,WAAQ;gCACP,IAAI,CAAC,OAAO,EAAE,OAAO;gCACrB,SAAS,MAAM,OAAO,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS,eAAe;gCACjF,iBAAiB,CAAC;oCAChB,IAAI,MAAM,OAAO,CAAC,aAAa;wCAC7B,cAAc,UACV;+CAAI;4CAAY;yCAAM,GACtB,WAAW,MAAM,CAAC,CAAA,IAAK,MAAM;oCAEnC,OAAO;wCACL,cAAc,UAAU;4CAAC;yCAAM,GAAG,EAAE;oCACtC;gCACF;;;;;;0CAEF,8OAAC;gCAAM,SAAS,CAAC,OAAO,EAAE,OAAO;gCAAE,WAAU;0CAC1C;;;;;;;uBAhBK;;;;;;;;;qCAsBd,8OAAC;gBAAI,WAAU;;oBACZ,8BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;kDAC3B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;;;;;;;;;;;;;oBAK7B,CAAC,8BACA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe;;0CACtC,8OAAC,kIAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAW;;;;;;kDAC7B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAKjC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,aAAa,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;wBAC1D,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;0BAK7C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS;kCAAa;;;;;;kCAGxC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;kCAAe;;;;;;;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/SmartCaviFilter.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Card, CardContent } from '@/components/ui/card'\nimport {\n  Search,\n  X,\n  CheckSquare,\n  Square,\n  Filter,\n  Settings,\n  SquareCheck,\n  SquareMinus\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\ninterface SmartCaviFilterProps {\n  cavi: Cavo[]\n  onFilteredDataChange?: (filteredCavi: Cavo[]) => void\n  loading?: boolean\n  selectionEnabled?: boolean\n  onSelectionToggle?: () => void\n  selectedCount?: number\n  totalCount?: number\n}\n\nexport default function SmartCaviFilter({\n  cavi = [],\n  onFilteredDataChange,\n  loading = false,\n  selectionEnabled = false,\n  onSelectionToggle,\n  selectedCount = 0,\n  totalCount = 0\n}: SmartCaviFilterProps) {\n  const [searchText, setSearchText] = useState('')\n  const [searchType, setSearchType] = useState<'contains' | 'equals'>('contains')\n\n  // Normalize string for search\n  const normalizeString = (str: string | null | undefined): string => {\n    if (!str) return ''\n    return str.toString().toLowerCase().trim()\n  }\n\n  // Extract cable info for advanced search\n  const getCavoInfo = (idCavo: string) => {\n    const match = idCavo.match(/^([A-Z]+)(\\d+)([A-Z]*)$/)\n    if (match) {\n      return {\n        prefix: match[1],\n        number: match[2],\n        suffix: match[3] || ''\n      }\n    }\n    return { prefix: '', number: idCavo, suffix: '' }\n  }\n\n  // Check if a cable matches a search term\n  const cavoMatchesTerm = useCallback((cavo: Cavo, term: string, exactMatch: boolean): boolean => {\n    const normalizedTerm = normalizeString(term)\n    \n    if (!normalizedTerm) return true\n\n    // Basic cable info\n    const cavoId = normalizeString(cavo.id_cavo)\n    const { prefix: cavoPrefix, number: cavoNumber, suffix: cavoSuffix } = getCavoInfo(cavo.id_cavo || '')\n    \n    // Cable properties\n    const tipologia = normalizeString(cavo.tipologia)\n    const formazione = normalizeString(cavo.formazione || cavo.sezione)\n    const utility = normalizeString(cavo.utility)\n    const sistema = normalizeString(cavo.sistema)\n    \n    // Locations\n    const ubicazionePartenza = normalizeString(cavo.da || cavo.ubicazione_partenza)\n    const ubicazioneArrivo = normalizeString(cavo.a || cavo.ubicazione_arrivo)\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza)\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo)\n    \n    // Reel info\n    const bobina = normalizeString(cavo.id_bobina)\n    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :\n                         cavo.id_bobina === null ? '' :\n                         normalizeString(cavo.id_bobina)\n\n    // All text fields to search\n    const textFields = [\n      cavoId, cavoPrefix, cavoNumber, cavoSuffix, tipologia, formazione, utility, sistema,\n      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,\n      bobina, bobinaDisplay\n    ]\n\n    // Numeric fields for range search\n    const numericFields = [\n      { value: cavo.metri_teorici, name: 'metri_teorici' },\n      { value: cavo.metratura_reale || cavo.metri_posati, name: 'metratura_reale' },\n      { value: parseFloat(formazione), name: 'formazione' }\n    ]\n\n    // Check for range queries (e.g., \">100\", \"<=50\")\n    const rangeMatch = normalizedTerm.match(/^([><=]+)(\\d+(?:\\.\\d+)?)$/)\n    if (rangeMatch) {\n      const operator = rangeMatch[1]\n      const value = parseFloat(rangeMatch[2])\n      \n      return numericFields.some(field => {\n        if (field.value == null || isNaN(field.value)) return false\n        \n        switch (operator) {\n          case '>': return field.value > value\n          case '>=': return field.value >= value\n          case '<': return field.value < value\n          case '<=': return field.value <= value\n          case '=': return field.value === value\n          default: return false\n        }\n      })\n    }\n\n    // Check for exact numeric match\n    const numericTerm = parseFloat(normalizedTerm)\n    if (!isNaN(numericTerm)) {\n      const numericMatch = numericFields.some(field => \n        field.value != null && !isNaN(field.value) && field.value === numericTerm\n      )\n      if (numericMatch) return true\n    }\n\n    // Text search\n    if (exactMatch) {\n      return textFields.some(field => field === normalizedTerm)\n    } else {\n      return textFields.some(field => field.includes(normalizedTerm))\n    }\n  }, [])\n\n  // Apply filter\n  const applyFilter = useCallback(() => {\n    if (!searchText.trim()) {\n      onFilteredDataChange?.(cavi)\n      return\n    }\n\n    // Split search terms by comma\n    const searchTerms = searchText.split(',')\n      .map(term => term.trim())\n      .filter(term => term.length > 0)\n\n    let filtered: Cavo[] = []\n\n    if (searchType === 'equals') {\n      if (searchTerms.length === 1) {\n        // Single term: exact search\n        filtered = cavi.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true))\n      } else {\n        // Multiple terms: all must match (AND)\n        filtered = cavi.filter(cavo =>\n          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))\n        )\n      }\n    } else {\n      // Contains search: at least one term must match (OR)\n      filtered = cavi.filter(cavo =>\n        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))\n      )\n    }\n\n    onFilteredDataChange?.(filtered)\n  }, [searchText, searchType, cavi, cavoMatchesTerm]) // Rimuoviamo onFilteredDataChange dalle dipendenze\n\n  // Apply filter when dependencies change\n  useEffect(() => {\n    applyFilter()\n  }, [searchText, searchType, cavi, cavoMatchesTerm]) // Usiamo le dipendenze dirette invece di applyFilter\n\n  const handleSearchTextChange = (value: string) => {\n    setSearchText(value)\n  }\n\n  const clearFilter = () => {\n    setSearchText('')\n    setSearchType('contains')\n  }\n\n  return (\n    <Card className=\"mb-1\">\n      <CardContent className=\"p-1\">\n        <div className=\"flex items-center gap-1\">\n          {/* Search input - takes most space */}\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Cerca per ID, sistema, utility, tipologia, ubicazione...\"\n              value={searchText}\n              onChange={(e) => handleSearchTextChange(e.target.value)}\n              disabled={loading}\n              className=\"pl-10 pr-10 h-8\"\n              aria-label=\"Campo di ricerca intelligente per cavi\"\n            />\n            {searchText && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0\"\n                onClick={clearFilter}\n              >\n                <X className=\"h-2.5 w-2.5\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Search type selector */}\n          <div className=\"w-32\">\n            <Select value={searchType} onValueChange={(value: 'contains' | 'equals') => setSearchType(value)}>\n              <SelectTrigger className=\"h-8\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"contains\">Contiene</SelectItem>\n                <SelectItem value=\"equals\">Uguale a</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Advanced filters button - removed as per user request */}\n\n          {/* Clear button */}\n          {searchText && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={clearFilter}\n              disabled={loading}\n              className=\"transition-all duration-200 hover:scale-105\"\n              aria-label=\"Pulisci ricerca\"\n            >\n              <X className=\"h-4 w-4 mr-1\" />\n              Pulisci\n            </Button>\n          )}\n\n          {/* Selection toggle button - only show when there are items to select */}\n          {onSelectionToggle && totalCount > 0 && (\n            <Button\n              variant={selectionEnabled ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={onSelectionToggle}\n              className=\"flex items-center gap-2 transition-all duration-200 hover:scale-105\"\n              aria-label={selectionEnabled ? 'Disabilita modalità selezione' : 'Abilita modalità selezione'}\n            >\n              {selectionEnabled ? <SquareCheck className=\"h-4 w-4\" /> : <Square className=\"h-4 w-4\" />}\n              {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}\n            </Button>\n          )}\n\n          {/* Deselect all button - only show when selection is enabled and items are selected */}\n          {selectionEnabled && selectedCount > 0 && (\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                // This would need to be passed as a prop or handled by parent\n                // For now, we'll just show the button\n              }}\n              className=\"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50\"\n              aria-label={`Deseleziona tutti i ${selectedCount} cavi selezionati`}\n            >\n              <SquareMinus className=\"h-4 w-4\" />\n              Deseleziona Tutto ({selectedCount})\n            </Button>\n          )}\n        </div>\n\n        {/* Search help text - ultra compact */}\n        {searchText && (\n          <div className=\"mt-0.5 text-xs text-muted-foreground\">\n            <div className=\"flex flex-wrap gap-1\">\n              <span>💡</span>\n              <span>• Virgole per multipli</span>\n              <span>• &gt;100, &lt;=50 per numeri</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;AAoCe,SAAS,gBAAgB,EACtC,OAAO,EAAE,EACT,oBAAoB,EACpB,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,iBAAiB,EACjB,gBAAgB,CAAC,EACjB,aAAa,CAAC,EACO;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAEpE,8BAA8B;IAC9B,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,KAAK,OAAO;QACjB,OAAO,IAAI,QAAQ,GAAG,WAAW,GAAG,IAAI;IAC1C;IAEA,yCAAyC;IACzC,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,OAAO,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,OAAO;gBACL,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE;gBAChB,QAAQ,KAAK,CAAC,EAAE,IAAI;YACtB;QACF;QACA,OAAO;YAAE,QAAQ;YAAI,QAAQ;YAAQ,QAAQ;QAAG;IAClD;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAY,MAAc;QAC7D,MAAM,iBAAiB,gBAAgB;QAEvC,IAAI,CAAC,gBAAgB,OAAO;QAE5B,mBAAmB;QACnB,MAAM,SAAS,gBAAgB,KAAK,OAAO;QAC3C,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,GAAG,YAAY,KAAK,OAAO,IAAI;QAEnG,mBAAmB;QACnB,MAAM,YAAY,gBAAgB,KAAK,SAAS;QAChD,MAAM,aAAa,gBAAgB,KAAK,UAAU,IAAI,KAAK,OAAO;QAClE,MAAM,UAAU,gBAAgB,KAAK,OAAO;QAC5C,MAAM,UAAU,gBAAgB,KAAK,OAAO;QAE5C,YAAY;QACZ,MAAM,qBAAqB,gBAAgB,KAAK,EAAE,IAAI,KAAK,mBAAmB;QAC9E,MAAM,mBAAmB,gBAAgB,KAAK,CAAC,IAAI,KAAK,iBAAiB;QACzE,MAAM,iBAAiB,gBAAgB,KAAK,eAAe;QAC3D,MAAM,eAAe,gBAAgB,KAAK,aAAa;QAEvD,YAAY;QACZ,MAAM,SAAS,gBAAgB,KAAK,SAAS;QAC7C,MAAM,gBAAgB,KAAK,SAAS,KAAK,iBAAiB,iBACrC,KAAK,SAAS,KAAK,OAAO,KAC1B,gBAAgB,KAAK,SAAS;QAEnD,4BAA4B;QAC5B,MAAM,aAAa;YACjB;YAAQ;YAAY;YAAY;YAAY;YAAW;YAAY;YAAS;YAC5E;YAAoB;YAAkB;YAAgB;YACtD;YAAQ;SACT;QAED,kCAAkC;QAClC,MAAM,gBAAgB;YACpB;gBAAE,OAAO,KAAK,aAAa;gBAAE,MAAM;YAAgB;YACnD;gBAAE,OAAO,KAAK,eAAe,IAAI,KAAK,YAAY;gBAAE,MAAM;YAAkB;YAC5E;gBAAE,OAAO,WAAW;gBAAa,MAAM;YAAa;SACrD;QAED,iDAAiD;QACjD,MAAM,aAAa,eAAe,KAAK,CAAC;QACxC,IAAI,YAAY;YACd,MAAM,WAAW,UAAU,CAAC,EAAE;YAC9B,MAAM,QAAQ,WAAW,UAAU,CAAC,EAAE;YAEtC,OAAO,cAAc,IAAI,CAAC,CAAA;gBACxB,IAAI,MAAM,KAAK,IAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO;gBAEtD,OAAQ;oBACN,KAAK;wBAAK,OAAO,MAAM,KAAK,GAAG;oBAC/B,KAAK;wBAAM,OAAO,MAAM,KAAK,IAAI;oBACjC,KAAK;wBAAK,OAAO,MAAM,KAAK,GAAG;oBAC/B,KAAK;wBAAM,OAAO,MAAM,KAAK,IAAI;oBACjC,KAAK;wBAAK,OAAO,MAAM,KAAK,KAAK;oBACjC;wBAAS,OAAO;gBAClB;YACF;QACF;QAEA,gCAAgC;QAChC,MAAM,cAAc,WAAW;QAC/B,IAAI,CAAC,MAAM,cAAc;YACvB,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,QACtC,MAAM,KAAK,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK;YAEhE,IAAI,cAAc,OAAO;QAC3B;QAEA,cAAc;QACd,IAAI,YAAY;YACd,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,UAAU;QAC5C,OAAO;YACL,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC;QACjD;IACF,GAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,uBAAuB;YACvB;QACF;QAEA,8BAA8B;QAC9B,MAAM,cAAc,WAAW,KAAK,CAAC,KAClC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;QAEhC,IAAI,WAAmB,EAAE;QAEzB,IAAI,eAAe,UAAU;YAC3B,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,4BAA4B;gBAC5B,WAAW,KAAK,MAAM,CAAC,CAAA,OAAQ,gBAAgB,MAAM,WAAW,CAAC,EAAE,EAAE;YACvE,OAAO;gBACL,uCAAuC;gBACvC,WAAW,KAAK,MAAM,CAAC,CAAA,OACrB,YAAY,KAAK,CAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;YAE1D;QACF,OAAO;YACL,qDAAqD;YACrD,WAAW,KAAK,MAAM,CAAC,CAAA,OACrB,YAAY,IAAI,CAAC,CAAA,OAAQ,gBAAgB,MAAM,MAAM;QAEzD;QAEA,uBAAuB;IACzB,GAAG;QAAC;QAAY;QAAY;QAAM;KAAgB,EAAE,mDAAmD;;IAEvG,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAY;QAAY;QAAM;KAAgB,EAAE,qDAAqD;;IAEzG,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,cAAc;QACd,cAAc;IAChB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;oCACtD,UAAU;oCACV,WAAU;oCACV,cAAW;;;;;;gCAEZ,4BACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAMnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAY,eAAe,CAAC,QAAiC,cAAc;;kDACxF,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAS;;;;;;;;;;;;;;;;;;;;;;;wBAQhC,4BACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;4BACV,cAAW;;8CAEX,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;wBAMjC,qBAAqB,aAAa,mBACjC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,mBAAmB,YAAY;4BACxC,MAAK;4BACL,SAAS;4BACT,WAAU;4BACV,cAAY,mBAAmB,kCAAkC;;gCAEhE,iCAAmB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAAe,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAC3E,mBAAmB,yBAAyB;;;;;;;wBAKhD,oBAAoB,gBAAgB,mBACnC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACP,8DAA8D;4BAC9D,sCAAsC;4BACxC;4BACA,WAAU;4BACV,cAAY,CAAC,oBAAoB,EAAE,cAAc,iBAAiB,CAAC;;8CAEnE,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;gCACf;gCAAc;;;;;;;;;;;;;gBAMvC,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 2603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/common/TruncatedText.tsx"], "sourcesContent": ["import React, { useState } from 'react'\n\ninterface TruncatedTextProps {\n  text: string\n  maxLength?: number\n  className?: string\n}\n\nexport default function TruncatedText({\n  text,\n  maxLength = 20,\n  className = \"\"\n}: TruncatedTextProps) {\n  const [showTooltip, setShowTooltip] = useState(false)\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })\n\n  if (!text) return <span className=\"text-gray-400\">-</span>\n\n  const shouldTruncate = text.length > maxLength\n  const displayText = shouldTruncate ? `${text.substring(0, maxLength)}...` : text\n\n  if (!shouldTruncate) {\n    return <span className={className}>{text}</span>\n  }\n\n  const handleMouseEnter = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n    setShowTooltip(true)\n  }\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    setMousePosition({ x: e.clientX, y: e.clientY })\n  }\n\n  return (\n    <div className=\"relative inline-block\">\n      <span\n        className={`cursor-help ${className}`}\n        style={{\n          textOverflow: 'ellipsis',\n          whiteSpace: 'nowrap',\n          overflow: 'hidden',\n          maxWidth: '100%',\n          display: 'inline-block'\n        }}\n        onMouseEnter={handleMouseEnter}\n        onMouseMove={handleMouseMove}\n        onMouseLeave={() => setShowTooltip(false)}\n        title={text} // Fallback browser tooltip\n      >\n        {displayText}\n      </span>\n\n      {/* Custom tooltip */}\n      {showTooltip && (\n        <div\n          className=\"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none\"\n          style={{\n            top: mousePosition.y - 40,\n            left: mousePosition.x - 150,\n            maxWidth: '300px',\n            wordWrap: 'break-word',\n            whiteSpace: 'normal'\n          }}\n        >\n          {text}\n          {/* Arrow */}\n          <div\n            className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0\"\n            style={{\n              borderLeft: '5px solid transparent',\n              borderRight: '5px solid transparent',\n              borderTop: '5px solid #1f2937'\n            }}\n          />\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAQe,SAAS,cAAc,EACpC,IAAI,EACJ,YAAY,EAAE,EACd,YAAY,EAAE,EACK;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,IAAI,CAAC,MAAM,qBAAO,8OAAC;QAAK,WAAU;kBAAgB;;;;;;IAElD,MAAM,iBAAiB,KAAK,MAAM,GAAG;IACrC,MAAM,cAAc,iBAAiB,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG;IAE5E,IAAI,CAAC,gBAAgB;QACnB,qBAAO,8OAAC;YAAK,WAAW;sBAAY;;;;;;IACtC;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;QAC9C,eAAe;IACjB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;YAAE,GAAG,EAAE,OAAO;YAAE,GAAG,EAAE,OAAO;QAAC;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC,YAAY,EAAE,WAAW;gBACrC,OAAO;oBACL,cAAc;oBACd,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,SAAS;gBACX;gBACA,cAAc;gBACd,aAAa;gBACb,cAAc,IAAM,eAAe;gBACnC,OAAO;0BAEN;;;;;;YAIF,6BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,KAAK,cAAc,CAAC,GAAG;oBACvB,MAAM,cAAc,CAAC,GAAG;oBACxB,UAAU;oBACV,UAAU;oBACV,YAAY;gBACd;;oBAEC;kCAED,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,aAAa;4BACb,WAAW;wBACb;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviTable.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useMemo, useEffect } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { getCavoColorClasses } from '@/utils/softColors'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { TableRow, TableCell } from '@/components/ui/table'\nimport { Cavo } from '@/types'\nimport FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'\nimport SmartCaviFilter from './SmartCaviFilter'\nimport TruncatedText from '@/components/common/TruncatedText'\nimport { ActionTooltip } from './tooltips/CableTooltips'\nimport {\n  MoreHorizontal,\n  Cable,\n  Settings,\n  Zap,\n  CheckCircle,\n  AlertCircle,\n  Clock,\n  Package,\n  Link,\n  Unlink,\n  Award,\n  Play,\n  Pause,\n  X,\n  Check,\n  FileText,\n  Download,\n  AlertTriangle,\n  Wrench,\n  ChevronDown,\n  Info\n} from 'lucide-react'\n\ninterface CaviTableProps {\n  cavi: Cavo[]\n  loading?: boolean\n  selectionEnabled?: boolean\n  selectedCavi?: string[]\n  onSelectionChange?: (selectedIds: string[]) => void\n  onStatusAction?: (cavo: Cavo, action: string) => void\n  onContextMenuAction?: (cavo: Cavo, action: string) => void\n}\n\nexport default function CaviTable({\n  cavi = [],\n  loading = false,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange,\n  onStatusAction,\n  onContextMenuAction\n}: CaviTableProps) {\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)\n  const [filteredCavi, setFilteredCavi] = useState(cavi)\n  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)\n\n  // Aggiorna i cavi quando cambiano i cavi originali\n  useEffect(() => {\n    setSmartFilteredCavi(cavi)\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Gestione filtri intelligenti\n  const handleSmartFilterChange = (filtered: Cavo[]) => {\n    setSmartFilteredCavi(filtered)\n  }\n\n  // Gestione filtri tabella\n  const handleTableFilterChange = (filtered: Cavo[]) => {\n    setFilteredCavi(filtered)\n  }\n\n  const handleSelectionToggle = () => {\n    setInternalSelectionEnabled(!internalSelectionEnabled)\n  }\n\n  // Gestione selezione\n  const handleSelectAll = (checked: boolean) => {\n    if (onSelectionChange) {\n      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])\n    }\n  }\n\n  const handleSelectCavo = (cavoId: string, checked: boolean) => {\n    if (onSelectionChange) {\n      const newSelection = checked\n        ? [...selectedCavi, cavoId]\n        : selectedCavi.filter(id => id !== cavoId)\n      onSelectionChange(newSelection)\n    }\n  }\n\n  // Bulk action handlers\n  const handleBulkExport = async () => {\n    try {\n      const response = await fetch('/api/cavi/export', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        const error = await response.json()\n        alert(`Errore durante l'esportazione: ${error.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'esportazione')\n    }\n  }\n\n  const handleBulkStatusChange = async () => {\n    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')\n    if (!newStatus) return\n\n    try {\n      const response = await fetch('/api/cavi/bulk-status', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1, // TODO: Get from context\n          newStatus\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante il cambio stato')\n    }\n  }\n\n  const handleBulkAssignCommand = () => {\n    // TODO: Implementare modal per selezione comanda\n    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)\n  }\n\n  const handleBulkDelete = async () => {\n    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {\n      return\n    }\n\n    try {\n      const response = await fetch('/api/cavi/bulk-delete', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedIds: selectedCavi,\n          cantiereId: 1 // TODO: Get from context\n        })\n      })\n\n      const result = await response.json()\n      if (result.success) {\n        alert(result.message)\n        // TODO: Refresh data\n      } else {\n        alert(`Errore: ${result.error}`)\n      }\n    } catch (error) {\n      alert('Errore durante l\\'eliminazione')\n    }\n  }\n\n  // Define columns matching original webapp structure\n  const columns: ColumnDef[] = useMemo(() => {\n    const baseColumns: ColumnDef[] = [\n      {\n        field: 'id_cavo',\n        headerName: 'ID',\n        dataType: 'text',\n        width: 70,\n        align: 'left',\n        renderCell: (row: Cavo) => (\n          <span className=\"font-semibold text-mariner-900\">{row.id_cavo}</span>\n        )\n      },\n      {\n        field: 'sistema',\n        headerName: 'Sistema',\n        dataType: 'text',\n        width: 80,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.sistema || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'utility',\n        headerName: 'Utility',\n        dataType: 'text',\n        width: 80,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.utility || ''} maxLength={8} />\n        )\n      },\n      {\n        field: 'tipologia',\n        headerName: 'Tipologia',\n        dataType: 'text',\n        width: 100,\n        renderCell: (row: Cavo) => (\n          <TruncatedText text={row.tipologia || ''} maxLength={12} />\n        )\n      },\n      {\n        field: 'formazione',\n        headerName: 'Form.',\n        dataType: 'text',\n        align: 'left',\n        width: 60,\n        renderCell: (row: Cavo) => row.formazione || row.sezione\n      },\n      {\n        field: 'metri_teorici',\n        headerName: 'M.Teor.',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n      },\n      {\n        field: 'metri_posati',\n        headerName: 'M.Reali',\n        dataType: 'number',\n        align: 'left',\n        width: 70,\n        renderCell: (row: Cavo) => {\n          const metri = row.metri_posati || row.metratura_reale || 0\n          return metri ? metri.toFixed(1) : '0'\n        }\n      },\n      {\n        field: 'ubicazione_partenza',\n        headerName: 'Da',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.da || row.ubicazione_partenza || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'ubicazione_arrivo',\n        headerName: 'A',\n        dataType: 'text',\n        width: 140,\n        renderCell: (row: Cavo) => (\n          <TruncatedText\n            text={row.a || row.ubicazione_arrivo || ''}\n            maxLength={18}\n          />\n        )\n      },\n      {\n        field: 'id_bobina',\n        headerName: 'Bobina',\n        dataType: 'text',\n        width: 80,\n        align: 'center',\n        renderCell: (row: Cavo) => getBobinaDisplay(row)\n      },\n      {\n        field: 'stato_installazione',\n        headerName: 'Stato',\n        dataType: 'text',\n        align: 'left',\n        width: 120,\n        disableSort: true,\n        getFilterValue: (row: Cavo) => getStatoFilterValue(row),\n        renderCell: (row: Cavo) => getStatusBadge(row)\n      },\n      {\n        field: 'collegamenti',\n        headerName: 'Collegamenti',\n        dataType: 'text',\n        align: 'left',\n        width: 180,\n        disableSort: true,\n        getFilterValue: (row: Cavo) => getCollegamentiFilterValue(row),\n        renderCell: (row: Cavo) => getConnectionButton(row)\n      },\n      {\n        field: 'certificato',\n        headerName: 'Certificato',\n        dataType: 'text',\n        align: 'left',\n        width: 130,\n        disableSort: true,\n        getFilterValue: (row: Cavo) => getCertificatoFilterValue(row),\n        renderCell: (row: Cavo) => getCertificationDisplay(row)\n      },\n\n    ]\n\n    // Add selection column if enabled\n    if (internalSelectionEnabled) {\n      baseColumns.unshift({\n        field: 'selection',\n        headerName: '',\n        disableFilter: true,\n        disableSort: true,\n        width: 50,\n        align: 'left',\n        renderHeader: () => (\n          <Checkbox\n            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}\n            onCheckedChange={handleSelectAll}\n          />\n        ),\n        renderCell: (row: Cavo) => (\n          <Checkbox\n            checked={selectedCavi.includes(row.id_cavo)}\n            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}\n            onClick={(e) => e.stopPropagation()}\n          />\n        )\n      })\n    }\n\n    return baseColumns\n  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])\n\n  // Custom row renderer for selection and context menu\n  const renderRow = (row: Cavo, index: number) => {\n    const isSelected = selectedCavi.includes(row.id_cavo)\n\n    return (\n      <TableRow\n        key={row.id_cavo}\n        className={`\n          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ${isSelected ? 'ring-1 ring-blue-300' : ''}\n        `}\n        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}\n        onContextMenu={(e) => {\n          e.preventDefault()\n          onContextMenuAction?.(row, 'context_menu')\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            className={`\n              py-2 px-2 text-sm text-left\n              ${isSelected ? 'text-blue-900' : 'text-gray-900'}\n              transition-colors duration-200\n            `}\n            style={{ width: column.width, ...column.cellStyle }}\n            onClick={(e) => {\n              // Prevent row click for action columns\n              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {\n                e.stopPropagation()\n              }\n            }}\n          >\n            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className=\"text-gray-400\">-</span>)}\n          </TableCell>\n        ))}\n      </TableRow>\n    )\n  }\n\n  // ===== COMPONENTI UI MIGLIORATI SECONDO LE SPECIFICHE =====\n\n  // ===== PALETTE COLORI PROFESSIONALE \"CORPORATE TECH\" =====\n  // Primario (Brand): #007bff (Blu)\n  // Successo: #28A745 (Verde)\n  // Avviso: #FFC107 (Arancione)\n  // Neutro: #6c757d (Grigio)\n  // Sfondi tenui: molto desaturati, quasi bianchi\n\n  // ===== FUNZIONI HELPER PER FILTRI =====\n\n  // Ottieni valore filtro per Stato\n  const getStatoFilterValue = (cavo: Cavo) => {\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const isInstalled = metriInstallati > 0\n    const stato = cavo.stato_installazione || 'Da installare'\n\n    // Verifica comanda attiva\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    if (comandaAttiva && stato === 'In corso') {\n      return `In corso (${comandaAttiva})`\n    }\n\n    if (stato === 'Installato' || isInstalled) {\n      return 'Installato'\n    }\n\n    return stato\n  }\n\n  // Ottieni valore filtro per Collegamenti\n  const getCollegamentiFilterValue = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n\n    if (!isInstalled) {\n      return 'Non disponibile'\n    }\n\n    switch (collegamento) {\n      case 0: return 'Collega'\n      case 1: return 'Completa Arrivo'\n      case 2: return 'Completa Partenza'\n      case 3: return 'Scollega'\n      default: return 'Gestisci'\n    }\n  }\n\n  // Ottieni valore filtro per Certificato\n  const getCertificatoFilterValue = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n\n    if (!isInstalled) {\n      return 'Non disponibile'\n    }\n\n    if (isCertified) {\n      return 'Genera PDF'\n    }\n\n    return 'Certifica'\n  }\n\n  // 1. COLONNA BOBINA - Tag Cliccabile Professionale\n  const getBobinaDisplay = (cavo: Cavo) => {\n    const idBobina = cavo.id_bobina\n\n    if (!idBobina || idBobina === 'N/A') {\n      return (\n        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-500 bg-white border border-gray-200\">\n          -\n        </span>\n      )\n    }\n\n    if (idBobina === 'BOBINA_VUOTA') {\n      // Bobina vuota - Tag cliccabile neutro\n      return (\n        <button\n          className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-white border border-gray-300 hover:bg-gray-50 hover:border-gray-400 transition-colors cursor-pointer\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, 'modify_reel')\n          }}\n          title=\"Clicca per assegnare bobina\"\n        >\n          <span>Vuota</span>\n          <ChevronDown className=\"w-3 h-3 opacity-60\" />\n        </button>\n      )\n    }\n\n    // Estrai il numero della bobina usando la stessa logica della versione originale\n    let numeroDisplay = idBobina\n\n    // Pattern dalla webapp originale: /_B(.+)$/\n    let match = idBobina.match(/_B(.+)$/)\n    if (match) {\n      numeroDisplay = match[1]\n    } else {\n      // Pattern alternativo: _b (minuscolo)\n      match = idBobina.match(/_b(.+)$/)\n      if (match) {\n        numeroDisplay = match[1]\n      } else {\n        // Pattern per cX_bY o cX_BY\n        match = idBobina.match(/c\\d+_[bB](\\d+)$/)\n        if (match) {\n          numeroDisplay = match[1]\n        } else {\n          // Pattern generale per numeri alla fine\n          match = idBobina.match(/(\\d+)$/)\n          if (match) {\n            numeroDisplay = match[1]\n          }\n        }\n      }\n    }\n\n    // Bobina assegnata - Tag cliccabile con colore primario\n    return (\n      <button\n        className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-white border border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-colors cursor-pointer\"\n        onClick={(e) => {\n          e.stopPropagation()\n          onStatusAction?.(cavo, 'modify_reel')\n        }}\n        title=\"Clicca per gestire bobina\"\n      >\n        <span>{numeroDisplay}</span>\n        <ChevronDown className=\"w-3 h-3 opacity-60\" />\n      </button>\n    )\n  }\n\n  // 2. COLONNA STATO - Badge Informativi + Azioni Strategiche\n  const getStatusBadge = (cavo: Cavo) => {\n    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0\n    const isInstalled = metriInstallati > 0\n    const stato = cavo.stato_installazione || 'Da installare'\n\n    // Verifica se il cavo è assegnato a una comanda attiva\n    const comandaPosa = cavo.comanda_posa\n    const comandaPartenza = cavo.comanda_partenza\n    const comandaArrivo = cavo.comanda_arrivo\n    const comandaCertificazione = cavo.comanda_certificazione\n    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione\n\n    // Se c'è una comanda attiva e lo stato è \"In corso\", mostra il codice comanda (cliccabile)\n    if (comandaAttiva && stato === 'In corso') {\n      return (\n        <button\n          className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-white border border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-colors cursor-pointer\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, 'view_command', comandaAttiva)\n          }}\n          title=\"Clicca per visualizzare comanda\"\n        >\n          <span>{comandaAttiva}</span>\n          <ChevronDown className=\"w-3 h-3 opacity-60\" />\n        </button>\n      )\n    }\n\n    // Stati con azioni disponibili\n    if (stato === 'Installato' || isInstalled) {\n      // Installato - Badge informativo desaturato (NO hover, NO click)\n      return (\n        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-700 bg-green-50 border border-green-100\">\n          Installato\n        </span>\n      )\n    } else if (stato === 'In corso') {\n      // In corso - Pulsante primario per inserire metri\n      return (\n        <button\n          className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-white border border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-colors cursor-pointer\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, 'insert_meters')\n          }}\n          title=\"Clicca per inserire metri posati\"\n        >\n          <span>In corso</span>\n          <ChevronDown className=\"w-3 h-3 opacity-60\" />\n        </button>\n      )\n    } else {\n      // Da installare - Pulsante neutro per inserire metri\n      return (\n        <button\n          className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-white border border-gray-300 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 transition-colors cursor-pointer\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, 'insert_meters')\n          }}\n          title=\"Clicca per inserire metri posati\"\n        >\n          <span>Da installare</span>\n          <ChevronDown className=\"w-3 h-3 opacity-60\" />\n        </button>\n      )\n    }\n  }\n\n  // 3. COLONNA COLLEGAMENTI - Pulsanti Outline Professionali\n  const getConnectionButton = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const collegamento = cavo.collegamento || cavo.collegamenti || 0\n\n    if (!isInstalled) {\n      // Non disponibile - Testo neutro senza interazione\n      return (\n        <span className=\"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-gray-500\" title=\"Collegamento disponibile solo per cavi installati\">\n          <Info className=\"w-3 h-3\" />\n          <span>Non disponibile</span>\n        </span>\n      )\n    }\n\n    // Pulsanti outline con colore primario (blu) per tutte le azioni\n    const getActionButton = (label: string, actionType: string, icon?: React.ReactNode) => {\n      return (\n        <button\n          className=\"inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium text-blue-600 bg-white border border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-colors cursor-pointer\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, actionType)\n          }}\n          title={`Clicca per ${label.toLowerCase()}`}\n        >\n          {icon && icon}\n          <span>{label}</span>\n        </button>\n      )\n    }\n\n    switch (collegamento) {\n      case 0:\n        return getActionButton(\"Collega\", \"connect_cable\", <Link className=\"w-3 h-3\" />)\n      case 1:\n        return getActionButton(\"Completa Arrivo\", \"connect_arrival\", <Link className=\"w-3 h-3\" />)\n      case 2:\n        return getActionButton(\"Completa Partenza\", \"connect_departure\", <Link className=\"w-3 h-3\" />)\n      case 3:\n        return getActionButton(\"Scollega\", \"disconnect_cable\", <Unlink className=\"w-3 h-3\" />)\n      default:\n        return getActionButton(\"Gestisci\", \"manage_connections\", <Settings className=\"w-3 h-3\" />)\n    }\n  }\n\n  // 4. COLONNA CERTIFICATO - Pulsanti Outline + Colore Secondario\n  const getCertificationDisplay = (cavo: Cavo) => {\n    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0\n    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'\n\n    if (!isInstalled) {\n      // Non disponibile - Testo neutro senza interazione\n      return (\n        <span className=\"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-gray-500\" title=\"Certificazione disponibile solo per cavi installati\">\n          <Info className=\"w-3 h-3\" />\n          <span>Non disponibile</span>\n        </span>\n      )\n    }\n\n    if (isCertified) {\n      // Certificato - Pulsante outline primario per PDF\n      return (\n        <button\n          className=\"inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium text-blue-600 bg-white border border-blue-300 hover:bg-blue-50 hover:border-blue-400 transition-colors cursor-pointer\"\n          onClick={(e) => {\n            e.stopPropagation()\n            onStatusAction?.(cavo, 'generate_pdf')\n          }}\n          title=\"Clicca per generare PDF\"\n        >\n          <Download className=\"w-3 h-3\" />\n          <span>Genera PDF</span>\n        </button>\n      )\n    }\n\n    // Non certificato - Pulsante outline secondario (viola) per certificare\n    return (\n      <button\n        className=\"inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium text-purple-600 bg-white border border-purple-300 hover:bg-purple-50 hover:border-purple-400 transition-colors cursor-pointer\"\n        onClick={(e) => {\n          e.stopPropagation()\n          onStatusAction?.(cavo, 'create_certificate')\n        }}\n        title=\"Clicca per certificare\"\n      >\n        <Award className=\"w-3 h-3\" />\n        <span>Certifica</span>\n      </button>\n    )\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Smart Filter */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n        selectionEnabled={internalSelectionEnabled}\n        onSelectionToggle={handleSelectionToggle}\n      />\n\n      {/* Filterable Table */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        onFilteredDataChange={handleTableFilterChange}\n        renderRow={renderRow}\n      />\n\n      {/* Contextual Action Bar - appears only when items are selected */}\n      {internalSelectionEnabled && selectedCavi.length > 0 && (\n        <div className=\"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10\">\n          <div className=\"flex items-center justify-between p-3\">\n            <div className=\"flex items-center space-x-3\">\n              <Badge variant=\"secondary\" className=\"bg-mariner-100 text-mariner-800\">\n                {selectedCavi.length} cavi selezionati\n              </Badge>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleSelectAll(false)}\n                className=\"text-xs\"\n              >\n                Deseleziona tutto\n              </Button>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleBulkExport()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>📊</span>\n                <span>Esporta</span>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleBulkStatusChange()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>🔄</span>\n                <span>Cambia Stato</span>\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => handleBulkAssignCommand()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>📋</span>\n                <span>Assegna Comanda</span>\n              </Button>\n\n              <Button\n                variant=\"destructive\"\n                size=\"sm\"\n                onClick={() => handleBulkDelete()}\n                className=\"flex items-center space-x-1\"\n              >\n                <span>🗑️</span>\n                <span>Elimina</span>\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;;;;AAgDe,SAAS,UAAU,EAChC,OAAO,EAAE,EACT,UAAU,KAAK,EACf,mBAAmB,KAAK,EACxB,eAAe,EAAE,EACjB,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACJ;IACf,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qBAAqB;QACrB,gBAAgB;IAClB,GAAG;QAAC;KAAK;IAET,+BAA+B;IAC/B,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB;IACvB;IAEA,0BAA0B;IAC1B,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,4BAA4B,CAAC;IAC/B;IAEA,qBAAqB;IACrB,MAAM,kBAAkB,CAAC;QACvB,IAAI,mBAAmB;YACrB,kBAAkB,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB;QACxC,IAAI,mBAAmB;YACrB,MAAM,eAAe,UACjB;mBAAI;gBAAc;aAAO,GACzB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO;YACrC,kBAAkB;QACpB;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACxE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,CAAC,+BAA+B,EAAE,MAAM,KAAK,EAAE;YACvD;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB;QAC7B,MAAM,YAAY,OAAO;QACzB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY;oBACZ;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,0BAA0B;QAC9B,iDAAiD;QACjD,MAAM,CAAC,yBAAyB,EAAE,aAAa,MAAM,CAAC,KAAK,CAAC;IAC9D;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ,CAAC,8BAA8B,EAAE,aAAa,MAAM,CAAC,MAAM,CAAC,GAAG;YAC1E;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa;oBACb,YAAY,EAAE,yBAAyB;gBACzC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,OAAO,OAAO;YACpB,qBAAqB;YACvB,OAAO;gBACL,MAAM,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,MAAM,UAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnC,MAAM,cAA2B;YAC/B;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC;wBAAK,WAAU;kCAAkC,IAAI,OAAO;;;;;;YAEjE;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,OAAO,IAAI;wBAAI,WAAW;;;;;;YAEvD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,OAAO,IAAI;wBAAI,WAAW;;;;;;YAEvD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBAAC,MAAM,IAAI,SAAS,IAAI;wBAAI,WAAW;;;;;;YAEzD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,IAAI,UAAU,IAAI,IAAI,OAAO;YAC1D;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,IAAI,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,KAAK;YAChF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC;oBACX,MAAM,QAAQ,IAAI,YAAY,IAAI,IAAI,eAAe,IAAI;oBACzD,OAAO,QAAQ,MAAM,OAAO,CAAC,KAAK;gBACpC;YACF;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBACZ,MAAM,IAAI,EAAE,IAAI,IAAI,mBAAmB,IAAI;wBAC3C,WAAW;;;;;;YAGjB;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,YAAY,CAAC,oBACX,8OAAC,6IAAA,CAAA,UAAa;wBACZ,MAAM,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI;wBACxC,WAAW;;;;;;YAGjB;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,YAAY,CAAC,MAAc,iBAAiB;YAC9C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,gBAAgB,CAAC,MAAc,oBAAoB;gBACnD,YAAY,CAAC,MAAc,eAAe;YAC5C;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,gBAAgB,CAAC,MAAc,2BAA2B;gBAC1D,YAAY,CAAC,MAAc,oBAAoB;YACjD;YACA;gBACE,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,gBAAgB,CAAC,MAAc,0BAA0B;gBACzD,YAAY,CAAC,MAAc,wBAAwB;YACrD;SAED;QAED,kCAAkC;QAClC,IAAI,0BAA0B;YAC5B,YAAY,OAAO,CAAC;gBAClB,OAAO;gBACP,YAAY;gBACZ,eAAe;gBACf,aAAa;gBACb,OAAO;gBACP,OAAO;gBACP,cAAc,kBACZ,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI,aAAa,MAAM,GAAG;wBAC9E,iBAAiB;;;;;;gBAGrB,YAAY,CAAC,oBACX,8OAAC,oIAAA,CAAA,WAAQ;wBACP,SAAS,aAAa,QAAQ,CAAC,IAAI,OAAO;wBAC1C,iBAAiB,CAAC,UAAY,iBAAiB,IAAI,OAAO,EAAE;wBAC5D,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;YAGvC;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAA0B;QAAc;QAAc;QAAiB;KAAiB;IAE5F,qDAAqD;IACrD,MAAM,YAAY,CAAC,KAAW;QAC5B,MAAM,aAAa,aAAa,QAAQ,CAAC,IAAI,OAAO;QAEpD,qBACE,8OAAC,iIAAA,CAAA,WAAQ;YAEP,WAAW,CAAC;UACV,EAAE,aAAa,+BAA+B,WAAW;;;;UAIzD,EAAE,aAAa,yBAAyB,GAAG;QAC7C,CAAC;YACD,SAAS,IAAM,4BAA4B,iBAAiB,IAAI,OAAO,EAAE,CAAC;YAC1E,eAAe,CAAC;gBACd,EAAE,cAAc;gBAChB,sBAAsB,KAAK;YAC7B;sBAEC,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;oBAER,WAAW,CAAC;;cAEV,EAAE,aAAa,kBAAkB,gBAAgB;;YAEnD,CAAC;oBACD,OAAO;wBAAE,OAAO,OAAO,KAAK;wBAAE,GAAG,OAAO,SAAS;oBAAC;oBAClD,SAAS,CAAC;wBACR,uCAAuC;wBACvC,IAAI;4BAAC;4BAAuB;4BAAgB;yBAAc,CAAC,QAAQ,CAAC,OAAO,KAAK,GAAG;4BACjF,EAAE,eAAe;wBACnB;oBACF;8BAEC,OAAO,UAAU,GAAG,OAAO,UAAU,CAAC,OAAQ,GAAG,CAAC,OAAO,KAAK,CAAC,kBAAI,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;mBAd/F,OAAO,KAAK;;;;;WAhBhB,IAAI,OAAO;;;;;IAmCtB;IAEA,6DAA6D;IAE7D,4DAA4D;IAC5D,kCAAkC;IAClC,4BAA4B;IAC5B,8BAA8B;IAC9B,2BAA2B;IAC3B,gDAAgD;IAEhD,yCAAyC;IAEzC,kCAAkC;IAClC,MAAM,sBAAsB,CAAC;QAC3B,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,cAAc,kBAAkB;QACtC,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAE1C,0BAA0B;QAC1B,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QACzD,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,IAAI,iBAAiB,UAAU,YAAY;YACzC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACtC;QAEA,IAAI,UAAU,gBAAgB,aAAa;YACzC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,yCAAyC;IACzC,MAAM,6BAA6B,CAAC;QAClC,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QAEA,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,wCAAwC;IACxC,MAAM,4BAA4B,CAAC;QACjC,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QAEnG,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QAEA,IAAI,aAAa;YACf,OAAO;QACT;QAEA,OAAO;IACT;IAEA,mDAAmD;IACnD,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,KAAK,SAAS;QAE/B,IAAI,CAAC,YAAY,aAAa,OAAO;YACnC,qBACE,8OAAC;gBAAK,WAAU;0BAAoH;;;;;;QAIxI;QAEA,IAAI,aAAa,gBAAgB;YAC/B,uCAAuC;YACvC,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;gBACA,OAAM;;kCAEN,8OAAC;kCAAK;;;;;;kCACN,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;QAG7B;QAEA,iFAAiF;QACjF,IAAI,gBAAgB;QAEpB,4CAA4C;QAC5C,IAAI,QAAQ,SAAS,KAAK,CAAC;QAC3B,IAAI,OAAO;YACT,gBAAgB,KAAK,CAAC,EAAE;QAC1B,OAAO;YACL,sCAAsC;YACtC,QAAQ,SAAS,KAAK,CAAC;YACvB,IAAI,OAAO;gBACT,gBAAgB,KAAK,CAAC,EAAE;YAC1B,OAAO;gBACL,4BAA4B;gBAC5B,QAAQ,SAAS,KAAK,CAAC;gBACvB,IAAI,OAAO;oBACT,gBAAgB,KAAK,CAAC,EAAE;gBAC1B,OAAO;oBACL,wCAAwC;oBACxC,QAAQ,SAAS,KAAK,CAAC;oBACvB,IAAI,OAAO;wBACT,gBAAgB,KAAK,CAAC,EAAE;oBAC1B;gBACF;YACF;QACF;QAEA,wDAAwD;QACxD,qBACE,8OAAC;YACC,WAAU;YACV,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB,iBAAiB,MAAM;YACzB;YACA,OAAM;;8BAEN,8OAAC;8BAAM;;;;;;8BACP,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;IAG7B;IAEA,4DAA4D;IAC5D,MAAM,iBAAiB,CAAC;QACtB,MAAM,kBAAkB,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;QACrE,MAAM,cAAc,kBAAkB;QACtC,MAAM,QAAQ,KAAK,mBAAmB,IAAI;QAE1C,uDAAuD;QACvD,MAAM,cAAc,KAAK,YAAY;QACrC,MAAM,kBAAkB,KAAK,gBAAgB;QAC7C,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,wBAAwB,KAAK,sBAAsB;QACzD,MAAM,gBAAgB,eAAe,mBAAmB,iBAAiB;QAEzE,2FAA2F;QAC3F,IAAI,iBAAiB,UAAU,YAAY;YACzC,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM,gBAAgB;gBACzC;gBACA,OAAM;;kCAEN,8OAAC;kCAAM;;;;;;kCACP,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;QAG7B;QAEA,+BAA+B;QAC/B,IAAI,UAAU,gBAAgB,aAAa;YACzC,iEAAiE;YACjE,qBACE,8OAAC;gBAAK,WAAU;0BAAyH;;;;;;QAI7I,OAAO,IAAI,UAAU,YAAY;YAC/B,kDAAkD;YAClD,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;gBACA,OAAM;;kCAEN,8OAAC;kCAAK;;;;;;kCACN,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;QAG7B,OAAO;YACL,qDAAqD;YACrD,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;gBACA,OAAM;;kCAEN,8OAAC;kCAAK;;;;;;kCACN,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;QAG7B;IACF;IAEA,2DAA2D;IAC3D,MAAM,sBAAsB,CAAC;QAC3B,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,eAAe,KAAK,YAAY,IAAI,KAAK,YAAY,IAAI;QAE/D,IAAI,CAAC,aAAa;YAChB,mDAAmD;YACnD,qBACE,8OAAC;gBAAK,WAAU;gBAA6E,OAAM;;kCACjG,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;kCAAK;;;;;;;;;;;;QAGZ;QAEA,iEAAiE;QACjE,MAAM,kBAAkB,CAAC,OAAe,YAAoB;YAC1D,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;gBACA,OAAO,CAAC,WAAW,EAAE,MAAM,WAAW,IAAI;;oBAEzC,QAAQ;kCACT,8OAAC;kCAAM;;;;;;;;;;;;QAGb;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO,gBAAgB,WAAW,+BAAiB,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACrE,KAAK;gBACH,OAAO,gBAAgB,mBAAmB,iCAAmB,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YAC/E,KAAK;gBACH,OAAO,gBAAgB,qBAAqB,mCAAqB,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACnF,KAAK;gBACH,OAAO,gBAAgB,YAAY,kCAAoB,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3E;gBACE,OAAO,gBAAgB,YAAY,oCAAsB,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QACjF;IACF;IAEA,gEAAgE;IAChE,MAAM,0BAA0B,CAAC;QAC/B,MAAM,cAAc,KAAK,YAAY,GAAG,KAAK,KAAK,eAAe,GAAG;QACpE,MAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,KAAK,WAAW,KAAK;QAEnG,IAAI,CAAC,aAAa;YAChB,mDAAmD;YACnD,qBACE,8OAAC;gBAAK,WAAU;gBAA6E,OAAM;;kCACjG,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;kCAAK;;;;;;;;;;;;QAGZ;QAEA,IAAI,aAAa;YACf,kDAAkD;YAClD,qBACE,8OAAC;gBACC,WAAU;gBACV,SAAS,CAAC;oBACR,EAAE,eAAe;oBACjB,iBAAiB,MAAM;gBACzB;gBACA,OAAM;;kCAEN,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;kCAAK;;;;;;;;;;;;QAGZ;QAEA,wEAAwE;QACxE,qBACE,8OAAC;YACC,WAAU;YACV,SAAS,CAAC;gBACR,EAAE,eAAe;gBACjB,iBAAiB,MAAM;YACzB;YACA,OAAM;;8BAEN,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,8OAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,6IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,sBAAsB;gBACtB,SAAS;gBACT,kBAAkB;gBAClB,mBAAmB;;;;;;0BAIrB,8OAAC,+IAAA,CAAA,UAAe;gBACd,MAAM;gBACN,SAAS;gBACT,SAAS;gBACT,cAAa;gBACb,sBAAsB;gBACtB,WAAW;;;;;;YAIZ,4BAA4B,aAAa,MAAM,GAAG,mBACjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC,aAAa,MAAM;wCAAC;;;;;;;8CAEvB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM;oCACf,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 3776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/tooltips/CableTooltips.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useRef, useEffect } from 'react'\nimport { createPortal } from 'react-dom'\n\ninterface TooltipProps {\n  content: string | React.ReactNode\n  children: React.ReactNode\n  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'\n  delay?: number\n  className?: string\n  disabled?: boolean\n  maxWidth?: number\n}\n\ninterface TooltipPosition {\n  top: number\n  left: number\n  position: 'top' | 'bottom' | 'left' | 'right'\n}\n\nexport const CableTooltip: React.FC<TooltipProps> = ({\n  content,\n  children,\n  position = 'auto',\n  delay = 500,\n  className = '',\n  disabled = false,\n  maxWidth = 250\n}) => {\n  const [isVisible, setIsVisible] = useState(false)\n  const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition | null>(null)\n  const triggerRef = useRef<HTMLDivElement>(null)\n  const tooltipRef = useRef<HTMLDivElement>(null)\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null)\n\n  const calculatePosition = (): TooltipPosition | null => {\n    if (!triggerRef.current) return null\n\n    const triggerRect = triggerRef.current.getBoundingClientRect()\n    const viewportWidth = window.innerWidth\n    const viewportHeight = window.innerHeight\n    const tooltipWidth = maxWidth\n    const tooltipHeight = 40 // Estimated height\n\n    let finalPosition = position\n    let top = 0\n    let left = 0\n\n    // Auto positioning logic\n    if (position === 'auto') {\n      const spaceTop = triggerRect.top\n      const spaceBottom = viewportHeight - triggerRect.bottom\n      const spaceLeft = triggerRect.left\n      const spaceRight = viewportWidth - triggerRect.right\n\n      if (spaceTop > tooltipHeight && spaceTop > spaceBottom) {\n        finalPosition = 'top'\n      } else if (spaceBottom > tooltipHeight) {\n        finalPosition = 'bottom'\n      } else if (spaceRight > tooltipWidth) {\n        finalPosition = 'right'\n      } else {\n        finalPosition = 'left'\n      }\n    }\n\n    // Calculate position based on final position\n    switch (finalPosition) {\n      case 'top':\n        top = triggerRect.top - tooltipHeight - 8\n        left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2)\n        break\n      case 'bottom':\n        top = triggerRect.bottom + 8\n        left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2)\n        break\n      case 'left':\n        top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2)\n        left = triggerRect.left - tooltipWidth - 8\n        break\n      case 'right':\n        top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2)\n        left = triggerRect.right + 8\n        break\n    }\n\n    // Ensure tooltip stays within viewport\n    left = Math.max(8, Math.min(left, viewportWidth - tooltipWidth - 8))\n    top = Math.max(8, Math.min(top, viewportHeight - tooltipHeight - 8))\n\n    return { top, left, position: finalPosition }\n  }\n\n  const showTooltip = () => {\n    if (disabled) return\n\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n    }\n\n    timeoutRef.current = setTimeout(() => {\n      const position = calculatePosition()\n      if (position) {\n        setTooltipPosition(position)\n        setIsVisible(true)\n      }\n    }, delay)\n  }\n\n  const hideTooltip = () => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n      timeoutRef.current = null\n    }\n    setIsVisible(false)\n    setTooltipPosition(null)\n  }\n\n  useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current)\n      }\n    }\n  }, [])\n\n  const getArrowClasses = (pos: string) => {\n    const baseClasses = \"absolute w-0 h-0 border-solid\"\n    switch (pos) {\n      case 'top':\n        return `${baseClasses} top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900`\n      case 'bottom':\n        return `${baseClasses} bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900`\n      case 'left':\n        return `${baseClasses} left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900`\n      case 'right':\n        return `${baseClasses} right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900`\n      default:\n        return baseClasses\n    }\n  }\n\n  const tooltipElement = isVisible && tooltipPosition ? (\n    <div\n      ref={tooltipRef}\n      className={`fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ${className}`}\n      style={{\n        top: tooltipPosition.top,\n        left: tooltipPosition.left,\n        maxWidth: maxWidth,\n        wordWrap: 'break-word',\n        whiteSpace: 'normal'\n      }}\n      role=\"tooltip\"\n      aria-hidden={!isVisible}\n    >\n      {content}\n      <div className={getArrowClasses(tooltipPosition.position)} />\n    </div>\n  ) : null\n\n  return (\n    <>\n      <div\n        ref={triggerRef}\n        onMouseEnter={showTooltip}\n        onMouseLeave={hideTooltip}\n        onFocus={showTooltip}\n        onBlur={hideTooltip}\n        className=\"inline-block\"\n        aria-describedby={isVisible ? 'tooltip' : undefined}\n      >\n        {children}\n      </div>\n      {typeof document !== 'undefined' && tooltipElement && createPortal(tooltipElement, document.body)}\n    </>\n  )\n}\n\n// Specialized tooltips for different cable actions\ninterface ActionTooltipProps {\n  action: 'connect' | 'disconnect' | 'certify' | 'generate_pdf' | 'install' | 'modify'\n  cableId?: string\n  disabled?: boolean\n  children: React.ReactNode\n}\n\nexport const ActionTooltip: React.FC<ActionTooltipProps> = ({\n  action,\n  cableId,\n  disabled = false,\n  children\n}) => {\n  // TOOLTIP DISABILITATI - Restituisce solo i children senza tooltip fastidiosi\n  return <>{children}</>\n}\n\n// KPI Tooltip for statistics\ninterface KpiTooltipProps {\n  type: 'total' | 'installed' | 'in_progress' | 'to_install' | 'connected' | 'certified'\n  count: number\n  percentage?: number\n  children: React.ReactNode\n}\n\nexport const KpiTooltip: React.FC<KpiTooltipProps> = ({\n  type,\n  count,\n  percentage,\n  children\n}) => {\n  const getTooltipContent = () => {\n    const baseText = `${count} cavi`\n    const percentageText = percentage !== undefined ? ` (${percentage.toFixed(1)}%)` : ''\n    \n    switch (type) {\n      case 'total':\n        return `Totale cavi nel progetto: ${baseText}`\n      case 'installed':\n        return `Cavi fisicamente installati: ${baseText}${percentageText}`\n      case 'in_progress':\n        return `Cavi in corso di installazione: ${baseText}${percentageText}`\n      case 'to_install':\n        return `Cavi ancora da installare: ${baseText}${percentageText}`\n      case 'connected':\n        return `Cavi completamente collegati: ${baseText}${percentageText}`\n      case 'certified':\n        return `Cavi certificati e collaudati: ${baseText}${percentageText}`\n      default:\n        return baseText\n    }\n  }\n\n  return (\n    <CableTooltip\n      content={getTooltipContent()}\n      delay={200}\n      position=\"bottom\"\n    >\n      {children}\n    </CableTooltip>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAqBO,MAAM,eAAuC,CAAC,EACnD,OAAO,EACP,QAAQ,EACR,WAAW,MAAM,EACjB,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,WAAW,GAAG,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,OAAO,EAAE,OAAO;QAEhC,MAAM,cAAc,WAAW,OAAO,CAAC,qBAAqB;QAC5D,MAAM,gBAAgB,OAAO,UAAU;QACvC,MAAM,iBAAiB,OAAO,WAAW;QACzC,MAAM,eAAe;QACrB,MAAM,gBAAgB,GAAG,mBAAmB;;QAE5C,IAAI,gBAAgB;QACpB,IAAI,MAAM;QACV,IAAI,OAAO;QAEX,yBAAyB;QACzB,IAAI,aAAa,QAAQ;YACvB,MAAM,WAAW,YAAY,GAAG;YAChC,MAAM,cAAc,iBAAiB,YAAY,MAAM;YACvD,MAAM,YAAY,YAAY,IAAI;YAClC,MAAM,aAAa,gBAAgB,YAAY,KAAK;YAEpD,IAAI,WAAW,iBAAiB,WAAW,aAAa;gBACtD,gBAAgB;YAClB,OAAO,IAAI,cAAc,eAAe;gBACtC,gBAAgB;YAClB,OAAO,IAAI,aAAa,cAAc;gBACpC,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;YAClB;QACF;QAEA,6CAA6C;QAC7C,OAAQ;YACN,KAAK;gBACH,MAAM,YAAY,GAAG,GAAG,gBAAgB;gBACxC,OAAO,YAAY,IAAI,GAAI,YAAY,KAAK,GAAG,IAAM,eAAe;gBACpE;YACF,KAAK;gBACH,MAAM,YAAY,MAAM,GAAG;gBAC3B,OAAO,YAAY,IAAI,GAAI,YAAY,KAAK,GAAG,IAAM,eAAe;gBACpE;YACF,KAAK;gBACH,MAAM,YAAY,GAAG,GAAI,YAAY,MAAM,GAAG,IAAM,gBAAgB;gBACpE,OAAO,YAAY,IAAI,GAAG,eAAe;gBACzC;YACF,KAAK;gBACH,MAAM,YAAY,GAAG,GAAI,YAAY,MAAM,GAAG,IAAM,gBAAgB;gBACpE,OAAO,YAAY,KAAK,GAAG;gBAC3B;QACJ;QAEA,uCAAuC;QACvC,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,gBAAgB,eAAe;QACjE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,iBAAiB,gBAAgB;QAEjE,OAAO;YAAE;YAAK;YAAM,UAAU;QAAc;IAC9C;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU;QAEd,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QAEA,WAAW,OAAO,GAAG,WAAW;YAC9B,MAAM,WAAW;YACjB,IAAI,UAAU;gBACZ,mBAAmB;gBACnB,aAAa;YACf;QACF,GAAG;IACL;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;YAC/B,WAAW,OAAO,GAAG;QACvB;QACA,aAAa;QACb,mBAAmB;IACrB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,cAAc;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO,GAAG,YAAY,0IAA0I,CAAC;YACnK,KAAK;gBACH,OAAO,GAAG,YAAY,6IAA6I,CAAC;YACtK,KAAK;gBACH,OAAO,GAAG,YAAY,0IAA0I,CAAC;YACnK,KAAK;gBACH,OAAO,GAAG,YAAY,2IAA2I,CAAC;YACpK;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,aAAa,gCAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAC,6HAA6H,EAAE,WAAW;QACtJ,OAAO;YACL,KAAK,gBAAgB,GAAG;YACxB,MAAM,gBAAgB,IAAI;YAC1B,UAAU;YACV,UAAU;YACV,YAAY;QACd;QACA,MAAK;QACL,eAAa,CAAC;;YAEb;0BACD,8OAAC;gBAAI,WAAW,gBAAgB,gBAAgB,QAAQ;;;;;;;;;;;eAExD;IAEJ,qBACE;;0BACE,8OAAC;gBACC,KAAK;gBACL,cAAc;gBACd,cAAc;gBACd,SAAS;gBACT,QAAQ;gBACR,WAAU;gBACV,oBAAkB,YAAY,YAAY;0BAEzC;;;;;;YAEF,OAAO,aAAa,eAAe,gCAAkB,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,SAAS,IAAI;;;AAGtG;AAUO,MAAM,gBAA8C,CAAC,EAC1D,MAAM,EACN,OAAO,EACP,WAAW,KAAK,EAChB,QAAQ,EACT;IACC,8EAA8E;IAC9E,qBAAO;kBAAG;;AACZ;AAUO,MAAM,aAAwC,CAAC,EACpD,IAAI,EACJ,KAAK,EACL,UAAU,EACV,QAAQ,EACT;IACC,MAAM,oBAAoB;QACxB,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC;QAChC,MAAM,iBAAiB,eAAe,YAAY,CAAC,EAAE,EAAE,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG;QAEnF,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,0BAA0B,EAAE,UAAU;YAChD,KAAK;gBACH,OAAO,CAAC,6BAA6B,EAAE,WAAW,gBAAgB;YACpE,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,WAAW,gBAAgB;YACvE,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,WAAW,gBAAgB;YAClE,KAAK;gBACH,OAAO,CAAC,8BAA8B,EAAE,WAAW,gBAAgB;YACrE,KAAK;gBACH,OAAO,CAAC,+BAA+B,EAAE,WAAW,gBAAgB;YACtE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,OAAO;QACP,UAAS;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 3983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CaviStatistics.tsx"], "sourcesContent": ["'use client'\n\nimport { useMemo, useState } from 'react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport {\n  Cable,\n  CheckCircle,\n  Clock,\n  AlertTriangle,\n  Zap,\n  Package,\n  BarChart3,\n  Filter,\n  X\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { KpiTooltip } from './tooltips/CableTooltips'\n\ninterface CaviStatisticsProps {\n  cavi: Cavo[]\n  filteredCavi: Cavo[]\n  className?: string\n  revisioneCorrente?: string\n}\n\nexport default function CaviStatistics({\n  cavi,\n  filteredCavi,\n  className,\n  revisioneCorrente\n}: CaviStatisticsProps) {\n  const stats = useMemo(() => {\n    const totalCavi = cavi.length\n    const filteredCount = filteredCavi.length\n    \n    // Installation status\n    const installati = filteredCavi.filter(c => \n      c.stato_installazione === 'Installato' || \n      (c.metri_posati && c.metri_posati > 0) ||\n      (c.metratura_reale && c.metratura_reale > 0)\n    ).length\n    \n    const inCorso = filteredCavi.filter(c => \n      c.stato_installazione === 'In corso'\n    ).length\n    \n    const daInstallare = filteredCount - installati - inCorso\n    \n    // Connection status\n    const collegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 3 // Both sides connected\n    }).length\n    \n    const parzialmenteCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 1 || collegamento === 2 // One side connected\n    }).length\n    \n    const nonCollegati = filteredCavi.filter(c => {\n      const collegamento = c.collegamento || c.collegamenti || 0\n      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)\n    }).length\n    \n    // Certification status\n    const certificati = filteredCavi.filter(c => \n      c.certificato === true || \n      c.certificato === 'SI' || \n      c.certificato === 'CERTIFICATO'\n    ).length\n    \n    // Meters calculation\n    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = filteredCavi.reduce((sum, c) => {\n      const metri = c.metri_posati || c.metratura_reale || 0\n      return sum + metri\n    }, 0)\n\n    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale\n    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {\n      // Pesi per le fasi del progetto\n      const Wp = 2.0  // Peso fase Posa\n      const Wc = 1.5  // Peso fase Collegamento\n      const Wz = 0.5  // Peso fase Certificazione\n\n      // Se non ci sono cavi, ritorna 0\n      if (nTot === 0) return 0\n\n      // Calcolo del numeratore (Sforzo Completato)\n      const sforzoSoloInstallati = (nInst - nColl) * Wp\n      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)\n      const sforzoCertificati = nCert * (Wp + Wc + Wz)\n      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati\n\n      // Calcolo del denominatore (Sforzo Massimo Previsto)\n      const denominatore = nTot * (Wp + Wc + Wz)\n\n      // Calcolo finale dell'IAP in percentuale\n      const iap = (numeratore / denominatore) * 100\n\n      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali\n    }\n\n    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)\n    \n    return {\n      totalCavi,\n      filteredCount,\n      installati,\n      inCorso,\n      daInstallare,\n      collegati,\n      parzialmenteCollegati,\n      nonCollegati,\n      certificati,\n      metriTotali,\n      metriInstallati,\n      percentualeInstallazione\n    }\n  }, [cavi, filteredCavi])\n\n  // Removed click-to-filter functionality as per user request\n\n  return (\n    <Card className={className}>\n      <CardContent className=\"p-1.5\">\n        {/* Header with revision and filter indicator */}\n        <div className=\"flex items-center justify-between mb-1\">\n          <div className=\"flex items-center space-x-1.5\">\n            <BarChart3 className=\"h-3.5 w-3.5 text-mariner-600\" />\n            <span className=\"text-xs font-semibold text-mariner-900\">Statistiche Cavi</span>\n            {/* Filter indicator removed */}\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {/* Clear filter button removed */}\n            {revisioneCorrente && (\n              <Badge variant=\"outline\" className=\"text-xs font-medium py-0 px-1.5 h-5\">\n                Rev. {revisioneCorrente}\n              </Badge>\n            )}\n          </div>\n        </div>\n\n        {/* Statistics distributed across full width */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2\">\n\n          {/* Total cables */}\n          <KpiTooltip type=\"total\" count={stats.totalCavi}>\n            <div className=\"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg\">\n              <Cable className=\"h-3.5 w-3.5 text-mariner-600\" />\n              <div>\n                <div className=\"font-bold text-mariner-900 text-sm\">{stats.filteredCount}</div>\n                <div className=\"text-xs text-mariner-600\">di {stats.totalCavi} cavi</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Installation status */}\n          <KpiTooltip\n            type=\"installed\"\n            count={stats.installati}\n            percentage={(stats.installati / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi installati: ${stats.installati} cavi`}\n            >\n              <CheckCircle className=\"h-3.5 w-3.5 text-green-600\" />\n              <div>\n                <div className=\"font-bold text-green-700 text-sm\">{stats.installati}</div>\n                <div className=\"text-xs text-green-600\">installati</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          <KpiTooltip\n            type=\"in_progress\"\n            count={stats.inCorso}\n            percentage={(stats.inCorso / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi in corso: ${stats.inCorso} cavi`}\n            >\n              <Clock className=\"h-3.5 w-3.5 text-yellow-600\" />\n              <div>\n                <div className=\"font-bold text-yellow-700 text-sm\">{stats.inCorso}</div>\n                <div className=\"text-xs text-yellow-600\">in corso</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          <KpiTooltip\n            type=\"to_install\"\n            count={stats.daInstallare}\n            percentage={(stats.daInstallare / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi da installare: ${stats.daInstallare} cavi`}\n            >\n              <AlertTriangle className=\"h-3.5 w-3.5 text-gray-600\" />\n              <div>\n                <div className=\"font-bold text-gray-700 text-sm\">{stats.daInstallare}</div>\n                <div className=\"text-xs text-gray-600\">da installare</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Connection status */}\n          <KpiTooltip\n            type=\"connected\"\n            count={stats.collegati}\n            percentage={(stats.collegati / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi collegati: ${stats.collegati} cavi`}\n            >\n              <Zap className=\"h-3.5 w-3.5 text-blue-600\" />\n              <div>\n                <div className=\"font-bold text-blue-700 text-sm\">{stats.collegati}</div>\n                <div className=\"text-xs text-blue-600\">collegati</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Certification status */}\n          <KpiTooltip\n            type=\"certified\"\n            count={stats.certificati}\n            percentage={(stats.certificati / stats.filteredCount) * 100}\n          >\n            <div\n              className=\"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg\"\n              aria-label={`Cavi certificati: ${stats.certificati} cavi`}\n            >\n              <Package className=\"h-3.5 w-3.5 text-purple-600\" />\n              <div>\n                <div className=\"font-bold text-purple-700 text-sm\">{stats.certificati}</div>\n                <div className=\"text-xs text-purple-600\">certificati</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n          {/* Meters progress */}\n          <KpiTooltip\n            type=\"total\"\n            count={stats.metriInstallati}\n          >\n            <div className=\"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg\">\n              <div className=\"h-3.5 w-3.5 flex items-center justify-center\">\n                <div className=\"h-2 w-2 bg-indigo-600 rounded-full\"></div>\n              </div>\n              <div>\n                <div className=\"font-bold text-indigo-700 text-sm\">{stats.metriInstallati.toLocaleString()}m</div>\n                <div className=\"text-xs text-indigo-600\">di {stats.metriTotali.toLocaleString()}m</div>\n              </div>\n            </div>\n          </KpiTooltip>\n\n        </div>\n\n        {/* IAP Progress bar - Colori morbidi */}\n        {stats.filteredCount > 0 && (\n          <div className=\"mt-2 bg-gray-50 p-2 rounded-lg\">\n            <div className=\"flex justify-between text-xs font-medium text-gray-700 mb-1\">\n              <span>IAP - Indice Avanzamento Ponderato</span>\n              <span className={`font-bold ${\n                stats.percentualeInstallazione >= 80 ? 'text-emerald-700' :\n                stats.percentualeInstallazione >= 50 ? 'text-yellow-700' :\n                stats.percentualeInstallazione >= 25 ? 'text-orange-700' : 'text-amber-700'\n              }`}>\n                {stats.percentualeInstallazione.toFixed(1)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${\n                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-emerald-500 to-emerald-600' :\n                  stats.percentualeInstallazione >= 50 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :\n                  stats.percentualeInstallazione >= 25 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :\n                  'bg-gradient-to-r from-amber-500 to-amber-600'\n                }`}\n                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-0.5\">\n              <span>Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)</span>\n              <span>{stats.installati}I + {stats.collegati}C + {stats.certificati}Cert</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAlBA;;;;;;;AA2Be,SAAS,eAAe,EACrC,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,iBAAiB,EACG;IACpB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpB,MAAM,YAAY,KAAK,MAAM;QAC7B,MAAM,gBAAgB,aAAa,MAAM;QAEzC,sBAAsB;QACtB,MAAM,aAAa,aAAa,MAAM,CAAC,CAAA,IACrC,EAAE,mBAAmB,KAAK,gBACzB,EAAE,YAAY,IAAI,EAAE,YAAY,GAAG,KACnC,EAAE,eAAe,IAAI,EAAE,eAAe,GAAG,GAC1C,MAAM;QAER,MAAM,UAAU,aAAa,MAAM,CAAC,CAAA,IAClC,EAAE,mBAAmB,KAAK,YAC1B,MAAM;QAER,MAAM,eAAe,gBAAgB,aAAa;QAElD,oBAAoB;QACpB,MAAM,YAAY,aAAa,MAAM,CAAC,CAAA;YACpC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,EAAE,uBAAuB;;QACnD,GAAG,MAAM;QAET,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA;YAChD,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,iBAAiB,EAAE,qBAAqB;;QACvE,GAAG,MAAM;QAET,MAAM,eAAe,aAAa,MAAM,CAAC,CAAA;YACvC,MAAM,eAAe,EAAE,YAAY,IAAI,EAAE,YAAY,IAAI;YACzD,OAAO,iBAAiB,KAAK,CAAC,EAAE,YAAY,GAAG,KAAK,EAAE,eAAe,GAAG,CAAC;QAC3E,GAAG,MAAM;QAET,uBAAuB;QACvB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,IACtC,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,QAClB,EAAE,WAAW,KAAK,eAClB,MAAM;QAER,qBAAqB;QACrB,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAClF,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAC,KAAK;YAChD,MAAM,QAAQ,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI;YACrD,OAAO,MAAM;QACf,GAAG;QAEH,4EAA4E;QAC5E,MAAM,eAAe,CAAC,MAAc,OAAe,OAAe;YAChE,gCAAgC;YAChC,MAAM,KAAK,IAAK,iBAAiB;;YACjC,MAAM,KAAK,IAAK,yBAAyB;;YACzC,MAAM,KAAK,IAAK,2BAA2B;;YAE3C,iCAAiC;YACjC,IAAI,SAAS,GAAG,OAAO;YAEvB,6CAA6C;YAC7C,MAAM,uBAAuB,CAAC,QAAQ,KAAK,IAAI;YAC/C,MAAM,sBAAsB,CAAC,QAAQ,KAAK,IAAI,CAAC,KAAK,EAAE;YACtD,MAAM,oBAAoB,QAAQ,CAAC,KAAK,KAAK,EAAE;YAC/C,MAAM,aAAa,uBAAuB,sBAAsB;YAEhE,qDAAqD;YACrD,MAAM,eAAe,OAAO,CAAC,KAAK,KAAK,EAAE;YAEzC,yCAAyC;YACzC,MAAM,MAAM,AAAC,aAAa,eAAgB;YAE1C,OAAO,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,yBAAyB;;QAC9D;QAEA,MAAM,2BAA2B,aAAa,eAAe,YAAY,WAAW;QAEpF,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,4DAA4D;IAE5D,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;kBACf,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAyC;;;;;;;;;;;;sCAG3D,8OAAC;4BAAI,WAAU;sCAEZ,mCACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAsC;oCACjE;;;;;;;;;;;;;;;;;;8BAOd,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC,uJAAA,CAAA,aAAU;4BAAC,MAAK;4BAAQ,OAAO,MAAM,SAAS;sCAC7C,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAsC,MAAM,aAAa;;;;;;0DACxE,8OAAC;gDAAI,WAAU;;oDAA2B;oDAAI,MAAM,SAAS;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAMpE,8OAAC,uJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,UAAU;4BACvB,YAAY,AAAC,MAAM,UAAU,GAAG,MAAM,aAAa,GAAI;sCAEvD,cAAA,8OAAC;gCACC,WAAU;gCACV,cAAY,CAAC,iBAAiB,EAAE,MAAM,UAAU,CAAC,KAAK,CAAC;;kDAEvD,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAoC,MAAM,UAAU;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;sCAK9C,8OAAC,uJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,OAAO;4BACpB,YAAY,AAAC,MAAM,OAAO,GAAG,MAAM,aAAa,GAAI;sCAEpD,cAAA,8OAAC;gCACC,WAAU;gCACV,cAAY,CAAC,eAAe,EAAE,MAAM,OAAO,CAAC,KAAK,CAAC;;kDAElD,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAqC,MAAM,OAAO;;;;;;0DACjE,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;sCAK/C,8OAAC,uJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,YAAY;4BACzB,YAAY,AAAC,MAAM,YAAY,GAAG,MAAM,aAAa,GAAI;sCAEzD,cAAA,8OAAC;gCACC,WAAU;gCACV,cAAY,CAAC,oBAAoB,EAAE,MAAM,YAAY,CAAC,KAAK,CAAC;;kDAE5D,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAmC,MAAM,YAAY;;;;;;0DACpE,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC,uJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,SAAS;4BACtB,YAAY,AAAC,MAAM,SAAS,GAAG,MAAM,aAAa,GAAI;sCAEtD,cAAA,8OAAC;gCACC,WAAU;gCACV,cAAY,CAAC,gBAAgB,EAAE,MAAM,SAAS,CAAC,KAAK,CAAC;;kDAErD,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAmC,MAAM,SAAS;;;;;;0DACjE,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC,uJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,WAAW;4BACxB,YAAY,AAAC,MAAM,WAAW,GAAG,MAAM,aAAa,GAAI;sCAExD,cAAA,8OAAC;gCACC,WAAU;gCACV,cAAY,CAAC,kBAAkB,EAAE,MAAM,WAAW,CAAC,KAAK,CAAC;;kDAEzD,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAqC,MAAM,WAAW;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,8OAAC,uJAAA,CAAA,aAAU;4BACT,MAAK;4BACL,OAAO,MAAM,eAAe;sCAE5B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAqC,MAAM,eAAe,CAAC,cAAc;oDAAG;;;;;;;0DAC3F,8OAAC;gDAAI,WAAU;;oDAA0B;oDAAI,MAAM,WAAW,CAAC,cAAc;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQvF,MAAM,aAAa,GAAG,mBACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAK,WAAW,CAAC,UAAU,EAC1B,MAAM,wBAAwB,IAAI,KAAK,qBACvC,MAAM,wBAAwB,IAAI,KAAK,oBACvC,MAAM,wBAAwB,IAAI,KAAK,oBAAoB,kBAC3D;;wCACC,MAAM,wBAAwB,CAAC,OAAO,CAAC;wCAAG;;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAW,CAAC,yDAAyD,EACnE,MAAM,wBAAwB,IAAI,KAAK,qDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,MAAM,wBAAwB,IAAI,KAAK,mDACvC,gDACA;gCACF,OAAO;oCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,wBAAwB,EAAE,KAAK,CAAC,CAAC;gCAAC;;;;;;;;;;;sCAGxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;;wCAAM,MAAM,UAAU;wCAAC;wCAAK,MAAM,SAAS;wCAAC;wCAAK,MAAM,WAAW;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlF", "debugId": null}}, {"offset": {"line": 4610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/modals/CableActionModals.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport {\n  AlertTriangle,\n  FileText,\n  X,\n  Download,\n  AlertCircle,\n  CheckCircle,\n  Loader2,\n  Zap,\n  Lock,\n  HelpCircle\n} from 'lucide-react'\nimport { Cavo } from '@/types'\n\n// Types for modal props\ninterface BaseModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n}\n\ninterface DisconnectModalProps extends BaseModalProps {\n  onConfirm: (cavoId: string) => Promise<void>\n}\n\ninterface GeneratePdfModalProps extends BaseModalProps {\n  onGenerate: (cavoId: string, options: PdfGenerationOptions) => Promise<void>\n}\n\ninterface CertificationErrorModalProps extends BaseModalProps {\n  errorMessage?: string\n  missingRequirements?: string[]\n}\n\ninterface PdfGenerationOptions {\n  fileName: string\n  includeTestData: boolean\n  format: 'standard' | 'detailed'\n  emailRecipient?: string\n}\n\n// Enhanced Modal Header Component with prominent cable ID\ninterface EnhancedModalHeaderProps {\n  icon: React.ReactNode\n  title: string\n  cableId: string\n  description?: string\n}\n\nconst EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({\n  icon,\n  title,\n  cableId,\n  description\n}) => (\n  <DialogHeader>\n    <DialogTitle className=\"flex items-center gap-2\">\n      {icon}\n      <span className=\"flex items-center gap-2\">\n        {title}\n        <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold\">\n          {cableId}\n        </span>\n      </span>\n    </DialogTitle>\n    {description && (\n      <DialogDescription className=\"text-sm text-muted-foreground\">\n        {description}\n      </DialogDescription>\n    )}\n  </DialogHeader>\n)\n\n// Enhanced Dialog Content with improved overlay behavior\ninterface EnhancedDialogContentProps {\n  children: React.ReactNode\n  className?: string\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  ariaLabelledBy?: string\n  ariaDescribedBy?: string\n}\n\nconst EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({\n  children,\n  className = \"sm:max-w-md\",\n  onKeyDown,\n  ariaLabelledBy,\n  ariaDescribedBy\n}) => (\n  <DialogContent\n    className={className}\n    onKeyDown={onKeyDown}\n    aria-labelledby={ariaLabelledBy}\n    aria-describedby={ariaDescribedBy}\n    onPointerDownOutside={(e) => e.preventDefault()} // Prevent closing on outside click\n    onEscapeKeyDown={(e) => {\n      // Allow ESC key to close\n      if (onKeyDown) {\n        onKeyDown(e as any)\n      }\n    }}\n  >\n    {children}\n  </DialogContent>\n)\n\n// Disconnect Confirmation Modal with enhanced UI\nexport const DisconnectCableModal: React.FC<DisconnectModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onConfirm\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)\n\n  const handleInitialConfirm = () => {\n    setShowFinalConfirmation(true)\n  }\n\n  const handleFinalConfirm = async () => {\n    if (!cavo) return\n\n    setIsLoading(true)\n    try {\n      await onConfirm(cavo.id_cavo)\n      onClose()\n      setShowFinalConfirmation(false)\n    } catch (error) {\n      console.error('Error disconnecting cable:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleClose = () => {\n    setShowFinalConfirmation(false)\n    onClose()\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-md\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"disconnect-modal-title\"\n        ariaDescribedBy=\"disconnect-modal-description\"\n      >\n        <EnhancedModalHeader\n          icon={<Zap className=\"h-5 w-5 text-orange-500\" />}\n          title=\"Gestione Collegamenti\"\n          cableId={cavo.id_cavo}\n          description=\"Gestisci le connessioni del cavo selezionato\"\n        />\n\n        {!showFinalConfirmation ? (\n          <>\n            <div className=\"py-4\">\n              <div className=\"flex items-center gap-2 mb-4\">\n                <div className=\"flex items-center gap-1\">\n                  <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                  <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                </div>\n                <span className=\"text-sm font-medium text-green-700\">\n                  Completamente collegato\n                  <HelpCircle\n                    className=\"inline h-4 w-4 ml-1 cursor-help\"\n                    title=\"Cavo collegato sia all'origine che alla destinazione\"\n                  />\n                </span>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div>\n                  <Label htmlFor=\"responsabile-collegamento\" className=\"text-sm font-medium\">\n                    Responsabile Collegamento\n                  </Label>\n                  <select\n                    id=\"responsabile-collegamento\"\n                    className=\"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                    defaultValue=\"\"\n                  >\n                    <option value=\"\" disabled>Seleziona responsabile...</option>\n                    <option value=\"cantiere\">Cantiere</option>\n                    <option value=\"tecnico1\">Tecnico 1</option>\n                    <option value=\"tecnico2\">Tecnico 2</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            <Alert className=\"my-4 bg-orange-50 border-orange-200\">\n              <AlertTriangle className=\"h-4 w-4 text-orange-600\" />\n              <AlertDescription className=\"text-orange-800\">\n                <strong>Attenzione:</strong> Lo scollegamento rimuoverà tutte le connessioni attive del cavo.\n                Questa azione potrebbe influenzare altri componenti collegati.\n              </AlertDescription>\n            </Alert>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={handleClose}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-gray-50\"\n              >\n                Annulla\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleInitialConfirm}\n                disabled={isLoading}\n                className=\"flex-1 hover:bg-red-600\"\n              >\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                Scollega Completamente\n              </Button>\n            </DialogFooter>\n          </>\n        ) : (\n          <>\n            <div className=\"py-4 text-center\">\n              <div className=\"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4\">\n                <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                Conferma Scollegamento\n              </h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Sei veramente sicuro di voler scollegare completamente il cavo <strong>{cavo.id_cavo}</strong>?\n              </p>\n              <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n                <p className=\"text-sm text-red-800 font-medium\">\n                  ⚠️ Questa azione è irreversibile\n                </p>\n              </div>\n            </div>\n\n            <DialogFooter className=\"gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowFinalConfirmation(false)}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                No, Annulla\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleFinalConfirm}\n                disabled={isLoading}\n                className=\"flex-1\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Scollegando...\n                  </>\n                ) : (\n                  'Sì, Scollega'\n                )}\n              </Button>\n            </DialogFooter>\n          </>\n        )}\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Enhanced PDF Generation Modal\nexport const GeneratePdfModal: React.FC<GeneratePdfModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onGenerate\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [options, setOptions] = useState<PdfGenerationOptions>({\n    fileName: '',\n    includeTestData: true,\n    format: 'standard',\n    emailRecipient: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Set default filename when modal opens\n  useEffect(() => {\n    if (cavo && open) {\n      setOptions(prev => ({\n        ...prev,\n        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`\n      }))\n      setValidationErrors({})\n    }\n  }, [cavo, open])\n\n  // Validate form fields\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!options.fileName.trim()) {\n      errors.fileName = 'Il nome del file è obbligatorio'\n    } else if (!/^[a-zA-Z0-9_\\-\\s]+\\.pdf$/i.test(options.fileName)) {\n      errors.fileName = 'Il nome del file deve terminare con .pdf e contenere solo caratteri validi'\n    }\n\n    if (options.emailRecipient && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(options.emailRecipient)) {\n      errors.emailRecipient = 'Inserisci un indirizzo email valido'\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleGenerate = async () => {\n    if (!cavo || !validateForm()) return\n\n    setIsLoading(true)\n    try {\n      await onGenerate(cavo.id_cavo, options)\n      onClose()\n    } catch (error) {\n      console.error('Error generating PDF:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = options.fileName.trim() && Object.keys(validationErrors).length === 0\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"pdf-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<FileText className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Genera Certificato\"\n          cableId={cavo.id_cavo}\n          description=\"Configura le opzioni per la generazione del certificato PDF\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* File Name */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"fileName\" className=\"text-sm font-medium\">\n              Nome File *\n            </Label>\n            <Input\n              id=\"fileName\"\n              value={options.fileName}\n              onChange={(e) => {\n                setOptions(prev => ({ ...prev, fileName: e.target.value }))\n                if (validationErrors.fileName) {\n                  setValidationErrors(prev => ({ ...prev, fileName: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              placeholder=\"Certificato_C001_2025-06-29.pdf\"\n              className={validationErrors.fileName ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.fileName && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.fileName}\n              </p>\n            )}\n          </div>\n\n          {/* Format Selection */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Formato Certificato</Label>\n            <div className=\"space-y-2\">\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"standard\"\n                  checked={options.format === 'standard'}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Standard</span>\n                  <p className=\"text-xs text-gray-500\">Certificato con informazioni essenziali</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"detailed\"\n                  checked={options.format === 'detailed'}\n                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Dettagliato</span>\n                  <p className=\"text-xs text-gray-500\">Certificato con tutti i dati tecnici</p>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          {/* Include Test Data */}\n          <div className=\"flex items-center space-x-3 p-2 border rounded-md\">\n            <Checkbox\n              id=\"includeTestData\"\n              checked={options.includeTestData}\n              onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeTestData: checked as boolean }))}\n            />\n            <div>\n              <Label htmlFor=\"includeTestData\" className=\"text-sm font-medium cursor-pointer\">\n                Includi Dati di Collaudo\n              </Label>\n              <p className=\"text-xs text-gray-500\">Aggiunge i risultati dei test al certificato</p>\n            </div>\n          </div>\n\n          {/* Email Recipient (Optional) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"emailRecipient\" className=\"text-sm font-medium\">\n              Email Destinatario (Opzionale)\n            </Label>\n            <Input\n              id=\"emailRecipient\"\n              type=\"email\"\n              value={options.emailRecipient}\n              onChange={(e) => {\n                setOptions(prev => ({ ...prev, emailRecipient: e.target.value }))\n                if (validationErrors.emailRecipient) {\n                  setValidationErrors(prev => ({ ...prev, emailRecipient: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              placeholder=\"<EMAIL>\"\n              className={validationErrors.emailRecipient ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.emailRecipient && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.emailRecipient}\n              </p>\n            )}\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Generando...\n              </>\n            ) : (\n              <>\n                <Download className=\"mr-2 h-4 w-4\" />\n                Genera PDF\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Enhanced Certification Error Modal\nexport const CertificationErrorModal: React.FC<CertificationErrorModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  errorMessage,\n  missingRequirements = []\n}) => {\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const defaultRequirements = [\n    'Il cavo deve essere nello stato \"Installato\"',\n    'Il cavo deve essere completamente collegato',\n    'Tutti i dati di collaudo devono essere presenti'\n  ]\n\n  const requirements = missingRequirements.length > 0 ? missingRequirements : defaultRequirements\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-md\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"certification-error-title\"\n      >\n        <EnhancedModalHeader\n          icon={<AlertCircle className=\"h-5 w-5 text-red-500\" />}\n          title=\"Impossibile Certificare Cavo\"\n          cableId={cavo.id_cavo}\n          description=\"Il cavo non può essere certificato nel suo stato attuale\"\n        />\n\n        <div className=\"py-4\">\n          {errorMessage && (\n            <Alert className=\"mb-4 bg-red-50 border-red-200\">\n              <AlertCircle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">{errorMessage}</AlertDescription>\n            </Alert>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2\">\n                <AlertTriangle className=\"h-4 w-4 text-amber-500\" />\n                Requisiti mancanti:\n              </h4>\n              <ul className=\"space-y-3\">\n                {requirements.map((requirement, index) => (\n                  <li key={index} className=\"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md\">\n                    <div className=\"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5\">\n                      <X className=\"h-3 w-3 text-red-600\" />\n                    </div>\n                    <span className=\"text-sm text-red-800\">{requirement}</span>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <Alert className=\"bg-blue-50 border-blue-200\">\n              <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n              <AlertDescription className=\"text-blue-800\">\n                <strong>Prossimi passi:</strong> Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo.\n              </AlertDescription>\n            </Alert>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button\n            onClick={onClose}\n            className=\"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500\"\n          >\n            <CheckCircle className=\"mr-2 h-4 w-4\" />\n            Ho Capito\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Cable Certification Modal\ninterface CertificationModalProps extends BaseModalProps {\n  onCertify: (cavoId: string, certificationData: CertificationData) => Promise<void>\n}\n\ninterface CertificationData {\n  responsabile: string\n  dataCertificazione: string\n  esitoCertificazione: 'CONFORME' | 'NON_CONFORME' | 'PARZIALMENTE_CONFORME'\n  note?: string\n}\n\nexport const CertificationModal: React.FC<CertificationModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onCertify\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [formData, setFormData] = useState<CertificationData>({\n    responsabile: '',\n    dataCertificazione: new Date().toISOString().split('T')[0],\n    esitoCertificazione: 'CONFORME',\n    note: ''\n  })\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})\n\n  // Reset form when modal opens\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        responsabile: '',\n        dataCertificazione: new Date().toISOString().split('T')[0],\n        esitoCertificazione: 'CONFORME',\n        note: ''\n      })\n      setValidationErrors({})\n    }\n  }, [open, cavo])\n\n  const validateForm = () => {\n    const errors: Record<string, string> = {}\n\n    if (!formData.responsabile.trim()) {\n      errors.responsabile = 'Il responsabile è obbligatorio'\n    }\n\n    if (!formData.dataCertificazione) {\n      errors.dataCertificazione = 'La data di certificazione è obbligatoria'\n    } else {\n      const selectedDate = new Date(formData.dataCertificazione)\n      const today = new Date()\n      today.setHours(0, 0, 0, 0)\n\n      if (selectedDate > today) {\n        errors.dataCertificazione = 'La data non può essere futura'\n      }\n    }\n\n    setValidationErrors(errors)\n    return Object.keys(errors).length === 0\n  }\n\n  const handleCertify = async () => {\n    if (!cavo || !validateForm()) return\n\n    setIsLoading(true)\n    try {\n      await onCertify(cavo.id_cavo, formData)\n      onClose()\n    } catch (error) {\n      console.error('Error certifying cable:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n  }\n\n  const isFormValid = formData.responsabile.trim() && formData.dataCertificazione && Object.keys(validationErrors).length === 0\n\n  if (!cavo) return null\n\n  // Mock cable status for demonstration\n  const cableStatus = {\n    installato: true,\n    collegato: true,\n    certificato: false\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"certification-modal-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Lock className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Gestione Certificazione\"\n          cableId={cavo.id_cavo}\n          description=\"Certifica il cavo dopo aver completato tutti i controlli\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          {/* Cable Status Indicators */}\n          <div className=\"space-y-3\">\n            <h4 className=\"text-sm font-semibold text-gray-900\">Stato Cavo:</h4>\n            <div className=\"grid grid-cols-3 gap-3\">\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.installato ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.installato ? '✓' : '✗'} Installato\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.collegato ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.collegato ? '✓' : '✗'} Collegato\n                </span>\n              </div>\n              <div className=\"flex items-center gap-2 p-2 bg-gray-50 rounded-md\">\n                <div className={`w-3 h-3 rounded-full ${cableStatus.certificato ? 'bg-green-500' : 'bg-orange-500'}`}></div>\n                <span className=\"text-sm font-medium\">\n                  {cableStatus.certificato ? '✓' : '⚠'} Non certificato\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Responsabile Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\" className=\"text-sm font-medium\">\n              Responsabile Certificazione *\n            </Label>\n            <select\n              id=\"responsabile\"\n              value={formData.responsabile}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, responsabile: e.target.value }))\n                if (validationErrors.responsabile) {\n                  setValidationErrors(prev => ({ ...prev, responsabile: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              className={`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${\n                validationErrors.responsabile ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\" disabled>Seleziona responsabile...</option>\n              <option value=\"cantiere\">Cantiere</option>\n              <option value=\"tecnico_1\">Tecnico 1</option>\n              <option value=\"tecnico_2\">Tecnico 2</option>\n              <option value=\"supervisore\">Supervisore</option>\n            </select>\n            {validationErrors.responsabile && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.responsabile}\n              </p>\n            )}\n          </div>\n\n          {/* Data Certificazione */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"dataCertificazione\" className=\"text-sm font-medium\">\n              Data Certificazione *\n            </Label>\n            <Input\n              id=\"dataCertificazione\"\n              type=\"date\"\n              value={formData.dataCertificazione}\n              onChange={(e) => {\n                setFormData(prev => ({ ...prev, dataCertificazione: e.target.value }))\n                if (validationErrors.dataCertificazione) {\n                  setValidationErrors(prev => ({ ...prev, dataCertificazione: '' }))\n                }\n              }}\n              onBlur={validateForm}\n              max={new Date().toISOString().split('T')[0]}\n              className={validationErrors.dataCertificazione ? 'border-red-500 focus:ring-red-500' : ''}\n            />\n            {validationErrors.dataCertificazione && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertCircle className=\"h-3 w-3\" />\n                {validationErrors.dataCertificazione}\n              </p>\n            )}\n          </div>\n\n          {/* Esito Certificazione */}\n          <div className=\"space-y-2\">\n            <Label className=\"text-sm font-medium\">Esito Certificazione</Label>\n            <select\n              value={formData.esitoCertificazione}\n              onChange={(e) => setFormData(prev => ({ ...prev, esitoCertificazione: e.target.value as any }))}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"CONFORME\">CONFORME</option>\n              <option value=\"NON_CONFORME\">NON CONFORME</option>\n              <option value=\"PARZIALMENTE_CONFORME\">PARZIALMENTE CONFORME</option>\n            </select>\n          </div>\n\n          {/* Note (opzionale) */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\" className=\"text-sm font-medium\">\n              Note (opzionale)\n            </Label>\n            <textarea\n              id=\"note\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              placeholder=\"Inserisci eventuali note sulla certificazione...\"\n              rows={3}\n              className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\n            />\n          </div>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Chiudi\n          </Button>\n          <Button\n            onClick={handleCertify}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Certificando...\n              </>\n            ) : (\n              <>\n                <Lock className=\"mr-2 h-4 w-4\" />\n                Certifica Cavo\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Success Toast Component (for feedback after actions)\ninterface SuccessToastProps {\n  message: string\n  visible: boolean\n  onClose: () => void\n}\n\nexport const SuccessToast: React.FC<SuccessToastProps> = ({\n  message,\n  visible,\n  onClose\n}) => {\n  useEffect(() => {\n    if (visible) {\n      const timer = setTimeout(() => {\n        onClose()\n      }, 3000)\n      return () => clearTimeout(timer)\n    }\n  }, [visible, onClose])\n\n  if (!visible) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2\">\n      <Alert className=\"bg-green-50 border-green-200 text-green-800 shadow-lg\">\n        <CheckCircle className=\"h-4 w-4\" />\n        <AlertDescription className=\"font-medium\">\n          {message}\n        </AlertDescription>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100\"\n          onClick={onClose}\n        >\n          <X className=\"h-3 w-3\" />\n        </Button>\n      </Alert>\n    </div>\n  )\n}\n\n// Export all modal types for easy importing\nexport type {\n  DisconnectModalProps,\n  GeneratePdfModalProps,\n  CertificationErrorModalProps,\n  CertificationModalProps,\n  PdfGenerationOptions,\n  CertificationData\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;AAiEA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,8OAAC,kIAAA,CAAA,eAAY;;0BACX,8OAAC,kIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB;kCACD,8OAAC;wBAAK,WAAU;;4BACb;0CACD,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAIN,6BACC,8OAAC,kIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;;;;;;;AAeT,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,aAAa,EACzB,SAAS,EACT,cAAc,EACd,eAAe,EAChB,iBACC,8OAAC,kIAAA,CAAA,gBAAa;QACZ,WAAW;QACX,WAAW;QACX,mBAAiB;QACjB,oBAAkB;QAClB,sBAAsB,CAAC,IAAM,EAAE,cAAc;QAC7C,iBAAiB,CAAC;YAChB,yBAAyB;YACzB,IAAI,WAAW;gBACb,UAAU;YACZ;QACF;kBAEC;;;;;;AAKE,MAAM,uBAAuD,CAAC,EACnE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,MAAM,uBAAuB;QAC3B,yBAAyB;IAC3B;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO;YAC5B;YACA,yBAAyB;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,yBAAyB;QACzB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;YACf,iBAAgB;;8BAEhB,8OAAC;oBACC,oBAAM,8OAAC,gMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;oBACrB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;gBAGb,CAAC,sCACA;;sCACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC;4CAAK,WAAU;;gDAAqC;8DAEnD,8OAAC,kNAAA,CAAA,aAAU;oDACT,WAAU;oDACV,OAAM;;;;;;;;;;;;;;;;;;8CAKZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAA4B,WAAU;0DAAsB;;;;;;0DAG3E,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,cAAa;;kEAEb,8OAAC;wDAAO,OAAM;wDAAG,QAAQ;kEAAC;;;;;;kEAC1B,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;kEACzB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;sCAKhC,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;iDAMhD;;sCACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;;wCAA6B;sDACuB,8OAAC;sDAAQ,KAAK,OAAO;;;;;;wCAAU;;;;;;;8CAEhG,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;sCAMpD,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,yBAAyB;oCACxC,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;AAGO,MAAM,mBAAoD,CAAC,EAChE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QAC3D,UAAU;QACV,iBAAiB;QACjB,QAAQ;QACR,gBAAgB;IAClB;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,WAAW,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,UAAU,CAAC,YAAY,EAAE,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBACvF,CAAC;YACD,oBAAoB,CAAC;QACvB;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,QAAQ,QAAQ,CAAC,IAAI,IAAI;YAC5B,OAAO,QAAQ,GAAG;QACpB,OAAO,IAAI,CAAC,4BAA4B,IAAI,CAAC,QAAQ,QAAQ,GAAG;YAC9D,OAAO,QAAQ,GAAG;QACpB;QAEA,IAAI,QAAQ,cAAc,IAAI,CAAC,6BAA6B,IAAI,CAAC,QAAQ,cAAc,GAAG;YACxF,OAAO,cAAc,GAAG;QAC1B;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,KAAK,OAAO,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,QAAQ,QAAQ,CAAC,IAAI,MAAM,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAExF,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAsB;;;;;;8CAG1D,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,QAAQ,QAAQ;oCACvB,UAAU,CAAC;wCACT,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACzD,IAAI,iBAAiB,QAAQ,EAAE;4CAC7B,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU;gDAAG,CAAC;wCACxD;oCACF;oCACA,QAAQ;oCACR,aAAY;oCACZ,WAAW,iBAAiB,QAAQ,GAAG,sCAAsC;;;;;;gCAE9E,iBAAiB,QAAQ,kBACxB,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,QAAQ;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,QAAQ,MAAM,KAAK;oDAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAA4B,CAAC;oDACnG,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,QAAQ,MAAM,KAAK;oDAC5B,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4DAA4B,CAAC;oDACnG,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,SAAS,QAAQ,eAAe;oCAChC,iBAAiB,CAAC,UAAY,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,iBAAiB;4CAAmB,CAAC;;;;;;8CAEpG,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAqC;;;;;;sDAGhF,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;8CAAsB;;;;;;8CAGhE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,QAAQ,cAAc;oCAC7B,UAAU,CAAC;wCACT,WAAW,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC/D,IAAI,iBAAiB,cAAc,EAAE;4CACnC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,gBAAgB;gDAAG,CAAC;wCAC9D;oCACF;oCACA,QAAQ;oCACR,aAAY;oCACZ,WAAW,iBAAiB,cAAc,GAAG,sCAAsC;;;;;;gCAEpF,iBAAiB,cAAc,kBAC9B,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;8BAMxC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,kCAAkC,qBAAqB;sCAE1F,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;AAGO,MAAM,0BAAkE,CAAC,EAC9E,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,sBAAsB,EAAE,EACzB;IACC,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,sBAAsB;QAC1B;QACA;QACA;KACD;IAED,MAAM,eAAe,oBAAoB,MAAM,GAAG,IAAI,sBAAsB;IAE5E,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAC7B,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;wBACZ,8BACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAAgB;;;;;;;;;;;;sCAIhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAA2B;;;;;;;sDAGtD,8OAAC;4CAAG,WAAU;sDACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;mDAJjC;;;;;;;;;;;;;;;;8CAUf,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;sDACf,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;;8DAC1B,8OAAC;8DAAO;;;;;;gDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOpD;AAcO,MAAM,qBAAwD,CAAC,EACpE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1D,cAAc;QACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,qBAAqB;QACrB,MAAM;IACR;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,YAAY;gBACV,cAAc;gBACd,oBAAoB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1D,qBAAqB;gBACrB,MAAM;YACR;YACA,oBAAoB,CAAC;QACvB;IACF,GAAG;QAAC;QAAM;KAAK;IAEf,MAAM,eAAe;QACnB,MAAM,SAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YACjC,OAAO,YAAY,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,kBAAkB,EAAE;YAChC,OAAO,kBAAkB,GAAG;QAC9B,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,SAAS,kBAAkB;YACzD,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YAExB,IAAI,eAAe,OAAO;gBACxB,OAAO,kBAAkB,GAAG;YAC9B;QACF;QAEA,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,UAAU,KAAK,OAAO,EAAE;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc,SAAS,YAAY,CAAC,IAAI,MAAM,SAAS,kBAAkB,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,KAAK;IAE5H,IAAI,CAAC,MAAM,OAAO;IAElB,sCAAsC;IACtC,MAAM,cAAc;QAClB,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,UAAU,GAAG,iBAAiB,cAAc;;;;;;8DAChG,8OAAC;oDAAK,WAAU;;wDACb,YAAY,UAAU,GAAG,MAAM;wDAAI;;;;;;;;;;;;;sDAGxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,SAAS,GAAG,iBAAiB,cAAc;;;;;;8DAC/F,8OAAC;oDAAK,WAAU;;wDACb,YAAY,SAAS,GAAG,MAAM;wDAAI;;;;;;;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,qBAAqB,EAAE,YAAY,WAAW,GAAG,iBAAiB,iBAAiB;;;;;;8DACpG,8OAAC;oDAAK,WAAU;;wDACb,YAAY,WAAW,GAAG,MAAM;wDAAI;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAsB;;;;;;8CAG9D,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,YAAY;oCAC5B,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCAC9D,IAAI,iBAAiB,YAAY,EAAE;4CACjC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc;gDAAG,CAAC;wCAC5D;oCACF;oCACA,QAAQ;oCACR,WAAW,CAAC,oFAAoF,EAC9F,iBAAiB,YAAY,GAAG,mBAAmB,mBACnD;;sDAEF,8OAAC;4CAAO,OAAM;4CAAG,QAAQ;sDAAC;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,8OAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;gCAE7B,iBAAiB,YAAY,kBAC5B,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,YAAY;;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAqB,WAAU;8CAAsB;;;;;;8CAGpE,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,kBAAkB;oCAClC,UAAU,CAAC;wCACT,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;wCACpE,IAAI,iBAAiB,kBAAkB,EAAE;4CACvC,oBAAoB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,oBAAoB;gDAAG,CAAC;wCAClE;oCACF;oCACA,QAAQ;oCACR,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oCAC3C,WAAW,iBAAiB,kBAAkB,GAAG,sCAAsC;;;;;;gCAExF,iBAAiB,kBAAkB,kBAClC,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCACtB,iBAAiB,kBAAkB;;;;;;;;;;;;;sCAM1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCACC,OAAO,SAAS,mBAAmB;oCACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CAAQ,CAAC;oCAC7F,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAW;;;;;;sDACzB,8OAAC;4CAAO,OAAM;sDAAe;;;;;;sDAC7B,8OAAC;4CAAO,OAAM;sDAAwB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,WAAU;8CAAsB;;;;;;8CAGtD,8OAAC;oCACC,IAAG;oCACH,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,8CAA8C,iCAAiC;sCAElH,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;AASO,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,OAAO,EACP,OAAO,EACR;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAS;KAAQ;IAErB,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAU;;8BACf,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC,iIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BACzB;;;;;;8BAEH,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;8BAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 6522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/modals/BobinaManagementModals.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport {\n  Search,\n  CheckCircle,\n  AlertTriangle,\n  Ruler,\n  Package,\n  X,\n  Loader2,\n  HelpCircle\n} from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\n// Enhanced Modal Components (imported from CableActionModals)\ninterface EnhancedModalHeaderProps {\n  icon: React.ReactNode\n  title: string\n  cableId: string\n  description?: string\n}\n\nconst EnhancedModalHeader: React.FC<EnhancedModalHeaderProps> = ({\n  icon,\n  title,\n  cableId,\n  description\n}) => (\n  <DialogHeader>\n    <DialogTitle className=\"flex items-center gap-2\">\n      {icon}\n      <span className=\"flex items-center gap-2\">\n        {title}\n        <span className=\"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold\">\n          {cableId}\n        </span>\n      </span>\n    </DialogTitle>\n    {description && (\n      <DialogDescription className=\"text-sm text-muted-foreground\">\n        {description}\n      </DialogDescription>\n    )}\n  </DialogHeader>\n)\n\ninterface EnhancedDialogContentProps {\n  children: React.ReactNode\n  className?: string\n  onKeyDown?: (e: React.KeyboardEvent) => void\n  ariaLabelledBy?: string\n  ariaDescribedBy?: string\n}\n\nconst EnhancedDialogContent: React.FC<EnhancedDialogContentProps> = ({\n  children,\n  className = \"sm:max-w-md\",\n  onKeyDown,\n  ariaLabelledBy,\n  ariaDescribedBy\n}) => (\n  <DialogContent \n    className={className}\n    onKeyDown={onKeyDown}\n    aria-labelledby={ariaLabelledBy}\n    aria-describedby={ariaDescribedBy}\n    onPointerDownOutside={(e) => e.preventDefault()}\n    onEscapeKeyDown={(e) => {\n      if (onKeyDown) {\n        onKeyDown(e as any)\n      }\n    }}\n  >\n    {children}\n  </DialogContent>\n)\n\n// Types\ninterface Bobina {\n  id_bobina: string\n  numero_bobina: string\n  tipologia: string\n  n_conduttori: string\n  sezione: string\n  metri_totali: number\n  metri_residui: number\n  stato_bobina: string\n  fornitore?: string\n  compatible?: boolean\n}\n\ninterface ModificaBobinaModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSave: (cavoId: string, bobinaId: string, option: string) => Promise<void>\n}\n\ninterface InserisciMetriModalProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  onSave: (cavoId: string, metriPosati: number) => Promise<void>\n}\n\n// Cable Info Card Component\ninterface CableInfoCardProps {\n  cavo: Cavo\n}\n\nconst CableInfoCard: React.FC<CableInfoCardProps> = ({ cavo }) => (\n  <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3\">\n    <div className=\"flex items-center gap-2 mb-3\">\n      <Package className=\"h-5 w-5 text-blue-600\" />\n      <h3 className=\"font-semibold text-blue-800\">Informazioni Cavo {cavo.id_cavo}</h3>\n    </div>\n\n    <div className=\"grid grid-cols-2 gap-3 text-sm\">\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Tipologia</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.tipologia || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Formazione</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.sezione || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Da</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.ubicazione_partenza || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">A</span>\n        <span className=\"text-gray-900 font-medium\">{cavo.ubicazione_arrivo || 'N/A'}</span>\n      </div>\n\n      <div className=\"flex flex-col col-span-2\">\n        <span className=\"text-gray-600 text-xs font-medium uppercase tracking-wide\">Metri Posati</span>\n        <span className=\"text-blue-600 font-bold text-lg\">{cavo.metratura_reale || 0} m</span>\n      </div>\n    </div>\n  </div>\n)\n\n// Modifica Bobina Modal\nexport const ModificaBobinaModal: React.FC<ModificaBobinaModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onSave\n}) => {\n  const { cantiere } = useAuth()\n  const [isLoading, setIsLoading] = useState(false)\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [selectedOption, setSelectedOption] = useState('')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [activeTab, setActiveTab] = useState<'compatible' | 'incompatible'>('compatible')\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [error, setError] = useState('')\n\n  // Carica bobine reali dall'API - COPIA ESATTA DA InserisciMetriDialog\n  const loadBobine = async () => {\n    console.log('🎯 ModificaBobinaModal: Caricamento bobine:', {\n      cavo: !!cavo,\n      cantiere: !!cantiere,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo || !cantiere) {\n      return\n    }\n\n    try {\n      setLoadingBobine(true)\n\n      // Carica tutte le bobine disponibili - STESSA CHIAMATA API\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      // Gestisce diversi formati di risposta - STESSA LOGICA\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0 - STESSA LOGICA\n      const bobineUtilizzabili = bobineData.filter((bobina: any) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      if (cavo) {\n        console.log('🔍 ModificaBobinaModal: Filtro per cavo:', {\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        })\n\n        // Determina compatibilità basata su tipologia e sezione\n        const bobineWithCompatibility = bobineUtilizzabili.map((bobina: any) => ({\n          ...bobina,\n          compatible: bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n        }))\n\n        setBobine(bobineWithCompatibility)\n        console.log('✅ ModificaBobinaModal: Bobine caricate:', bobineWithCompatibility.length)\n      } else {\n        setBobine([])\n      }\n    } catch (error: any) {\n      console.error('Errore caricamento bobine:', error)\n      setError('Errore durante il caricamento delle bobine')\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  // Carica bobine quando si apre il modal - STESSA LOGICA DI InserisciMetriDialog\n  useEffect(() => {\n    if (open && cavo) {\n      if (cantiere) {\n        loadBobine()\n      } else {\n        console.log('❌ ModificaBobinaModal: Cantiere non disponibile')\n      }\n\n      // Reset stati\n      setSelectedOption('')\n      setSelectedBobina('')\n      setSearchTerm('')\n      setActiveTab('compatible')\n      setError('')\n    }\n  }, [open, cavo, cantiere])\n\n  const filteredBobine = bobine.filter(bobina => {\n    const matchesSearch = bobina.numero_bobina?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         bobina.tipologia?.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesTab = activeTab === 'compatible' ? bobina.compatible : !bobina.compatible\n    return matchesSearch && matchesTab\n  })\n\n  const compatibleCount = bobine.filter(b => b.compatible).length\n  const incompatibleCount = bobine.filter(b => !b.compatible).length\n\n  console.log('🔍 ModificaBobinaModal: Stato filtri', {\n    totaleBobine: bobine.length,\n    compatibili: compatibleCount,\n    incompatibili: incompatibleCount,\n    filtrate: filteredBobine.length,\n    activeTab,\n    searchTerm\n  })\n\n  const handleSave = async () => {\n    if (!cavo || !selectedOption) return\n\n    setIsLoading(true)\n    try {\n      await onSave(cavo.id_cavo, selectedBobina, selectedOption)\n      handleClose()\n    } catch (error) {\n      console.error('Error saving bobina modification:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      handleClose()\n    }\n  }\n\n  const handleClose = () => {\n    // Reset stati quando si chiude il modal\n    setSelectedOption('')\n    setSelectedBobina('')\n    setSearchTerm('')\n    setActiveTab('compatible')\n    setError('')\n    setBobine([])\n    onClose()\n  }\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-3xl max-h-[85vh] overflow-hidden\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"modifica-bobina-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Package className=\"h-5 w-5 text-blue-500\" />}\n          title=\"Modifica Bobina Cavo\"\n          cableId={cavo.id_cavo}\n          description=\"Seleziona una nuova bobina per il cavo o modifica i parametri\"\n        />\n\n        <div className=\"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]\">\n          <CableInfoCard cavo={cavo} />\n\n          {/* Messaggio di errore */}\n          {error && (\n            <Alert className=\"bg-red-50 border-red-200\">\n              <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">\n                {error}\n              </AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di modifica */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-semibold\">Opzioni di modifica</Label>\n            <div className=\"grid grid-cols-1 gap-2\">\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"modifica-option\"\n                  value=\"cambia-bobina\"\n                  checked={selectedOption === 'cambia-bobina'}\n                  onChange={(e) => setSelectedOption(e.target.value)}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Cambia bobina</span>\n                  <p className=\"text-xs text-gray-500\">Assegna una bobina diversa al cavo</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"modifica-option\"\n                  value=\"bobina-vuota\"\n                  checked={selectedOption === 'bobina-vuota'}\n                  onChange={(e) => setSelectedOption(e.target.value)}\n                  className=\"text-blue-600 focus:ring-blue-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium\">Bobina vuota</span>\n                  <p className=\"text-xs text-gray-500\">Rimuovi l'associazione con la bobina attuale</p>\n                </div>\n              </label>\n              <label className=\"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors\">\n                <input\n                  type=\"radio\"\n                  name=\"modifica-option\"\n                  value=\"annulla-posa\"\n                  checked={selectedOption === 'annulla-posa'}\n                  onChange={(e) => setSelectedOption(e.target.value)}\n                  className=\"text-red-600 focus:ring-red-500\"\n                />\n                <div>\n                  <span className=\"text-sm font-medium text-red-700\">Annulla posa</span>\n                  <p className=\"text-xs text-red-500\">Annulla l'installazione e restituisci i metri alla bobina</p>\n                </div>\n              </label>\n            </div>\n          </div>\n\n          {selectedOption === 'cambia-bobina' && (\n            <>\n              {/* Campo di ricerca */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"search-bobina\" className=\"text-sm font-medium\">\n                  Cerca bobina\n                </Label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <Input\n                    id=\"search-bobina\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder=\"Cerca bobina per ID, tipologia o numero...\"\n                    className=\"pl-10 h-8\"\n                  />\n                </div>\n              </div>\n\n              {/* Tabs per bobine compatibili/incompatibili */}\n              <div className=\"space-y-2\">\n                <div className=\"flex border-b\">\n                  <button\n                    onClick={() => setActiveTab('compatible')}\n                    className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'compatible'\n                        ? 'border-green-500 text-green-700 bg-green-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <CheckCircle className=\"h-3 w-3 text-green-500\" />\n                    Compatibili ({compatibleCount})\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('incompatible')}\n                    className={`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'incompatible'\n                        ? 'border-yellow-500 text-yellow-700 bg-yellow-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <AlertTriangle className=\"h-3 w-3 text-yellow-500\" />\n                    Incompatibili ({incompatibleCount})\n                  </button>\n                </div>\n\n                {/* Lista bobine - Ridotta altezza per mostrare almeno 3 bobine */}\n                <div className=\"h-32 overflow-y-auto border rounded-md bg-gray-50\">\n                  {loadingBobine ? (\n                    <div className=\"p-3 text-center\">\n                      <Loader2 className=\"h-6 w-6 text-blue-500 mx-auto mb-2 animate-spin\" />\n                      <p className=\"text-xs text-gray-600\">Caricamento bobine...</p>\n                    </div>\n                  ) : filteredBobine.length === 0 ? (\n                    <div className=\"p-3 text-center\">\n                      <div className=\"bg-yellow-50 border border-yellow-200 rounded-md p-3\">\n                        <AlertTriangle className=\"h-6 w-6 text-yellow-500 mx-auto mb-1\" />\n                        <p className=\"text-xs text-yellow-800 font-medium mb-1\">\n                          Nessuna bobina {activeTab === 'compatible' ? 'compatibile' : 'incompatibile'} trovata\n                        </p>\n                        <p className=\"text-xs text-yellow-700\">\n                          Prova a modificare i criteri di ricerca\n                        </p>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"space-y-1 p-1\">\n                      {filteredBobine.map((bobina) => (\n                        <label\n                          key={bobina.id_bobina}\n                          className=\"flex items-center space-x-2 p-2 border rounded-md hover:bg-white cursor-pointer transition-colors bg-white\"\n                        >\n                          <input\n                            type=\"radio\"\n                            name=\"selected-bobina\"\n                            value={bobina.id_bobina}\n                            checked={selectedBobina === bobina.id_bobina}\n                            onChange={(e) => setSelectedBobina(e.target.value)}\n                            className=\"text-blue-600 focus:ring-blue-500 w-3 h-3\"\n                          />\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center gap-1\">\n                              <span className=\"font-medium text-xs truncate\">{bobina.numero_bobina}</span>\n                              {bobina.compatible ? (\n                                <CheckCircle className=\"h-3 w-3 text-green-500 flex-shrink-0\" />\n                              ) : (\n                                <AlertTriangle className=\"h-3 w-3 text-yellow-500 flex-shrink-0\" />\n                              )}\n                            </div>\n                            <p className=\"text-xs text-gray-500 truncate\">\n                              {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m\n                            </p>\n                          </div>\n                        </label>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </>\n          )}\n\n          {/* Messaggio di conferma per annulla posa */}\n          {selectedOption === 'annulla-posa' && (\n            <Alert className=\"bg-red-50 border-red-200\">\n              <AlertTriangle className=\"h-4 w-4 text-red-600\" />\n              <AlertDescription className=\"text-red-800\">\n                <strong>ATTENZIONE:</strong> Questa operazione annullerà completamente l'installazione del cavo.\n                Tutti i metri posati saranno restituiti alla bobina originale e lo stato del cavo sarà resettato a \"Da installare\".\n              </AlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <DialogFooter className=\"gap-2 pt-4 border-t\">\n          <Button\n            variant=\"outline\"\n            onClick={handleClose}\n            disabled={isLoading}\n            className=\"px-6 py-2 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={isLoading || !selectedOption || (selectedOption === 'cambia-bobina' && !selectedBobina)}\n            className={`px-6 py-2 ${\n              selectedOption === 'annulla-posa'\n                ? 'bg-red-600 hover:bg-red-700 text-white'\n                : 'bg-blue-600 hover:bg-blue-700 text-white'\n            }`}\n            variant={selectedOption === 'annulla-posa' ? 'destructive' : 'default'}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Salvando...\n              </>\n            ) : selectedOption === 'annulla-posa' ? (\n              <>\n                <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                Annulla Posa\n              </>\n            ) : (\n              'Salva Modifiche'\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Inserisci Metri Posati Modal\nexport const InserisciMetriModal: React.FC<InserisciMetriModalProps> = ({\n  open,\n  onClose,\n  cavo,\n  onSave\n}) => {\n  const [isLoading, setIsLoading] = useState(false)\n  const [metriPosati, setMetriPosati] = useState('')\n  const [validationError, setValidationError] = useState('')\n\n  // Mock data for demonstration\n  const metriDaInstallare = 150\n  const metriGiaInstallati = 75\n\n  useEffect(() => {\n    if (open) {\n      setMetriPosati('')\n      setValidationError('')\n    }\n  }, [open])\n\n  const validateInput = (value: string) => {\n    const numValue = parseFloat(value)\n\n    if (!value.trim()) {\n      return 'Il campo metri posati è obbligatorio'\n    }\n\n    if (isNaN(numValue) || numValue <= 0) {\n      return 'Inserisci un valore numerico valido maggiore di 0'\n    }\n\n    if (numValue > metriDaInstallare) {\n      return `I metri posati non possono superare i metri da installare (${metriDaInstallare}m)`\n    }\n\n    return ''\n  }\n\n  const handleInputChange = (value: string) => {\n    setMetriPosati(value)\n    const error = validateInput(value)\n    setValidationError(error)\n  }\n\n  const handleSave = async () => {\n    if (!cavo) return\n\n    const error = validateInput(metriPosati)\n    if (error) {\n      setValidationError(error)\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      await onSave(cavo.id_cavo, parseFloat(metriPosati))\n      onClose()\n    } catch (error) {\n      console.error('Error saving metri posati:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      onClose()\n    }\n    if (e.key === 'Enter' && !validationError && metriPosati.trim()) {\n      handleSave()\n    }\n  }\n\n  const isFormValid = metriPosati.trim() && !validationError\n\n  if (!cavo) return null\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <EnhancedDialogContent\n        className=\"sm:max-w-lg\"\n        onKeyDown={handleKeyDown}\n        ariaLabelledBy=\"inserisci-metri-title\"\n      >\n        <EnhancedModalHeader\n          icon={<Ruler className=\"h-5 w-5 text-green-500\" />}\n          title=\"Inserisci Metri Posati\"\n          cableId={cavo.id_cavo}\n          description=\"Registra i metri di cavo effettivamente posati\"\n        />\n\n        <div className=\"space-y-4 py-4\">\n          <CableInfoCard cavo={cavo} />\n\n          {/* Box informativi */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <Ruler className=\"h-4 w-4 text-blue-600\" />\n                <span className=\"text-sm font-semibold text-blue-900\">Metri da Installare</span>\n              </div>\n              <p className=\"text-2xl font-bold text-blue-700\">{metriDaInstallare}m</p>\n              <p className=\"text-xs text-blue-600\">Lunghezza totale prevista</p>\n            </div>\n\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n              <div className=\"flex items-center gap-2 mb-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                <span className=\"text-sm font-semibold text-green-900\">Già Installati</span>\n              </div>\n              <p className=\"text-2xl font-bold text-green-700\">{metriGiaInstallati}m</p>\n              <p className=\"text-xs text-green-600\">Precedentemente registrati</p>\n            </div>\n          </div>\n\n          {/* Campo input metri posati */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"metri-posati\" className=\"text-sm font-medium flex items-center gap-2\">\n              <Ruler className=\"h-4 w-4 text-gray-600\" />\n              Metri Posati *\n            </Label>\n            <div className=\"relative\">\n              <Input\n                id=\"metri-posati\"\n                type=\"number\"\n                value={metriPosati}\n                onChange={(e) => handleInputChange(e.target.value)}\n                placeholder=\"Inserisci i metri posati...\"\n                min=\"0\"\n                max={metriDaInstallare}\n                step=\"0.1\"\n                className={`pr-12 ${validationError ? 'border-red-500 focus:ring-red-500' : 'focus:ring-green-500'}`}\n              />\n              <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-gray-500\">\n                m\n              </span>\n            </div>\n            {validationError && (\n              <p className=\"text-sm text-red-600 flex items-center gap-1\">\n                <AlertTriangle className=\"h-3 w-3\" />\n                {validationError}\n              </p>\n            )}\n            <p className=\"text-xs text-gray-500\">\n              Rimanenti da installare: {Math.max(0, metriDaInstallare - metriGiaInstallati - (parseFloat(metriPosati) || 0))}m\n            </p>\n          </div>\n\n          {/* Informazioni aggiuntive */}\n          <Alert className=\"bg-blue-50 border-blue-200\">\n            <HelpCircle className=\"h-4 w-4 text-blue-600\" />\n            <AlertDescription className=\"text-blue-800\">\n              <strong>Suggerimento:</strong> Assicurati di misurare accuratamente i metri posati.\n              Questo valore verrà utilizzato per calcolare il progresso dell'installazione.\n            </AlertDescription>\n          </Alert>\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={isLoading}\n            className=\"flex-1 hover:bg-gray-50\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={isLoading || !isFormValid}\n            className={`flex-1 ${!isFormValid ? 'opacity-50 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                Salvando...\n              </>\n            ) : (\n              <>\n                <CheckCircle className=\"mr-2 h-4 w-4\" />\n                Salva Metri\n              </>\n            )}\n          </Button>\n        </DialogFooter>\n      </EnhancedDialogContent>\n    </Dialog>\n  )\n}\n\n// Export types\nexport type {\n  ModificaBobinaModalProps,\n  InserisciMetriModalProps,\n  Bobina\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AA3BA;;;;;;;;;;;AAqCA,MAAM,sBAA0D,CAAC,EAC/D,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACZ,iBACC,8OAAC,kIAAA,CAAA,eAAY;;0BACX,8OAAC,kIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB;kCACD,8OAAC;wBAAK,WAAU;;4BACb;0CACD,8OAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;;YAIN,6BACC,8OAAC,kIAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC1B;;;;;;;;;;;;AAcT,MAAM,wBAA8D,CAAC,EACnE,QAAQ,EACR,YAAY,aAAa,EACzB,SAAS,EACT,cAAc,EACd,eAAe,EAChB,iBACC,8OAAC,kIAAA,CAAA,gBAAa;QACZ,WAAW;QACX,WAAW;QACX,mBAAiB;QACjB,oBAAkB;QAClB,sBAAsB,CAAC,IAAM,EAAE,cAAc;QAC7C,iBAAiB,CAAC;YAChB,IAAI,WAAW;gBACb,UAAU;YACZ;QACF;kBAEC;;;;;;AAqCL,MAAM,gBAA8C,CAAC,EAAE,IAAI,EAAE,iBAC3D,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAG,WAAU;;4BAA8B;4BAAmB,KAAK,OAAO;;;;;;;;;;;;;0BAG7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,8OAAC;gCAAK,WAAU;0CAA6B,KAAK,SAAS,IAAI;;;;;;;;;;;;kCAGjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,8OAAC;gCAAK,WAAU;0CAA6B,KAAK,OAAO,IAAI;;;;;;;;;;;;kCAG/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,8OAAC;gCAAK,WAAU;0CAA6B,KAAK,mBAAmB,IAAI;;;;;;;;;;;;kCAG3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,8OAAC;gCAAK,WAAU;0CAA6B,KAAK,iBAAiB,IAAI;;;;;;;;;;;;kCAGzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA4D;;;;;;0CAC5E,8OAAC;gCAAK,WAAU;;oCAAmC,KAAK,eAAe,IAAI;oCAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAO9E,MAAM,sBAA0D,CAAC,EACtE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACP;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sEAAsE;IACtE,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,+CAA+C;YACzD,MAAM,CAAC,CAAC;YACR,UAAU,CAAC,CAAC;YACZ,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,2DAA2D;YAC3D,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,uDAAuD;YACvD,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,mFAAmF;YACnF,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,4CAA4C;oBACtD,WAAW,KAAK,SAAS;oBACzB,SAAS,KAAK,OAAO;gBACvB;gBAEA,wDAAwD;gBACxD,MAAM,0BAA0B,mBAAmB,GAAG,CAAC,CAAC,SAAgB,CAAC;wBACvE,GAAG,MAAM;wBACT,YAAY,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;oBACpF,CAAC;gBAED,UAAU;gBACV,QAAQ,GAAG,CAAC,2CAA2C,wBAAwB,MAAM;YACvF,OAAO;gBACL,UAAU,EAAE;YACd;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,gFAAgF;IAChF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAChB,IAAI,UAAU;gBACZ;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,cAAc;YACd,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,SAAS;QACX;IACF,GAAG;QAAC;QAAM;QAAM;KAAS;IAEzB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,OAAO,aAAa,EAAE,cAAc,SAAS,WAAW,WAAW,OACpE,OAAO,SAAS,EAAE,cAAc,SAAS,WAAW,WAAW;QACpF,MAAM,aAAa,cAAc,eAAe,OAAO,UAAU,GAAG,CAAC,OAAO,UAAU;QACtF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,kBAAkB,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;IAC/D,MAAM,oBAAoB,OAAO,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;IAElE,QAAQ,GAAG,CAAC,wCAAwC;QAClD,cAAc,OAAO,MAAM;QAC3B,aAAa;QACb,eAAe;QACf,UAAU,eAAe,MAAM;QAC/B;QACA;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,gBAAgB;QAE9B,aAAa;QACb,IAAI;YACF,MAAM,OAAO,KAAK,OAAO,EAAE,gBAAgB;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,MAAM,cAAc;QAClB,wCAAwC;QACxC,kBAAkB;QAClB,kBAAkB;QAClB,cAAc;QACd,aAAa;QACb,SAAS;QACT,UAAU,EAAE;QACZ;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAc,MAAM;;;;;;wBAGpB,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;sCAMP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAwB;;;;;;8CACzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,mBAAmB;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,mBAAmB;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAsB;;;;;;sEACtC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAM;oDACN,SAAS,mBAAmB;oDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,WAAU;;;;;;8DAEZ,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAM3C,mBAAmB,iCAClB;;8CAEE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAsB;;;;;;sDAG/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,CAAC,qFAAqF,EAC/F,cAAc,eACV,gDACA,wDACJ;;sEAEF,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAA2B;wDACpC;wDAAgB;;;;;;;8DAEhC,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,CAAC,qFAAqF,EAC/F,cAAc,iBACV,mDACA,wDACJ;;sEAEF,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAA4B;wDACrC;wDAAkB;;;;;;;;;;;;;sDAKtC,8OAAC;4CAAI,WAAU;sDACZ,8BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;uDAErC,eAAe,MAAM,KAAK,kBAC5B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;sEACzB,8OAAC;4DAAE,WAAU;;gEAA2C;gEACtC,cAAc,eAAe,gBAAgB;gEAAgB;;;;;;;sEAE/E,8OAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;qEAM3C,8OAAC;gDAAI,WAAU;0DACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,OAAO,SAAS;gEACvB,SAAS,mBAAmB,OAAO,SAAS;gEAC5C,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEACjD,WAAU;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgC,OAAO,aAAa;;;;;;4EACnE,OAAO,UAAU,iBAChB,8OAAC,2NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;qGAEvB,8OAAC,wNAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;;;;;;;kFAG7B,8OAAC;wEAAE,WAAU;;4EACV,OAAO,SAAS;4EAAC;4EAAI,OAAO,OAAO;4EAAC;4EAAI,OAAO,aAAa;4EAAC;;;;;;;;;;;;;;uDArB7D,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;wBAkCpC,mBAAmB,gCAClB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;sDAAO;;;;;;wCAAoB;;;;;;;;;;;;;;;;;;;8BAOpC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,kBAAmB,mBAAmB,mBAAmB,CAAC;4BAClF,WAAW,CAAC,UAAU,EACpB,mBAAmB,iBACf,2CACA,4CACJ;4BACF,SAAS,mBAAmB,iBAAiB,gBAAgB;sCAE5D,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAGjD,mBAAmB,+BACrB;;kDACE,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;+CAI5C;;;;;;;;;;;;;;;;;;;;;;;AAOd;AAGO,MAAM,sBAA0D,CAAC,EACtE,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,MAAM,EACP;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8BAA8B;IAC9B,MAAM,oBAAoB;IAC1B,MAAM,qBAAqB;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,eAAe;YACf,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAK;IAET,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAW,WAAW;QAE5B,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;QACT;QAEA,IAAI,MAAM,aAAa,YAAY,GAAG;YACpC,OAAO;QACT;QAEA,IAAI,WAAW,mBAAmB;YAChC,OAAO,CAAC,2DAA2D,EAAE,kBAAkB,EAAE,CAAC;QAC5F;QAEA,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,eAAe;QACf,MAAM,QAAQ,cAAc;QAC5B,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,MAAM;QAEX,MAAM,QAAQ,cAAc;QAC5B,IAAI,OAAO;YACT,mBAAmB;YACnB;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,OAAO,KAAK,OAAO,EAAE,WAAW;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;QACA,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,mBAAmB,YAAY,IAAI,IAAI;YAC/D;QACF;IACF;IAEA,MAAM,cAAc,YAAY,IAAI,MAAM,CAAC;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC;YACC,WAAU;YACV,WAAW;YACX,gBAAe;;8BAEf,8OAAC;oBACC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,OAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAY;;;;;;8BAGd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAc,MAAM;;;;;;sCAGrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAsC;;;;;;;;;;;;sDAExD,8OAAC;4CAAE,WAAU;;gDAAoC;gDAAkB;;;;;;;sDACnE,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;sDAEzD,8OAAC;4CAAE,WAAU;;gDAAqC;gDAAmB;;;;;;;sDACrE,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;;sDACtC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;8CAG7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,aAAY;4CACZ,KAAI;4CACJ,KAAK;4CACL,MAAK;4CACL,WAAW,CAAC,MAAM,EAAE,kBAAkB,sCAAsC,wBAAwB;;;;;;sDAEtG,8OAAC;4CAAK,WAAU;sDAA4E;;;;;;;;;;;;gCAI7F,iCACC,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCACxB;;;;;;;8CAGL,8OAAC;oCAAE,WAAU;;wCAAwB;wCACT,KAAK,GAAG,CAAC,GAAG,oBAAoB,qBAAqB,CAAC,WAAW,gBAAgB,CAAC;wCAAG;;;;;;;;;;;;;sCAKnH,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,8OAAC,iIAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC;sDAAO;;;;;;wCAAsB;;;;;;;;;;;;;;;;;;;8BAMpC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC;4BACxB,WAAW,CAAC,OAAO,EAAE,CAAC,cAAc,kCAAkC,mCAAmC;sCAExG,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}, {"offset": {"line": 7898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ModificaBobinaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON>ooter,\n  <PERSON><PERSON>Header,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Package, Search, CheckCircle, AlertTriangle } from 'lucide-react'\nimport { Cavo, Bobina } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ModificaBobinaDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: string; nome_cantiere: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ModificaBobinaD<PERSON>og({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: ModificaBobinaDialogProps) {\n  const { cantiere: cantiereFromContext } = useAuth()\n  const cantiere = cantiereProp || cantiereFromContext\n\n  const [selectedOption, setSelectedOption] = useState<string>('assegna_nuova')\n  const [selectedBobina, setSelectedBobina] = useState<string>('')\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [loadingBobine, setLoadingBobine] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string>('')\n  const [searchText, setSearchText] = useState('')\n  const [activeTab, setActiveTab] = useState<'compatibili' | 'incompatibili'>('compatibili')\n\n  // Reset form quando il dialog si apre/chiude\n  useEffect(() => {\n    if (open) {\n      setSelectedOption('assegna_nuova')\n      setSelectedBobina('')\n      setSearchText('')\n      setActiveTab('compatibili')\n      setError('')\n      if (cantiere?.id_cantiere) {\n        loadBobine()\n      }\n    }\n  }, [open, cantiere?.id_cantiere])\n\n  const loadBobine = async () => {\n    if (!cantiere?.id_cantiere) {\n      setError('Cantiere non disponibile')\n      return\n    }\n\n    try {\n      setLoadingBobine(true)\n      setError('')\n\n      console.log('🔄 ModificaBobinaDialog: Caricamento bobine per cantiere:', cantiere.id_cantiere)\n\n      // Usa la stessa logica di InserisciMetriDialog\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      if (response && Array.isArray(response)) {\n        // Filtra solo per stato (disponibile o in uso) e metri residui > 0 - STESSA LOGICA DI InserisciMetriDialog\n        const bobineUtilizzabili = response.filter((bobina) =>\n          bobina.stato_bobina !== 'Terminata' &&\n          bobina.stato_bobina !== 'Over' &&\n          bobina.metri_residui > 0\n        )\n\n        setBobine(bobineUtilizzabili)\n        console.log('✅ ModificaBobinaDialog: Bobine caricate:', bobineUtilizzabili.length)\n        console.log('📋 ModificaBobinaDialog: Dettaglio bobine:', bobineUtilizzabili.map(b => ({\n          id: b.id_bobina,\n          tipologia: b.tipologia,\n          sezione: b.sezione,\n          metri_residui: b.metri_residui,\n          stato: b.stato_bobina\n        })))\n      } else {\n        setBobine([])\n        console.log('⚠️ ModificaBobinaDialog: Nessuna bobina trovata')\n      }\n    } catch (error) {\n      console.error('❌ ModificaBobinaDialog: Errore caricamento bobine:', error)\n      setError('Errore nel caricamento delle bobine')\n      setBobine([])\n    } finally {\n      setLoadingBobine(false)\n    }\n  }\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    const compatibili = bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n\n    console.log('🔍 ModificaBobinaDialog: Filtro compatibili:', {\n      cavoTipologia: cavo.tipologia,\n      cavoSezione: cavo.sezione,\n      totaleBobine: bobine.length,\n      bobineCompatibili: compatibili.length,\n      searchText\n    })\n\n    return compatibili\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  const handleClose = () => {\n    setSelectedOption('assegna_nuova')\n    setSelectedBobina('')\n    setSearchText('')\n    setError('')\n    onClose()\n  }\n\n  const handleSave = async () => {\n    console.log('🔄 ModificaBobinaDialog: Salvataggio:', {\n      selectedOption,\n      selectedBobina,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      if (selectedOption === 'assegna_nuova') {\n        if (!selectedBobina) {\n          onError('Selezionare una bobina')\n          return\n        }\n\n        // Usa updateMetriPosati come in InserisciMetriDialog\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: cavo.metratura_reale || 0, // Mantieni i metri attuali\n          id_bobina: selectedBobina,\n          force_over: true // Permetti operazioni incompatibili\n        })\n\n        if (result.success) {\n          onSuccess(`Bobina aggiornata con successo per il cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante l\\'aggiornamento della bobina')\n        }\n      } else if (selectedOption === 'rimuovi_bobina') {\n        // Assegna BOBINA_VUOTA\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: cavo.metratura_reale || 0,\n          id_bobina: 'BOBINA_VUOTA',\n          force_over: false\n        })\n\n        if (result.success) {\n          onSuccess(`Bobina rimossa dal cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante la rimozione della bobina')\n        }\n      } else if (selectedOption === 'annulla_installazione') {\n        // Reset completo del cavo\n        const result = await caviApi.updateMetriPosati({\n          id_cavo: cavo.id_cavo,\n          metri_posati: 0,\n          id_bobina: 'BOBINA_VUOTA',\n          force_over: false\n        })\n\n        if (result.success) {\n          onSuccess(`Installazione annullata per il cavo ${cavo.id_cavo}`)\n          handleClose()\n        } else {\n          onError(result.message || 'Errore durante l\\'annullamento dell\\'installazione')\n        }\n      }\n    } catch (error) {\n      console.error('❌ ModificaBobinaDialog: Errore salvataggio:', error)\n      onError('Errore durante il salvataggio')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-4xl max-h-[90vh] flex flex-col\">\n          <DialogHeader>\n            <DialogTitle>\n              Modifica Bobina Cavo {cavo.id_cavo}\n            </DialogTitle>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-hidden space-y-6\">\n            {/* Sezione Cavo Selezionato */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5 text-blue-600\" />\n                <h3 className=\"font-medium text-gray-900\">Cavo Selezionato</h3>\n              </div>\n\n              {/* Informazioni Cavo - Formato esatto come richiesto dall'utente */}\n              <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n                <div className=\"text-sm font-medium text-blue-800\">\n                  Informazioni Cavo / Tipologia: {cavo.tipologia || 'N/A'} / Da: {cavo.ubicazione_partenza || 'N/A'} / Formazione: {cavo.sezione || 'N/A'} / A: {cavo.ubicazione_arrivo || 'N/A'} / Metri Posati: {cavo.metratura_reale || 0} m\n                </div>\n              </div>\n            </div>\n\n            {/* Sezione Opzioni di modifica */}\n            <div className=\"space-y-3\">\n              <h3 className=\"font-medium\">Opzioni di modifica</h3>\n\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"assegna_nuova\"\n                    checked={selectedOption === 'assegna_nuova'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm\">Cambia bobina</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"rimuovi_bobina\"\n                    checked={selectedOption === 'rimuovi_bobina'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm\">Bobina vuota</span>\n                </label>\n\n                <label className=\"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50\">\n                  <input\n                    type=\"radio\"\n                    name=\"operazione\"\n                    value=\"annulla_installazione\"\n                    checked={selectedOption === 'annulla_installazione'}\n                    onChange={(e) => setSelectedOption(e.target.value)}\n                    className=\"w-4 h-4 text-blue-600\"\n                  />\n                  <span className=\"text-sm text-red-600\">Annulla posa</span>\n                </label>\n              </div>\n            </div>\n\n            {/* Sezione Seleziona bobina - solo se \"Assegna nuova bobina\" è selezionato */}\n            {selectedOption === 'assegna_nuova' && (\n              <div className=\"space-y-3\">\n                <h3 className=\"font-medium\">Seleziona bobina</h3>\n\n                {/* Campo ricerca */}\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                  <Input\n                    placeholder=\"Cerca bobina per ID, tipologia o numero...\"\n                    value={searchText}\n                    onChange={(e) => setSearchText(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n\n                {/* Tab per bobine compatibili/incompatibili */}\n                <div className=\"flex space-x-1 border-b\">\n                  <button\n                    onClick={() => setActiveTab('compatibili')}\n                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'compatibili'\n                        ? 'border-green-500 text-green-600 bg-green-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      <span>Bobine Compatibili ({bobineCompatibili.length})</span>\n                    </div>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('incompatibili')}\n                    className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                      activeTab === 'incompatibili'\n                        ? 'border-orange-500 text-orange-600 bg-orange-50'\n                        : 'border-transparent text-gray-500 hover:text-gray-700'\n                    }`}\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      <span>Bobine Incompatibili ({bobineIncompatibili.length})</span>\n                    </div>\n                  </button>\n                </div>\n\n                {/* Lista bobine */}\n                <div className=\"border rounded-lg h-64 overflow-y-auto\">\n                  {loadingBobine ? (\n                    <div className=\"flex items-center justify-center h-full\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\n                        <span className=\"text-sm text-gray-600\">Caricamento bobine...</span>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"p-2\">\n                      {activeTab === 'compatibili' ? (\n                        bobineCompatibili.length === 0 ? (\n                          <div className=\"text-center py-8\">\n                            <div className=\"text-gray-500 text-sm mb-2\">\n                              Nessuna bobina compatibile trovata\n                            </div>\n                            <div className=\"text-xs text-gray-400\">\n                              Cercando bobine con tipologia <strong>{cavo.tipologia}</strong> e formazione <strong>{cavo.sezione}</strong>\n                            </div>\n                          </div>\n                        ) : (\n                          <div className=\"space-y-2\">\n                            {bobineCompatibili.map((bobina) => (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => setSelectedBobina(bobina.id_bobina)}\n                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-blue-100 border-2 border-blue-300 shadow-md'\n                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                <div className=\"flex justify-between items-start\">\n                                  <div className=\"flex-1\">\n                                    <div className=\"flex items-center space-x-2 mb-1\">\n                                      <div className=\"font-medium text-sm text-gray-900\">{bobina.id_bobina}</div>\n                                      {bobina.stato_bobina && (\n                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                          bobina.stato_bobina === 'Disponibile' ? 'bg-green-100 text-green-800' :\n                                          bobina.stato_bobina === 'In uso' ? 'bg-blue-100 text-blue-800' :\n                                          'bg-yellow-100 text-yellow-800'\n                                        }`}>\n                                          {bobina.stato_bobina}\n                                        </span>\n                                      )}\n                                    </div>\n                                    <div className=\"text-xs text-gray-500 mb-1\">\n                                      <span className=\"font-medium\">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>\n                                    </div>\n                                  </div>\n                                  <div className=\"text-right ml-3\">\n                                    <div className={`text-sm font-medium ${\n                                      bobina.metri_residui > 0 ? 'text-green-600' : 'text-gray-500'\n                                    }`}>\n                                      {bobina.metri_residui}m\n                                    </div>\n                                    <div className=\"text-xs text-gray-400\">\n                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )\n                      ) : (\n                        bobineIncompatibili.length === 0 ? (\n                          <div className=\"text-center py-8 text-gray-500 text-sm\">\n                            Nessuna bobina incompatibile trovata\n                          </div>\n                        ) : (\n                          <div className=\"space-y-2\">\n                            {/* Avviso per bobine incompatibili */}\n                            <div className=\"p-3 bg-orange-50 border border-orange-200 rounded-lg\">\n                              <div className=\"flex items-start space-x-2\">\n                                <AlertCircle className=\"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0\" />\n                                <div className=\"text-sm text-orange-800\">\n                                  <div className=\"font-medium mb-1\">Bobine Incompatibili</div>\n                                  <div className=\"text-xs\">\n                                    Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate.\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n\n                            {bobineIncompatibili.map((bobina) => (\n                              <div\n                                key={bobina.id_bobina}\n                                onClick={() => setSelectedBobina(bobina.id_bobina)}\n                                className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${\n                                  selectedBobina === bobina.id_bobina\n                                    ? 'bg-orange-100 border-2 border-orange-300 shadow-md'\n                                    : 'hover:bg-gray-50 border border-gray-200 hover:border-gray-300'\n                                }`}\n                              >\n                                <div className=\"flex justify-between items-start\">\n                                  <div className=\"flex-1\">\n                                    <div className=\"flex items-center space-x-2 mb-1\">\n                                      <div className=\"font-medium text-sm text-gray-900\">{bobina.id_bobina}</div>\n                                      <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800\">\n                                        INCOMPATIBILE\n                                      </span>\n                                    </div>\n                                    <div className=\"text-xs text-gray-500 mb-1\">\n                                      <span className=\"font-medium\">{bobina.tipologia}</span> • <span>{bobina.sezione}</span>\n                                    </div>\n                                  </div>\n                                  <div className=\"text-right ml-3\">\n                                    <div className={`text-sm font-medium ${\n                                      bobina.metri_residui > 0 ? 'text-orange-600' : 'text-gray-500'\n                                    }`}>\n                                      {bobina.metri_residui}m\n                                    </div>\n                                    <div className=\"text-xs text-gray-400\">\n                                      {bobina.metri_residui > 0 ? 'disponibili' : 'esaurita'}\n                                    </div>\n                                  </div>\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        )\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Errori */}\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          <DialogFooter className=\"flex justify-end space-x-2\">\n            <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n              Annulla\n            </Button>\n            <Button \n              onClick={handleSave} \n              disabled={loading || (selectedOption === 'assegna_nuova' && !selectedBobina)}\n            >\n              {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n              Salva\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAjBA;;;;;;;;;;AA4Be,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,UAAU,mBAAmB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,WAAW,gBAAgB;IAEjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,aAAa;YACb,SAAS;YACT,IAAI,UAAU,aAAa;gBACzB;YACF;QACF;IACF,GAAG;QAAC;QAAM,UAAU;KAAY;IAEhC,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,aAAa;YAC1B,SAAS;YACT;QACF;QAEA,IAAI;YACF,iBAAiB;YACjB,SAAS;YAET,QAAQ,GAAG,CAAC,6DAA6D,SAAS,WAAW;YAE7F,+CAA+C;YAC/C,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,IAAI,YAAY,MAAM,OAAO,CAAC,WAAW;gBACvC,2GAA2G;gBAC3G,MAAM,qBAAqB,SAAS,MAAM,CAAC,CAAC,SAC1C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;gBAGzB,UAAU;gBACV,QAAQ,GAAG,CAAC,4CAA4C,mBAAmB,MAAM;gBACjF,QAAQ,GAAG,CAAC,8CAA8C,mBAAmB,GAAG,CAAC,CAAA,IAAK,CAAC;wBACrF,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,SAAS;wBACtB,SAAS,EAAE,OAAO;wBAClB,eAAe,EAAE,aAAa;wBAC9B,OAAO,EAAE,YAAY;oBACvB,CAAC;YACH,OAAO;gBACL,UAAU,EAAE;gBACZ,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,SAAS;YACT,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA;YAChC,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;QAEA,QAAQ,GAAG,CAAC,gDAAgD;YAC1D,eAAe,KAAK,SAAS;YAC7B,aAAa,KAAK,OAAO;YACzB,cAAc,OAAO,MAAM;YAC3B,mBAAmB,YAAY,MAAM;YACrC;QACF;QAEA,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,MAAM,cAAc;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,cAAc;QACd,SAAS;QACT;IACF;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,yCAAyC;YACnD;YACA;YACA,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,mBAAmB,iBAAiB;gBACtC,IAAI,CAAC,gBAAgB;oBACnB,QAAQ;oBACR;gBACF;gBAEA,qDAAqD;gBACrD,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc,KAAK,eAAe,IAAI;oBACtC,WAAW;oBACX,YAAY,KAAK,oCAAoC;gBACvD;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,2CAA2C,EAAE,KAAK,OAAO,EAAE;oBACtE;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF,OAAO,IAAI,mBAAmB,kBAAkB;gBAC9C,uBAAuB;gBACvB,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc,KAAK,eAAe,IAAI;oBACtC,WAAW;oBACX,YAAY;gBACd;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,wBAAwB,EAAE,KAAK,OAAO,EAAE;oBACnD;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF,OAAO,IAAI,mBAAmB,yBAAyB;gBACrD,0BAA0B;gBAC1B,MAAM,SAAS,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;oBAC7C,SAAS,KAAK,OAAO;oBACrB,cAAc;oBACd,WAAW;oBACX,YAAY;gBACd;gBAEA,IAAI,OAAO,OAAO,EAAE;oBAClB,UAAU,CAAC,oCAAoC,EAAE,KAAK,OAAO,EAAE;oBAC/D;gBACF,OAAO;oBACL,QAAQ,OAAO,OAAO,IAAI;gBAC5B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;kBACE,cAAA,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAM,cAAc;sBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;kCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;;gCAAC;gCACW,KAAK,OAAO;;;;;;;;;;;;kCAItC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;gDAAG,WAAU;0DAA4B;;;;;;;;;;;;kDAI5C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;gDAAoC;gDACjB,KAAK,SAAS,IAAI;gDAAM;gDAAQ,KAAK,mBAAmB,IAAI;gDAAM;gDAAgB,KAAK,OAAO,IAAI;gDAAM;gDAAO,KAAK,iBAAiB,IAAI;gDAAM;gDAAkB,KAAK,eAAe,IAAI;gDAAE;;;;;;;;;;;;;;;;;;0CAMjO,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAG5B,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAM;wDACN,SAAS,mBAAmB;wDAC5B,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAuB;;;;;;;;;;;;;;;;;;;;;;;;4BAM5C,mBAAmB,iCAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAc;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,gBACV,gDACA,wDACJ;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;;gEAAK;gEAAqB,kBAAkB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;0DAGxD,8OAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,kBACV,mDACA,wDACJ;0DAEF,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;;gEAAK;gEAAuB,oBAAoB,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;kDAM9D,8OAAC;wCAAI,WAAU;kDACZ,8BACC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;iEAI5C,8OAAC;4CAAI,WAAU;sDACZ,cAAc,gBACb,kBAAkB,MAAM,KAAK,kBAC3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAG5C,8OAAC;wDAAI,WAAU;;4DAAwB;0EACP,8OAAC;0EAAQ,KAAK,SAAS;;;;;;4DAAU;0EAAc,8OAAC;0EAAQ,KAAK,OAAO;;;;;;;;;;;;;;;;;qEAItG,8OAAC;gDAAI,WAAU;0DACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;wDAEC,SAAS,IAAM,kBAAkB,OAAO,SAAS;wDACjD,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,OAAO,SAAS,GAC/B,mDACA,iEACJ;kEAEF,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FAAqC,OAAO,SAAS;;;;;;gFACnE,OAAO,YAAY,kBAClB,8OAAC;oFAAK,WAAW,CAAC,2CAA2C,EAC3D,OAAO,YAAY,KAAK,gBAAgB,gCACxC,OAAO,YAAY,KAAK,WAAW,8BACnC,iCACA;8FACC,OAAO,YAAY;;;;;;;;;;;;sFAI1B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAe,OAAO,SAAS;;;;;;gFAAQ;8FAAG,8OAAC;8FAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;8EAGnF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,oBAAoB,EACnC,OAAO,aAAa,GAAG,IAAI,mBAAmB,iBAC9C;;gFACC,OAAO,aAAa;gFAAC;;;;;;;sFAExB,8OAAC;4EAAI,WAAU;sFACZ,OAAO,aAAa,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;uDAjC7C,OAAO,SAAS;;;;;;;;;uDA0C7B,oBAAoB,MAAM,KAAK,kBAC7B,8OAAC;gDAAI,WAAU;0DAAyC;;;;;qEAIxD,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;8EACvB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAmB;;;;;;sFAClC,8OAAC;4EAAI,WAAU;sFAAU;;;;;;;;;;;;;;;;;;;;;;;oDAO9B,oBAAoB,GAAG,CAAC,CAAC,uBACxB,8OAAC;4DAEC,SAAS,IAAM,kBAAkB,OAAO,SAAS;4DACjD,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,OAAO,SAAS,GAC/B,uDACA,iEACJ;sEAEF,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGAAqC,OAAO,SAAS;;;;;;kGACpE,8OAAC;wFAAK,WAAU;kGAA2E;;;;;;;;;;;;0FAI7F,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAAe,OAAO,SAAS;;;;;;oFAAQ;kGAAG,8OAAC;kGAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;;kFAGnF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAW,CAAC,oBAAoB,EACnC,OAAO,aAAa,GAAG,IAAI,oBAAoB,iBAC/C;;oFACC,OAAO,aAAa;oFAAC;;;;;;;0FAExB,8OAAC;gFAAI,WAAU;0FACZ,OAAO,aAAa,GAAG,IAAI,gBAAgB;;;;;;;;;;;;;;;;;;2DA3B7C,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA4C1C,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,8OAAC,kIAAA,CAAA,eAAY;wBAAC,WAAU;;0CACtB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAa,UAAU;0CAAS;;;;;;0CAGnE,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,WAAY,mBAAmB,mBAAmB,CAAC;;oCAE5D,yBAAW,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA+B;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5E", "debugId": null}}, {"offset": {"line": 8867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/InserisciMetriDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Badge } from '@/components/ui/badge'\nimport { Loader2, AlertCircle, Calculator, Search, CheckCircle, AlertTriangle, X } from 'lucide-react'\nimport { Cavo } from '@/types'\nimport { parcoCaviApi, caviApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\nimport ModificaBobinaDialog from './ModificaBobinaDialog'\n\ninterface Bobina {\n  id_bobina: string\n  numero_bobina?: string\n  tipologia: string\n  formazione: string\n  metri_residui: number\n  fornitore?: string\n  stato_bobina?: string\n}\n\ninterface FormData {\n  metri_posati: string\n  id_bobina: string\n}\n\ninterface FormErrors {\n  metri_posati?: string\n  id_bobina?: string\n}\n\ninterface FormWarnings {\n  metri_posati?: string\n}\n\ninterface InserisciMetriDialogProps {\n  open: boolean\n  onClose: () => void\n  cavo: Cavo | null\n  cantiere?: { id_cantiere: number; commessa: string } | null\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function InserisciMetriDialog({\n  open,\n  onClose,\n  cavo,\n  cantiere: cantiereProp,\n  onSuccess,\n  onError\n}: InserisciMetriDialogProps) {\n  const { cantiere: cantiereAuth } = useAuth()\n\n  // Usa il cantiere passato come prop o quello dall'auth come fallback\n  const cantiere = cantiereProp || cantiereAuth\n  \n  // Stati per il form\n  const [formData, setFormData] = useState<FormData>({\n    metri_posati: '',\n    id_bobina: ''\n  })\n\n  // Debug formData changes\n  useEffect(() => {\n    console.log('📊 InserisciMetriDialog: FormData aggiornato:', {\n      hasMetri: !!formData.metri_posati,\n      hasBobina: !!formData.id_bobina,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n  }, [formData])\n  const [formErrors, setFormErrors] = useState<FormErrors>({})\n  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})\n  const [saving, setSaving] = useState(false)\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState<Bobina[]>([])\n  const [bobineLoading, setBobineLoading] = useState(false)\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('')\n\n  // Stati per dialoghi\n  const [showModificaBobinaDialog, setShowModificaBobinaDialog] = useState(false)\n\n  // Carica bobine quando si apre il dialog\n  useEffect(() => {\n    if (open && cavo) {\n\n      if (cantiere) {\n        loadBobine()\n      } else {\n      }\n\n      setFormData({\n        metri_posati: '0', // Default a 0 come nell'originale\n        id_bobina: ''\n      })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n    }\n  }, [open, cavo, cantiere])\n\n  // Validazione real-time dei metri posati\n  useEffect(() => {\n    if (formData.metri_posati && cavo) {\n      validateMetriPosati(parseFloat(formData.metri_posati))\n    } else {\n      setFormErrors(prev => ({ ...prev, metri_posati: undefined }))\n      setFormWarnings(prev => ({ ...prev, metri_posati: undefined }))\n    }\n  }, [formData.metri_posati, cavo])\n\n  const validateMetriPosati = (metri: number) => {\n    if (!cavo) return\n\n    let errors: FormErrors = { ...formErrors }\n    let warnings: FormWarnings = { ...formWarnings }\n\n    // Rimuovi errori/warning precedenti per metri_posati\n    delete errors.metri_posati\n    delete warnings.metri_posati\n\n    // NESSUN ERRORE BLOCCANTE - Solo warning informativi\n    // I warning non impediscono il salvataggio\n\n    if (metri > (cavo.metri_teorici || 0) * 1.1) {\n      warnings.metri_posati = `Attenzione: i metri posati superano del 10% i metri teorici (${cavo.metri_teorici}m)`\n    } else if (metri > (cavo.metri_teorici || 0)) {\n      warnings.metri_posati = 'Metratura superiore ai metri teorici'\n    }\n\n    setFormErrors(errors)\n    setFormWarnings(warnings)\n  }\n\n  const loadBobine = async () => {\n    console.log('🎯 InserisciMetriDialog: Caricamento bobine:', {\n      cavo: !!cavo,\n      cantiere: !!cantiere,\n      cavoId: cavo?.id_cavo,\n      cantiereId: cantiere?.id_cantiere\n    })\n\n    if (!cavo || !cantiere) {\n      return\n    }\n\n    try {\n      setBobineLoading(true)\n\n      // Carica tutte le bobine disponibili\n      const response = await parcoCaviApi.getBobine(cantiere.id_cantiere)\n\n      // Gestisce diversi formati di risposta\n      let bobineData = []\n      if (Array.isArray(response)) {\n        bobineData = response\n      } else if (response && Array.isArray(response.data)) {\n        bobineData = response.data\n      } else if (response && response.bobine && Array.isArray(response.bobine)) {\n        bobineData = response.bobine\n      } else {\n        throw new Error('Formato risposta API non valido')\n      }\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter((bobina: Bobina) =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      )\n\n      if (cavo) {\n        console.log('🔍 InserisciMetriDialog: Filtro per cavo:', {\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        })\n\n        // Separa bobine compatibili e incompatibili\n        const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n          const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione\n          return isCompatible\n        })\n        const bobineNonCompatibili = bobineUtilizzabili.filter(bobina =>\n          !(bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione)\n        )\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui)\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili]\n        setBobine(bobineOrdinate)\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui)\n        setBobine(bobineUtilizzabili)\n      }\n    } catch (error: any) {\n      console.error('❌ InserisciMetriDialog: Errore caricamento bobine:', {\n        message: error.message,\n        response: error.response,\n        status: error.response?.status,\n        data: error.response?.data\n      })\n\n      // Non mostrare errore se è solo un problema di rete, permetti di usare BOBINA_VUOTA\n      if (error.response?.status !== 404) {\n        onError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA.')\n      }\n      setBobine([])\n    } finally {\n      setBobineLoading(false)\n    }\n  }\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina: string) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA'\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1]\n    }\n    // Cerca nella lista bobine per ottenere il numero_bobina\n    const bobina = bobine.find(b => b.id_bobina === idBobina)\n    return bobina ? bobina.numero_bobina || idBobina : idBobina\n  }\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isCompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return []\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()))\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0\n    })\n  }\n\n  const bobineCompatibili = getBobineCompatibili()\n  const bobineIncompatibili = getBobineIncompatibili()\n\n  const handleBobinaSelect = (bobina: Bobina) => {\n    // Selezione diretta senza popup - sia compatibili che incompatibili\n    setFormData(prev => ({ ...prev, id_bobina: bobina.id_bobina }))\n\n    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota\n    setFormErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.id_bobina  // Rimuovi completamente la chiave\n      return newErrors\n    })\n  }\n\n  const handleBobinaVuotaSelect = () => {\n    setFormData(prev => {\n      const newData = { ...prev, id_bobina: 'BOBINA_VUOTA' }\n      return newData\n    })\n\n    // Reset completo degli errori - rimuovi la chiave invece di impostarla vuota\n    setFormErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.id_bobina  // Rimuovi completamente la chiave\n      return newErrors\n    })\n\n    console.log('🔄 InserisciMetriDialog: Usando BOBINA_VUOTA:', {\n      saving: false,\n      metri_posati: formData.metri_posati,\n      id_bobina: 'BOBINA_VUOTA',\n      errorsCount: Object.keys(formErrors).length\n    })\n  }\n\n  const handleSave = async () => {\n    console.log('💾 InserisciMetriDialog: Salvataggio metri:', {\n      cavo: cavo?.id_cavo,\n      metri_posati: formData.metri_posati,\n      id_bobina: formData.id_bobina\n    })\n\n    if (!cavo) {\n      return\n    }\n\n    // Validazioni di base (solo controlli essenziali)\n    if (!formData.metri_posati || parseFloat(formData.metri_posati) < 0) {\n      onError('Inserire metri posati validi (≥ 0)')\n      return\n    }\n\n    if (!formData.id_bobina) {\n      onError('Selezionare una bobina o BOBINA VUOTA')\n      return\n    }\n\n    const metri = parseFloat(formData.metri_posati)\n\n    // Gestione stato OVER per bobine reali (NON BLOCCANTE)\n    if (formData.id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === formData.id_bobina)\n      if (bobina && metri > bobina.metri_residui) {\n        // OVER state - salva comunque ma avvisa\n        // Il salvataggio continua - lo stato OVER viene gestito dal backend\n      }\n    }\n\n    try {\n      setSaving(true)\n\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      // Aggiorna metri posati tramite API\n      console.log('🚀 InserisciMetriDialog: Chiamata API updateMetriPosati:', {\n        cantiere: cantiere.id_cantiere,\n        cavo: cavo.id_cavo,\n        metri: metri,\n        bobina: formData.id_bobina,  // Mostra il valore reale che viene passato\n        isBobinaVuota: formData.id_bobina === 'BOBINA_VUOTA'\n      })\n\n      await caviApi.updateMetriPosati(\n        cantiere.id_cantiere,\n        cavo.id_cavo,\n        metri,\n        formData.id_bobina,  // Passa sempre il valore, incluso 'BOBINA_VUOTA'\n        true  // force_over: true per permettere bobine incompatibili e OVER state\n      )\n\n      onSuccess(`Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metri}m`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il salvataggio dei metri posati'\n      onError(errorMessage)\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleClose = () => {\n    if (!saving) {\n      setFormData({ metri_posati: '', id_bobina: '' })\n      setFormErrors({})\n      setFormWarnings({})\n      setSearchText('')\n      onClose()\n    }\n  }\n\n  if (!cavo) return null\n\n  return (\n    <>\n      <Dialog open={open} onOpenChange={handleClose}>\n        <DialogContent className=\"max-w-7xl h-[90vh] flex flex-col\">\n          <DialogHeader className=\"flex-shrink-0\">\n            <DialogTitle className=\"flex items-center gap-2\">\n              <Calculator className=\"h-5 w-5\" />\n              Inserisci Metri Posati - {cavo.id_cavo}\n            </DialogTitle>\n            <DialogDescription>\n              Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"flex-1 overflow-y-auto space-y-6\">\n            {/* Sezione informazioni cavo e metri posati */}\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4\">\n              {/* Informazioni cavo - 2/3 della larghezza */}\n              <div className=\"lg:col-span-2\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-200\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Informazioni Cavo</h3>\n                  <div className=\"grid grid-cols-2 gap-3 text-sm\">\n                    <div><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</div>\n                    <div><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</div>\n                    <div><strong>Formazione:</strong> {cavo.sezione || 'N/A'}</div>\n                    <div><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</div>\n                    <div><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</div>\n                    <div><strong>Già posati:</strong> {cavo.metratura_reale || 0} m</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Campo metri posati - 1/3 della larghezza */}\n              <div className=\"lg:col-span-1\">\n                <div className=\"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">Metri da Installare</h3>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"metri\" className=\"text-sm font-medium\">\n                      Metri Posati\n                    </Label>\n                    <div className=\"relative\">\n                      <Input\n                        id=\"metri\"\n                        type=\"number\"\n                        value={formData.metri_posati}\n                        onChange={(e) => setFormData(prev => ({ ...prev, metri_posati: e.target.value }))}\n                        placeholder=\"Inserisci metri posati\"\n                        disabled={saving}\n                        step=\"0.1\"\n                        min=\"0\"\n                        className=\"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600\"\n                        autoFocus\n                      />\n                      <span className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600\">\n                        m\n                      </span>\n                    </div>\n                    {formErrors.metri_posati && (\n                      <p className=\"text-sm text-red-600\">{formErrors.metri_posati}</p>\n                    )}\n                    {formWarnings.metri_posati && (\n                      <p className=\"text-sm text-amber-600\">{formWarnings.metri_posati}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Selezione bobina */}\n            <div className=\"space-y-4\">\n              <h3 className=\"font-semibold text-blue-800 text-lg\">Selezione Bobina</h3>\n\n              {/* Controlli di ricerca e BOBINA VUOTA */}\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center\">\n                  {/* Campo di ricerca - 5 colonne */}\n                  <div className=\"sm:col-span-5\">\n                    <div className=\"relative\">\n                      <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                      <Input\n                        placeholder=\"ID, tipologia, formazione...\"\n                        value={searchText}\n                        onChange={(e) => setSearchText(e.target.value)}\n                        className=\"pl-10\"\n                        disabled={saving}\n                      />\n                      {searchText && (\n                        <Button\n                          type=\"button\"\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className=\"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0\"\n                          onClick={() => setSearchText('')}\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </Button>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Pulsante BOBINA VUOTA - 7 colonne */}\n                  <div className=\"sm:col-span-7\">\n                    <Button\n                      type=\"button\"\n                      variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'default' : 'outline'}\n                      className={`w-full h-10 font-bold flex items-center justify-center gap-2 ${\n                        formData.id_bobina === 'BOBINA_VUOTA'\n                          ? 'bg-green-600 hover:bg-green-700 text-white'\n                          : 'border-blue-400 text-blue-700 hover:bg-blue-50'\n                      }`}\n                      onClick={handleBobinaVuotaSelect}\n                      disabled={saving}\n                    >\n                      {formData.id_bobina === 'BOBINA_VUOTA' && (\n                        <CheckCircle className=\"h-5 w-5\" />\n                      )}\n                      BOBINA VUOTA\n                    </Button>\n                  </div>\n                </div>\n              </div>\n\n              {bobineLoading ? (\n                <div className=\"flex items-center justify-center p-8\">\n                  <Loader2 className=\"h-6 w-6 animate-spin mr-2\" />\n                  <span>Caricamento bobine...</span>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {/* Bobine compatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-green-700 mb-2 flex items-center gap-2\">\n                      <CheckCircle className=\"h-4 w-4\" />\n                      Bobine Compatibili ({bobineCompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineCompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina compatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineCompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-green-100 border-green-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-green-50 hover:border-green-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-green-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Bobine incompatibili */}\n                  <div>\n                    <h4 className=\"font-medium text-amber-700 mb-2 flex items-center gap-2\">\n                      <AlertTriangle className=\"h-4 w-4\" />\n                      Bobine Incompatibili ({bobineIncompatibili.length})\n                    </h4>\n                    <div className=\"max-h-72 overflow-y-auto border rounded-lg\">\n                      {bobineIncompatibili.length === 0 ? (\n                        <div className=\"p-4 text-center text-gray-500\">\n                          Nessuna bobina incompatibile trovata\n                        </div>\n                      ) : (\n                        <div className=\"divide-y\">\n                          {bobineIncompatibili.map((bobina) => (\n                            <div\n                              key={bobina.id_bobina}\n                              className={`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${\n                                formData.id_bobina === bobina.id_bobina\n                                  ? 'bg-amber-100 border-amber-500 shadow-md'\n                                  : 'border-gray-200 hover:bg-amber-50 hover:border-amber-300'\n                              }`}\n                              onClick={() => handleBobinaSelect(bobina)}\n                            >\n                              <div className=\"flex justify-between items-center\">\n                                <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                                  {formData.id_bobina === bobina.id_bobina && (\n                                    <CheckCircle className=\"h-5 w-5 text-amber-600 flex-shrink-0\" />\n                                  )}\n                                  <div className=\"font-bold text-base min-w-fit\">\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </div>\n                                  <div className=\"text-sm text-gray-600 truncate\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </div>\n                                </div>\n                                <Badge variant=\"outline\" className=\"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit\">\n                                  {bobina.metri_residui}m\n                                </Badge>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {bobine.length === 0 && !bobineLoading && (\n                <Alert className=\"border-amber-200 bg-amber-50\">\n                  <AlertTriangle className=\"h-4 w-4 text-amber-600\" />\n                  <AlertDescription className=\"text-amber-800\">\n                    Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina.\n                  </AlertDescription>\n                </Alert>\n              )}\n\n              {formErrors.id_bobina && (\n                <Alert variant=\"destructive\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <AlertDescription>{formErrors.id_bobina}</AlertDescription>\n                </Alert>\n              )}\n            </div>\n          </div>\n\n          <DialogFooter className=\"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center\">\n            {/* Pulsante Modifica Bobina a sinistra (solo se cavo è installato) */}\n            <div>\n              {cavo.stato_installazione === 'installato' && cavo.id_bobina && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => {\n                    setShowModificaBobinaDialog(true)\n                  }}\n                  disabled={saving}\n                  className=\"text-blue-600 border-blue-300 hover:bg-blue-50\"\n                >\n                  Modifica Bobina\n                </Button>\n              )}\n            </div>\n\n            {/* Pulsanti principali a destra */}\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" onClick={handleClose} disabled={saving}>\n                Annulla\n              </Button>\n              <Button\n                onClick={handleSave}\n                disabled={\n                  saving ||\n                  !formData.metri_posati ||\n                  parseFloat(formData.metri_posati) < 0 ||\n                  !formData.id_bobina\n                }\n                className=\"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200\"\n              >\n                {saving && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n                Salva\n              </Button>\n            </div>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog per modifica bobina */}\n      <ModificaBobinaDialog\n        open={showModificaBobinaDialog}\n        onClose={() => setShowModificaBobinaDialog(false)}\n        cavo={cavo}\n        onSuccess={(message) => {\n          onSuccess(message)\n          setShowModificaBobinaDialog(false)\n          onClose() // Chiudi anche il dialog principale dopo il successo\n        }}\n        onError={onError}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AArBA;;;;;;;;;;;;;AAwDe,SAAS,qBAAqB,EAC3C,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,UAAU,YAAY,EACtB,SAAS,EACT,OAAO,EACmB;IAC1B,MAAM,EAAE,UAAU,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzC,qEAAqE;IACrE,MAAM,WAAW,gBAAgB;IAEjC,oBAAoB;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc;QACd,WAAW;IACb;IAEA,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,iDAAiD;YAC3D,UAAU,CAAC,CAAC,SAAS,YAAY;YACjC,WAAW,CAAC,CAAC,SAAS,SAAS;YAC/B,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;IACF,GAAG;QAAC;KAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,CAAC;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oCAAoC;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBAAqB;IACrB,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzE,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,MAAM;YAEhB,IAAI,UAAU;gBACZ;YACF,OAAO,CACP;YAEA,YAAY;gBACV,cAAc;gBACd,WAAW;YACb;YACA,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;QAChB;IACF,GAAG;QAAC;QAAM;QAAM;KAAS;IAEzB,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,YAAY,IAAI,MAAM;YACjC,oBAAoB,WAAW,SAAS,YAAY;QACtD,OAAO;YACL,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAU,CAAC;YAC3D,gBAAgB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,cAAc;gBAAU,CAAC;QAC/D;IACF,GAAG;QAAC,SAAS,YAAY;QAAE;KAAK;IAEhC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM;QAEX,IAAI,SAAqB;YAAE,GAAG,UAAU;QAAC;QACzC,IAAI,WAAyB;YAAE,GAAG,YAAY;QAAC;QAE/C,qDAAqD;QACrD,OAAO,OAAO,YAAY;QAC1B,OAAO,SAAS,YAAY;QAE5B,qDAAqD;QACrD,2CAA2C;QAE3C,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,IAAI,KAAK;YAC3C,SAAS,YAAY,GAAG,CAAC,6DAA6D,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC;QAChH,OAAO,IAAI,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC,GAAG;YAC5C,SAAS,YAAY,GAAG;QAC1B;QAEA,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,gDAAgD;YAC1D,MAAM,CAAC,CAAC;YACR,UAAU,CAAC,CAAC;YACZ,QAAQ,MAAM;YACd,YAAY,UAAU;QACxB;QAEA,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB;QACF;QAEA,IAAI;YACF,iBAAiB;YAEjB,qCAAqC;YACrC,MAAM,WAAW,MAAM,iHAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,WAAW;YAElE,uCAAuC;YACvC,IAAI,aAAa,EAAE;YACnB,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,aAAa;YACf,OAAO,IAAI,YAAY,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACnD,aAAa,SAAS,IAAI;YAC5B,OAAO,IAAI,YAAY,SAAS,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG;gBACxE,aAAa,SAAS,MAAM;YAC9B,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,mEAAmE;YACnE,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC,SAC5C,OAAO,YAAY,KAAK,eACxB,OAAO,YAAY,KAAK,UACxB,OAAO,aAAa,GAAG;YAGzB,IAAI,MAAM;gBACR,QAAQ,GAAG,CAAC,6CAA6C;oBACvD,WAAW,KAAK,SAAS;oBACzB,SAAS,KAAK,OAAO;gBACvB;gBAEA,4CAA4C;gBAC5C,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA;oBAClD,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;oBAC3F,OAAO;gBACT;gBACA,MAAM,uBAAuB,mBAAmB,MAAM,CAAC,CAAA,SACrD,CAAC,CAAC,OAAO,SAAS,KAAK,KAAK,SAAS,IAAI,OAAO,OAAO,KAAK,KAAK,OAAO;gBAG1E,4DAA4D;gBAC5D,kBAAkB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAClE,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBAErE,oEAAoE;gBACpE,MAAM,iBAAiB;uBAAI;uBAAsB;iBAAqB;gBACtE,UAAU;YACZ,OAAO;gBACL,oFAAoF;gBACpF,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;gBACnE,UAAU;YACZ;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sDAAsD;gBAClE,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ;gBACxB,QAAQ,MAAM,QAAQ,EAAE;gBACxB,MAAM,MAAM,QAAQ,EAAE;YACxB;YAEA,oFAAoF;YACpF,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,QAAQ;YACV;YACA,UAAU,EAAE;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,gEAAgE;IAChE,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,aAAa,gBAAgB,OAAO;QACrD,8DAA8D;QAC9D,IAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;YACvC,OAAO,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE;QAChC;QACA,yDAAyD;QACzD,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QAChD,OAAO,SAAS,OAAO,aAAa,IAAI,WAAW;IACrD;IAEA,+BAA+B;IAC/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,eAAe,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACnD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,gBAAgB,iBAAiB,OAAO,aAAa,GAAG;QACjE;IACF;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,OAAO,MAAM,CAAC,CAAA;YACnB,MAAM,iBAAiB,OAAO,SAAS,KAAK,KAAK,SAAS,IACpC,OAAO,OAAO,KAAK,KAAK,OAAO;YACrD,MAAM,gBAAgB,eAAe,MAChB,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClF,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;YAChH,OAAO,kBAAkB,iBAAiB,OAAO,aAAa,GAAG;QACnE;IACF;IAEA,MAAM,oBAAoB;IAC1B,MAAM,sBAAsB;IAE5B,MAAM,qBAAqB,CAAC;QAC1B,oEAAoE;QACpE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW,OAAO,SAAS;YAAC,CAAC;QAE7D,6EAA6E;QAC7E,cAAc,CAAA;YACZ,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,SAAS,CAAE,kCAAkC;;YAC9D,OAAO;QACT;IACF;IAEA,MAAM,0BAA0B;QAC9B,YAAY,CAAA;YACV,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAe;YACrD,OAAO;QACT;QAEA,6EAA6E;QAC7E,cAAc,CAAA;YACZ,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,SAAS,CAAE,kCAAkC;;YAC9D,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,iDAAiD;YAC3D,QAAQ;YACR,cAAc,SAAS,YAAY;YACnC,WAAW;YACX,aAAa,OAAO,IAAI,CAAC,YAAY,MAAM;QAC7C;IACF;IAEA,MAAM,aAAa;QACjB,QAAQ,GAAG,CAAC,+CAA+C;YACzD,MAAM,MAAM;YACZ,cAAc,SAAS,YAAY;YACnC,WAAW,SAAS,SAAS;QAC/B;QAEA,IAAI,CAAC,MAAM;YACT;QACF;QAEA,kDAAkD;QAClD,IAAI,CAAC,SAAS,YAAY,IAAI,WAAW,SAAS,YAAY,IAAI,GAAG;YACnE,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,SAAS,SAAS,EAAE;YACvB,QAAQ;YACR;QACF;QAEA,MAAM,QAAQ,WAAW,SAAS,YAAY;QAE9C,uDAAuD;QACvD,IAAI,SAAS,SAAS,KAAK,gBAAgB;YACzC,MAAM,SAAS,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,SAAS,SAAS;YAClE,IAAI,UAAU,QAAQ,OAAO,aAAa,EAAE;YAC1C,wCAAwC;YACxC,oEAAoE;YACtE;QACF;QAEA,IAAI;YACF,UAAU;YAEV,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,QAAQ,GAAG,CAAC,4DAA4D;gBACtE,UAAU,SAAS,WAAW;gBAC9B,MAAM,KAAK,OAAO;gBAClB,OAAO;gBACP,QAAQ,SAAS,SAAS;gBAC1B,eAAe,SAAS,SAAS,KAAK;YACxC;YAEA,MAAM,iHAAA,CAAA,UAAO,CAAC,iBAAiB,CAC7B,SAAS,WAAW,EACpB,KAAK,OAAO,EACZ,OACA,SAAS,SAAS,EAClB,KAAM,oEAAoE;;YAG5E,UAAU,CAAC,iDAAiD,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YACvF;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,QAAQ;YACX,YAAY;gBAAE,cAAc;gBAAI,WAAW;YAAG;YAC9C,cAAc,CAAC;YACf,gBAAgB,CAAC;YACjB,cAAc;YACd;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAM,cAAc;0BAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;wCACR,KAAK,OAAO;;;;;;;8CAExC,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAmB;oEAAE,KAAK,SAAS,IAAI;;;;;;;0EACpD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAY;oEAAE,KAAK,mBAAmB,IAAI;;;;;;;0EACvD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,OAAO,IAAI;;;;;;;0EACnD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAW;oEAAE,KAAK,iBAAiB,IAAI;;;;;;;0EACpD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAuB;oEAAE,KAAK,aAAa,IAAI;oEAAM;;;;;;;0EAClE,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAoB;oEAAE,KAAK,eAAe,IAAI;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMnE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;0EAAsB;;;;;;0EAGvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,YAAY;wEAC5B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oFAAE,GAAG,IAAI;oFAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gFAAC,CAAC;wEAC/E,aAAY;wEACZ,UAAU;wEACV,MAAK;wEACL,KAAI;wEACJ,WAAU;wEACV,SAAS;;;;;;kFAEX,8OAAC;wEAAK,WAAU;kFAAsF;;;;;;;;;;;;4DAIvG,WAAW,YAAY,kBACtB,8OAAC;gEAAE,WAAU;0EAAwB,WAAW,YAAY;;;;;;4DAE7D,aAAa,YAAY,kBACxB,8OAAC;gEAAE,WAAU;0EAA0B,aAAa,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDAGpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO;oEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oEAC7C,WAAU;oEACV,UAAU;;;;;;gEAEX,4BACC,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,cAAc;8EAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAOrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,SAAS,SAAS,KAAK,iBAAiB,YAAY;4DAC7D,WAAW,CAAC,6DAA6D,EACvE,SAAS,SAAS,KAAK,iBACnB,+CACA,kDACJ;4DACF,SAAS;4DACT,UAAU;;gEAET,SAAS,SAAS,KAAK,gCACtB,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEACvB;;;;;;;;;;;;;;;;;;;;;;;wCAOT,8BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;;;;;iEAGR,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAAY;gEACd,kBAAkB,MAAM;gEAAC;;;;;;;sEAEhD,8OAAC;4DAAI,WAAU;sEACZ,kBAAkB,MAAM,KAAK,kBAC5B,8OAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,8OAAC;gEAAI,WAAU;0EACZ,kBAAkB,GAAG,CAAC,CAAC,uBACtB,8OAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,8OAAC,2NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,8OAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;8DAgCjC,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAY;gEACd,oBAAoB,MAAM;gEAAC;;;;;;;sEAEpD,8OAAC;4DAAI,WAAU;sEACZ,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;gEAAI,WAAU;0EAAgC;;;;;qFAI/C,8OAAC;gEAAI,WAAU;0EACZ,oBAAoB,GAAG,CAAC,CAAC,uBACxB,8OAAC;wEAEC,WAAW,CAAC,2DAA2D,EACrE,SAAS,SAAS,KAAK,OAAO,SAAS,GACnC,4CACA,4DACJ;wEACF,SAAS,IAAM,mBAAmB;kFAElC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;wFACZ,SAAS,SAAS,KAAK,OAAO,SAAS,kBACtC,8OAAC,2NAAA,CAAA,cAAW;4FAAC,WAAU;;;;;;sGAEzB,8OAAC;4FAAI,WAAU;sGACZ,gBAAgB,OAAO,SAAS;;;;;;sGAEnC,8OAAC;4FAAI,WAAU;;gGACZ,OAAO,SAAS;gGAAC;gGAAI,OAAO,OAAO;;;;;;;;;;;;;8FAGxC,8OAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;wFAChC,OAAO,aAAa;wFAAC;;;;;;;;;;;;;uEArBrB,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAiCpC,OAAO,MAAM,KAAK,KAAK,CAAC,+BACvB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC,iIAAA,CAAA,mBAAgB;oDAAC,WAAU;8DAAiB;;;;;;;;;;;;wCAMhD,WAAW,SAAS,kBACnB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC,iIAAA,CAAA,mBAAgB;8DAAE,WAAW,SAAS;;;;;;;;;;;;;;;;;;;;;;;;sCAM/C,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CAEtB,8OAAC;8CACE,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,SAAS,kBAC1D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;4CACP,4BAA4B;wCAC9B;wCACA,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,SAAS;4CAAa,UAAU;sDAAQ;;;;;;sDAGlE,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UACE,UACA,CAAC,SAAS,YAAY,IACtB,WAAW,SAAS,YAAY,IAAI,KACpC,CAAC,SAAS,SAAS;4CAErB,WAAU;;gDAET,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvE,8OAAC,kJAAA,CAAA,UAAoB;gBACnB,MAAM;gBACN,SAAS,IAAM,4BAA4B;gBAC3C,MAAM;gBACN,WAAW,CAAC;oBACV,UAAU;oBACV,4BAA4B;oBAC5B,UAAU,qDAAqD;;gBACjE;gBACA,SAAS;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 10002, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 10030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/CreaComandaDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, ClipboardList, Users } from 'lucide-react'\nimport { comandeApi, responsabiliApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface CreaComandaDialogProps {\n  open: boolean\n  onClose: () => void\n  caviSelezionati: string[]\n  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\ninterface Responsabile {\n  id: number\n  nome_responsabile: string\n  numero_telefono?: string\n  mail?: string\n}\n\nexport default function CreaComandaDialog({\n  open,\n  onClose,\n  caviSelezionati,\n  tipoComanda,\n  onSuccess,\n  onError\n}: CreaComandaDialogProps) {\n  const { cantiere } = useAuth()\n  const [formData, setFormData] = useState({\n    tipo_comanda: tipoComanda || 'POSA',\n    responsabile: '',\n    note: ''\n  })\n  const [responsabili, setResponsabili] = useState<Responsabile[]>([])\n  const [loading, setLoading] = useState(false)\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false)\n  const [error, setError] = useState('')\n\n  // Reset form quando si apre il dialog\n  useEffect(() => {\n    if (open) {\n      setFormData({\n        tipo_comanda: tipoComanda || 'POSA',\n        responsabile: '',\n        note: ''\n      })\n      setError('')\n      loadResponsabili()\n    }\n  }, [open, tipoComanda])\n\n  const loadResponsabili = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoadingResponsabili(true)\n      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)\n      setResponsabili(response.data)\n    } catch (error) {\n      setResponsabili([])\n    } finally {\n      setLoadingResponsabili(false)\n    }\n  }\n\n  const getTipoComandaLabel = (tipo: string) => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa Cavi'\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza'\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo'\n      case 'CERTIFICAZIONE':\n        return 'Certificazione'\n      default:\n        return tipo\n    }\n  }\n\n  const handleCreaComanda = async () => {\n    if (!cantiere) return\n\n    if (!formData.responsabile) {\n      setError('Seleziona un responsabile per la comanda')\n      return\n    }\n\n    if (caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo per la comanda')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n\n      // Crea la comanda con i cavi assegnati\n      const comandaData = {\n        tipo_comanda: formData.tipo_comanda,\n        responsabile: formData.responsabile,\n        note: formData.note || null\n      }\n\n      const response = await comandeApi.createComandaWithCavi(\n        cantiere.id_cantiere,\n        comandaData,\n        caviSelezionati\n      )\n\n      onSuccess(`Comanda ${response.data.codice_comanda} creata con successo per ${caviSelezionati.length} cavi`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <ClipboardList className=\"h-5 w-5\" />\n            Crea Nuova Comanda\n          </DialogTitle>\n          <DialogDescription>\n            Crea una nuova comanda per {caviSelezionati.length} cavi selezionati\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Cavi selezionati */}\n          <div className=\"p-4 bg-gray-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Cavi Selezionati ({caviSelezionati.length})</Label>\n            <div className=\"mt-2 max-h-32 overflow-y-auto\">\n              <div className=\"flex flex-wrap gap-1\">\n                {caviSelezionati.slice(0, 10).map((cavoId) => (\n                  <span\n                    key={cavoId}\n                    className=\"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\"\n                  >\n                    {cavoId}\n                  </span>\n                ))}\n                {caviSelezionati.length > 10 && (\n                  <span className=\"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded\">\n                    +{caviSelezionati.length - 10} altri...\n                  </span>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Tipo comanda */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"tipo\">Tipo Comanda *</Label>\n            <Select\n              value={formData.tipo_comanda}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"POSA\">🔧 Posa Cavi</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_PARTENZA\">🔌 Collegamento Partenza</SelectItem>\n                <SelectItem value=\"COLLEGAMENTO_ARRIVO\">⚡ Collegamento Arrivo</SelectItem>\n                <SelectItem value=\"CERTIFICAZIONE\">📋 Certificazione</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Responsabile */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"responsabile\">Responsabile *</Label>\n            <Select\n              value={formData.responsabile}\n              onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}\n              disabled={loadingResponsabili}\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"Seleziona responsabile...\" />\n              </SelectTrigger>\n              <SelectContent>\n                {responsabili.map((resp) => (\n                  <SelectItem key={resp.id} value={resp.nome_responsabile}>\n                    <div className=\"flex items-center gap-2\">\n                      <Users className=\"h-4 w-4\" />\n                      <span>{resp.nome_responsabile}</span>\n                      {resp.numero_telefono && (\n                        <span className=\"text-xs text-gray-500\">- {resp.numero_telefono}</span>\n                      )}\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Note */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"note\">Note (opzionale)</Label>\n            <Textarea\n              id=\"note\"\n              placeholder=\"Inserisci eventuali note per la comanda...\"\n              value={formData.note}\n              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}\n              rows={3}\n            />\n          </div>\n\n          {/* Riepilogo */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Riepilogo Comanda</Label>\n            <div className=\"mt-2 space-y-1 text-sm\">\n              <div><strong>Tipo:</strong> {getTipoComandaLabel(formData.tipo_comanda)}</div>\n              <div><strong>Responsabile:</strong> {formData.responsabile || 'Non selezionato'}</div>\n              <div><strong>Cavi:</strong> {caviSelezionati.length} selezionati</div>\n              {formData.note && <div><strong>Note:</strong> {formData.note}</div>}\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleCreaComanda}\n            disabled={loading || !formData.responsabile || caviSelezionati.length === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <ClipboardList className=\"h-4 w-4 mr-2\" />}\n            Crea Comanda\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAEA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAzBA;;;;;;;;;;;;AA2Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,eAAe,EACf,WAAW,EACX,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,cAAc,eAAe;QAC7B,cAAc;QACd,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,YAAY;gBACV,cAAc,eAAe;gBAC7B,cAAc;gBACd,MAAM;YACR;YACA,SAAS;YACT;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,uBAAuB;YACvB,MAAM,WAAW,MAAM,iHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,SAAS,WAAW;YAC3E,gBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,OAAO;YACd,gBAAgB,EAAE;QACpB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU;QAEf,IAAI,CAAC,SAAS,YAAY,EAAE;YAC1B,SAAS;YACT;QACF;QAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,uCAAuC;YACvC,MAAM,cAAc;gBAClB,cAAc,SAAS,YAAY;gBACnC,cAAc,SAAS,YAAY;gBACnC,MAAM,SAAS,IAAI,IAAI;YACzB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,qBAAqB,CACrD,SAAS,WAAW,EACpB,aACA;YAGF,UAAU,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,EAAE,gBAAgB,MAAM,CAAC,KAAK,CAAC;YAC1G;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGvC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACW,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;;8BAIvD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;wCAAsB;wCAAmB,gBAAgB,MAAM;wCAAC;;;;;;;8CACjF,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,uBACjC,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,gBAAgB,MAAM,GAAG,oBACxB,8OAAC;gDAAK,WAAU;;oDAAmE;oDAC/E,gBAAgB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;wBAOvC,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;;sDAE/E,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAwB;;;;;;8DAC1C,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAsB;;;;;;8DACxC,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,YAAY;oCAC5B,eAAe,CAAC,QAAU,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,cAAc;4CAAM,CAAC;oCAC/E,UAAU;;sDAEV,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oDAAe,OAAO,KAAK,iBAAiB;8DACrD,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,KAAK,iBAAiB;;;;;;4DAC5B,KAAK,eAAe,kBACnB,8OAAC;gEAAK,WAAU;;oEAAwB;oEAAG,KAAK,eAAe;;;;;;;;;;;;;mDALpD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAehC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;oCACvE,MAAM;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,oBAAoB,SAAS,YAAY;;;;;;;sDACtE,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAsB;gDAAE,SAAS,YAAY,IAAI;;;;;;;sDAC9D,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,gBAAgB,MAAM;gDAAC;;;;;;;wCACnD,SAAS,IAAI,kBAAI,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;8BAKlE,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,SAAS,YAAY,IAAI,gBAAgB,MAAM,KAAK;;gCAEzE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAOtH", "debugId": null}}, {"offset": {"line": 10629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ImportExcelDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport {\n  <PERSON><PERSON>,\n  <PERSON>alogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON>alogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Input } from '@/components/ui/input'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Upload, FileSpreadsheet, CheckCircle } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ImportExcelDialogProps {\n  open: boolean\n  onClose: () => void\n  tipo: 'cavi' | 'bobine'\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ImportExcelDialog({\n  open,\n  onClose,\n  tipo,\n  onSuccess,\n  onError\n}: ImportExcelDialogProps) {\n  const { cantiere } = useAuth()\n  const [file, setFile] = useState<File | null>(null)\n  const [revisione, setRevisione] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0]\n    if (selectedFile) {\n      // Verifica che sia un file Excel\n      const validTypes = [\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'application/vnd.ms-excel'\n      ]\n      \n      if (!validTypes.includes(selectedFile.type) && \n          !selectedFile.name.toLowerCase().endsWith('.xlsx') && \n          !selectedFile.name.toLowerCase().endsWith('.xls')) {\n        setError('Seleziona un file Excel valido (.xlsx o .xls)')\n        return\n      }\n\n      setFile(selectedFile)\n      setError('')\n    }\n  }\n\n  const handleImport = async () => {\n    if (!file || !cantiere) return\n\n    if (tipo === 'cavi' && !revisione.trim()) {\n      setError('Inserisci il codice revisione per l\\'importazione cavi')\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError('')\n      setUploadProgress(0)\n\n      let response\n      if (tipo === 'cavi') {\n        response = await excelApi.importCavi(cantiere.id_cantiere, file, revisione.trim())\n      } else {\n        response = await excelApi.importBobine(cantiere.id_cantiere, file)\n      }\n\n      setUploadProgress(100)\n\n      if (response.data.success) {\n        const details = response.data.details\n        let message = response.data.message\n        \n        if (tipo === 'cavi' && details?.cavi_importati) {\n          message += ` (${details.cavi_importati} cavi importati)`\n        } else if (tipo === 'bobine' && details?.bobine_importate) {\n          message += ` (${details.bobine_importate} bobine importate)`\n        }\n\n        onSuccess(message)\n        onClose()\n      } else {\n        onError(response.data.message || 'Errore durante l\\'importazione')\n      }\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'importazione del file'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n      setUploadProgress(0)\n    }\n  }\n\n  const handleClose = () => {\n    if (!loading) {\n      setFile(null)\n      setRevisione('')\n      setError('')\n      setUploadProgress(0)\n      if (fileInputRef.current) {\n        fileInputRef.current.value = ''\n      }\n      onClose()\n    }\n  }\n\n  const getTipoLabel = () => {\n    return tipo === 'cavi' ? 'Cavi' : 'Bobine'\n  }\n\n  const getFileRequirements = () => {\n    if (tipo === 'cavi') {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'Codice revisione obbligatorio per tracciabilità'\n      ]\n    } else {\n      return [\n        'File Excel (.xlsx o .xls)',\n        'Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.',\n        'Prima riga deve contenere le intestazioni',\n        'I metri residui saranno impostati uguali ai metri totali'\n      ]\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Importa {getTipoLabel()} da Excel\n          </DialogTitle>\n          <DialogDescription>\n            Carica un file Excel per importare {getTipoLabel().toLowerCase()} nel cantiere\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Requisiti file */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Requisiti File</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              {getFileRequirements().map((req, index) => (\n                <li key={index} className=\"flex items-start gap-2\">\n                  <span className=\"text-blue-500 mt-0.5\">•</span>\n                  <span>{req}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Selezione file */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"file\">File Excel *</Label>\n            <div className=\"flex items-center gap-2\">\n              <Input\n                ref={fileInputRef}\n                id=\"file\"\n                type=\"file\"\n                accept=\".xlsx,.xls\"\n                onChange={handleFileSelect}\n                disabled={loading}\n                className=\"flex-1\"\n              />\n              {file && (\n                <div className=\"flex items-center gap-1 text-green-600\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  <span className=\"text-sm\">File selezionato</span>\n                </div>\n              )}\n            </div>\n            {file && (\n              <div className=\"text-sm text-gray-600\">\n                <FileSpreadsheet className=\"h-4 w-4 inline mr-1\" />\n                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)\n              </div>\n            )}\n          </div>\n\n          {/* Revisione (solo per cavi) */}\n          {tipo === 'cavi' && (\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"revisione\">Codice Revisione *</Label>\n              <Input\n                id=\"revisione\"\n                value={revisione}\n                onChange={(e) => setRevisione(e.target.value)}\n                placeholder=\"es. REV001, V1.0, 2024-01\"\n                disabled={loading}\n              />\n              <p className=\"text-sm text-gray-500\">\n                Codice identificativo della revisione per tracciabilità delle modifiche\n              </p>\n            </div>\n          )}\n\n          {/* Progress bar */}\n          {loading && (\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n                <span className=\"text-sm\">Caricamento in corso...</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${uploadProgress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Riepilogo */}\n          {file && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Importazione</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Tipo:</strong> {getTipoLabel()}</div>\n                <div><strong>File:</strong> {file.name}</div>\n                <div><strong>Dimensione:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</div>\n                {tipo === 'cavi' && revisione && (\n                  <div><strong>Revisione:</strong> {revisione}</div>\n                )}\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleImport}\n            disabled={loading || !file || (tipo === 'cavi' && !revisione.trim())}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Upload className=\"h-4 w-4 mr-2\" />}\n            Importa {getTipoLabel()}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA2Be,SAAS,kBAAkB,EACxC,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,SAAS,EACT,OAAO,EACgB;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,iCAAiC;YACjC,MAAM,aAAa;gBACjB;gBACA;aACD;YAED,IAAI,CAAC,WAAW,QAAQ,CAAC,aAAa,IAAI,KACtC,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAC1C,CAAC,aAAa,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBACrD,SAAS;gBACT;YACF;YAEA,QAAQ;YACR,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAExB,IAAI,SAAS,UAAU,CAAC,UAAU,IAAI,IAAI;YACxC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,kBAAkB;YAElB,IAAI;YACJ,IAAI,SAAS,QAAQ;gBACnB,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW,EAAE,MAAM,UAAU,IAAI;YACjF,OAAO;gBACL,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW,EAAE;YAC/D;YAEA,kBAAkB;YAElB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO;gBACrC,IAAI,UAAU,SAAS,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,UAAU,SAAS,gBAAgB;oBAC9C,WAAW,CAAC,EAAE,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;gBAC1D,OAAO,IAAI,SAAS,YAAY,SAAS,kBAAkB;oBACzD,WAAW,CAAC,EAAE,EAAE,QAAQ,gBAAgB,CAAC,kBAAkB,CAAC;gBAC9D;gBAEA,UAAU;gBACV;YACF,OAAO;gBACL,QAAQ,SAAS,IAAI,CAAC,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;YACX,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,QAAQ;YACR,aAAa;YACb,SAAS;YACT,kBAAkB;YAClB,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;YACA;QACF;IACF;IAEA,MAAM,eAAe;QACnB,OAAO,SAAS,SAAS,SAAS;IACpC;IAEA,MAAM,sBAAsB;QAC1B,IAAI,SAAS,QAAQ;YACnB,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH,OAAO;YACL,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCACrB;gCAAe;;;;;;;sCAE1B,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACmB,eAAe,WAAW;gCAAG;;;;;;;;;;;;;8BAIrE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;8CACX,sBAAsB,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4CAAe,WAAU;;8DACxB,8OAAC;oDAAK,WAAU;8DAAuB;;;;;;8DACvC,8OAAC;8DAAM;;;;;;;2CAFA;;;;;;;;;;;;;;;;wBAQd,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAO;;;;;;8CACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,IAAG;4CACH,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,UAAU;4CACV,WAAU;;;;;;wCAEX,sBACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI/B,sBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAC1B,KAAK,IAAI;wCAAC;wCAAG,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;wBAMxD,SAAS,wBACR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAY;;;;;;8CAC3B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,aAAY;oCACZ,UAAU;;;;;;8CAEZ,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;wBAOxC,yBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAE5B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,eAAe,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;wBAO5C,sBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE;;;;;;;sDAC7B,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAc;gDAAE,KAAK,IAAI;;;;;;;sDACtC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAoB;gDAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;wCACvE,SAAS,UAAU,2BAClB,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAmB;gDAAE;;;;;;;sDAEpC,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAa,UAAU;sCAAS;;;;;;sCAGnE,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,CAAC,QAAS,SAAS,UAAU,CAAC,UAAU,IAAI;;gCAEhE,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAkB;gCAC1F;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 11217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/cavi/ExportDataDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Loader2, AlertCircle, Download, FileSpreadsheet, Database } from 'lucide-react'\nimport { excelApi } from '@/lib/api'\nimport { useAuth } from '@/contexts/AuthContext'\n\ninterface ExportDataDialogProps {\n  open: boolean\n  onClose: () => void\n  onSuccess: (message: string) => void\n  onError: (message: string) => void\n}\n\nexport default function ExportDataDialog({\n  open,\n  onClose,\n  onSuccess,\n  onError\n}: ExportDataDialogProps) {\n  const { cantiere } = useAuth()\n  const [selectedExports, setSelectedExports] = useState({\n    cavi: true,\n    bobine: true,\n    comande: false,\n    certificazioni: false,\n    responsabili: false\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleExportChange = (exportType: string, checked: boolean) => {\n    setSelectedExports(prev => ({\n      ...prev,\n      [exportType]: checked\n    }))\n  }\n\n  const handleExportCavi = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportCavi(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `cavi_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export cavi completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei cavi'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportBobine = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      const response = await excelApi.exportBobine(cantiere.id_cantiere)\n      \n      // Crea un link per il download\n      const url = window.URL.createObjectURL(new Blob([response.data]))\n      const link = document.createElement('a')\n      link.href = url\n      link.setAttribute('download', `bobine_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)\n      document.body.appendChild(link)\n      link.click()\n      link.remove()\n      window.URL.revokeObjectURL(url)\n\n      onSuccess('Export bobine completato con successo')\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export delle bobine'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleExportAll = async () => {\n    if (!cantiere) return\n\n    try {\n      setLoading(true)\n      setError('')\n\n      const exports = []\n\n      if (selectedExports.cavi) {\n        exports.push(handleExportCavi())\n      }\n\n      if (selectedExports.bobine) {\n        exports.push(handleExportBobine())\n      }\n\n      // TODO: Implementare export per comande, certificazioni, responsabili\n      if (selectedExports.comande) {\n      }\n\n      if (selectedExports.certificazioni) {\n      }\n\n      if (selectedExports.responsabili) {\n      }\n\n      await Promise.all(exports)\n\n      const exportCount = Object.values(selectedExports).filter(Boolean).length\n      onSuccess(`Export completato: ${exportCount} file scaricati`)\n      onClose()\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\\'export dei dati'\n      onError(errorMessage)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getSelectedCount = () => {\n    return Object.values(selectedExports).filter(Boolean).length\n  }\n\n  const exportOptions = [\n    {\n      key: 'cavi',\n      label: 'Cavi',\n      description: 'Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni',\n      icon: <Database className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'bobine',\n      label: 'Bobine',\n      description: 'Esporta tutte le bobine del parco cavi con metri residui e assegnazioni',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: true\n    },\n    {\n      key: 'comande',\n      label: 'Comande',\n      description: 'Esporta tutte le comande con cavi assegnati e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'certificazioni',\n      label: 'Certificazioni',\n      description: 'Esporta tutte le certificazioni con esiti e responsabili',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    },\n    {\n      key: 'responsabili',\n      label: 'Responsabili',\n      description: 'Esporta tutti i responsabili con contatti e ruoli',\n      icon: <FileSpreadsheet className=\"h-4 w-4\" />,\n      available: false\n    }\n  ]\n\n  return (\n    <Dialog open={open} onOpenChange={onClose}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Download className=\"h-5 w-5\" />\n            Esporta Dati Cantiere\n          </DialogTitle>\n          <DialogDescription>\n            Seleziona i dati da esportare dal cantiere {cantiere?.nome_cantiere}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {error && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{error}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* Opzioni di export */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Seleziona Dati da Esportare</Label>\n            \n            {exportOptions.map((option) => (\n              <div\n                key={option.key}\n                className={`flex items-start space-x-3 p-3 rounded-lg border ${\n                  option.available ? 'bg-white' : 'bg-gray-50'\n                }`}\n              >\n                <Checkbox\n                  id={option.key}\n                  checked={selectedExports[option.key as keyof typeof selectedExports]}\n                  onCheckedChange={(checked) => handleExportChange(option.key, checked as boolean)}\n                  disabled={!option.available || loading}\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    {option.icon}\n                    <Label\n                      htmlFor={option.key}\n                      className={`font-medium ${!option.available ? 'text-gray-500' : ''}`}\n                    >\n                      {option.label}\n                      {!option.available && (\n                        <span className=\"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded\">\n                          In sviluppo\n                        </span>\n                      )}\n                    </Label>\n                  </div>\n                  <p className={`text-sm mt-1 ${!option.available ? 'text-gray-400' : 'text-gray-600'}`}>\n                    {option.description}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Informazioni export */}\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <Label className=\"text-sm font-medium\">Informazioni Export</Label>\n            <ul className=\"mt-2 space-y-1 text-sm text-gray-600\">\n              <li>• I file saranno scaricati in formato Excel (.xlsx)</li>\n              <li>• I nomi file includeranno data e nome cantiere</li>\n              <li>• I dati esportati riflettono lo stato attuale del database</li>\n              <li>• L'export non modifica i dati originali</li>\n            </ul>\n          </div>\n\n          {/* Riepilogo */}\n          {getSelectedCount() > 0 && (\n            <div className=\"p-4 bg-gray-50 rounded-lg\">\n              <Label className=\"text-sm font-medium\">Riepilogo Export</Label>\n              <div className=\"mt-2 space-y-1 text-sm\">\n                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>\n                <div><strong>File da scaricare:</strong> {getSelectedCount()}</div>\n                <div><strong>Data export:</strong> {new Date().toLocaleDateString('it-IT')}</div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleExportAll}\n            disabled={loading || getSelectedCount() === 0}\n          >\n            {loading ? <Loader2 className=\"h-4 w-4 animate-spin mr-2\" /> : <Download className=\"h-4 w-4 mr-2\" />}\n            Esporta {getSelectedCount() > 0 ? `(${getSelectedCount()})` : ''}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAjBA;;;;;;;;;;;AA0Be,SAAS,iBAAiB,EACvC,IAAI,EACJ,OAAO,EACP,SAAS,EACT,OAAO,EACe;IACtB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,MAAM;QACN,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,WAAW,EAAE;YAChB,CAAC;IACH;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,SAAS,WAAW;YAE/D,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,iHAAA,CAAA,WAAQ,CAAC,YAAY,CAAC,SAAS,WAAW;YAEjE,+BAA+B;YAC/B,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC,SAAS,IAAI;aAAC;YAC/D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC/G,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,KAAK,MAAM;YACX,OAAO,GAAG,CAAC,eAAe,CAAC;YAE3B,UAAU;QACZ,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,UAAU,EAAE;YAElB,IAAI,gBAAgB,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,IAAI,gBAAgB,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC;YACf;YAEA,sEAAsE;YACtE,IAAI,gBAAgB,OAAO,EAAE,CAC7B;YAEA,IAAI,gBAAgB,cAAc,EAAE,CACpC;YAEA,IAAI,gBAAgB,YAAY,EAAE,CAClC;YAEA,MAAM,QAAQ,GAAG,CAAC;YAElB,MAAM,cAAc,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;YACzE,UAAU,CAAC,mBAAmB,EAAE,YAAY,eAAe,CAAC;YAC5D;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,SAAS,MAAM;IAC9D;IAEA,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;QACA;YACE,KAAK;YACL,OAAO;YACP,aAAa;YACb,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,WAAW;QACb;KACD;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCAC2B,UAAU;;;;;;;;;;;;;8BAI1D,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAKvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;gCAEtC,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAEC,WAAW,CAAC,iDAAiD,EAC3D,OAAO,SAAS,GAAG,aAAa,cAChC;;0DAEF,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,OAAO,GAAG;gDACd,SAAS,eAAe,CAAC,OAAO,GAAG,CAAiC;gDACpE,iBAAiB,CAAC,UAAY,mBAAmB,OAAO,GAAG,EAAE;gDAC7D,UAAU,CAAC,OAAO,SAAS,IAAI;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,IAAI;0EACZ,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,OAAO,GAAG;gEACnB,WAAW,CAAC,YAAY,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,IAAI;;oEAEnE,OAAO,KAAK;oEACZ,CAAC,OAAO,SAAS,kBAChB,8OAAC;wEAAK,WAAU;kFAA2D;;;;;;;;;;;;;;;;;;kEAMjF,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,CAAC,OAAO,SAAS,GAAG,kBAAkB,iBAAiB;kEAClF,OAAO,WAAW;;;;;;;;;;;;;uCA3BlB,OAAO,GAAG;;;;;;;;;;;sCAmCrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAKP,qBAAqB,mBACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAsB;;;;;;8CACvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAkB;gDAAE,UAAU;;;;;;;sDAC3C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAA2B;gDAAE;;;;;;;sDAC1C,8OAAC;;8DAAI,8OAAC;8DAAO;;;;;;gDAAqB;gDAAE,IAAI,OAAO,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAM1E,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAS;;;;;;sCAG/D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,WAAW,uBAAuB;;gCAE3C,wBAAU,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAkB;gCAC5F,qBAAqB,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;AAM1E", "debugId": null}}, {"offset": {"line": 11754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cavi/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCantiere } from '@/hooks/useCantiere'\nimport { CantiereErrorBoundary } from '@/components/cantiere/CantiereErrorBoundary'\nimport { caviApi } from '@/lib/api'\nimport { Cavo } from '@/types'\nimport CaviTable from '@/components/cavi/CaviTable'\nimport CaviStatistics from '@/components/cavi/CaviStatistics'\nimport InserisciMetriDialog from '@/components/cavi/InserisciMetriDialog'\n// Import dei nuovi componenti modali migliorati\nimport {\n  DisconnectCableModal,\n  GeneratePdfModal,\n  CertificationModal,\n  CertificationErrorModal,\n  SuccessToast\n} from '@/components/cavi/modals/CableActionModals'\nimport {\n  ModificaBobinaModal,\n  InserisciMetriModal\n} from '@/components/cavi/modals/BobinaManagementModals'\n// Import CSS per i modali migliorati\nimport '@/components/cavi/modals/enhanced-modals.css'\n// Import del vecchio dialog per InserisciMetri (temporaneo)\nimport InserisciMetriDialogOld from '@/components/cavi/InserisciMetriDialog'\nimport CreaComandaDialog from '@/components/cavi/CreaComandaDialog'\nimport ImportExcelDialog from '@/components/cavi/ImportExcelDialog'\nimport ExportDataDialog from '@/components/cavi/ExportDataDialog'\n// import { useToast } from '@/hooks/use-toast'\nimport {\n  Package,\n  AlertCircle,\n  Loader2\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totali: number\n  installati: number\n  collegati: number\n  certificati: number\n  percentualeInstallazione: number\n  percentualeCollegamento: number\n  percentualeCertificazione: number\n  metriTotali: number\n  metriInstallati: number\n  metriCollegati: number\n  metriCertificati: number\n}\n\nexport default function CaviPage() {\n  const { user, isAuthenticated, isLoading: authLoading } = useAuth()\n  const { cantiereId, cantiere, isValidCantiere, isLoading: cantiereLoading, error: cantiereError } = useCantiere()\n  const router = useRouter()\n\n\n\n  // Sistema toast semplice\n  const toast = ({ title, description, variant }: { title: string, description: string, variant?: string }) => {\n    // TODO: Implementare sistema toast visuale\n  }\n  const [cavi, setCavi] = useState<Cavo[]>([])\n  const [caviSpare, setCaviSpare] = useState<Cavo[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [selectedCavi, setSelectedCavi] = useState<string[]>([])\n  const [selectionEnabled, setSelectionEnabled] = useState(true)\n  const [filteredCavi, setFilteredCavi] = useState<Cavo[]>([])\n  const [revisioneCorrente, setRevisioneCorrente] = useState<string>('')\n\n  // Update filtered cavi when main cavi change\n  useEffect(() => {\n    setFilteredCavi(cavi)\n  }, [cavi])\n\n  // Stati per i dialoghi\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  // Stati per i nuovi modali migliorati\n  const [disconnectModal, setDisconnectModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [generatePdfModal, setGeneratePdfModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificationModal, setCertificationModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n  }>({ open: false, cavo: null })\n\n  const [certificationErrorModal, setCertificationErrorModal] = useState<{\n    open: boolean\n    cavo: Cavo | null\n    error: string\n  }>({ open: false, cavo: null, error: '' })\n\n  const [successToast, setSuccessToast] = useState<{\n    visible: boolean\n    message: string\n  }>({ visible: false, message: '' })\n\n  const [creaComandaDialog, setCreaComandaDialog] = useState<{\n    open: boolean\n    tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'\n  }>({ open: false })\n\n  const [importExcelDialog, setImportExcelDialog] = useState<{\n    open: boolean\n    tipo?: 'cavi' | 'bobine'\n  }>({ open: false })\n\n  const [exportDataDialog, setExportDataDialog] = useState(false)\n  const [stats, setStats] = useState<DashboardStats>({\n    totali: 0,\n    installati: 0,\n    collegati: 0,\n    certificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriCollegati: 0,\n    metriCertificati: 0\n  })\n\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, authLoading, router])\n\n  // Crea oggetto cantiere per il dialog\n  const cantiereForDialog = cantiere || (cantiereId && cantiereId > 0 ? {\n    id_cantiere: cantiereId,\n    commessa: `Cantiere ${cantiereId}`\n  } : null)\n\n  // Carica i cavi dal backend - MIGLIORATO con nuovo hook\n  useEffect(() => {\n    if (isValidCantiere && cantiereId && cantiereId > 0 && !cantiereLoading) {\n\n      loadCavi()\n      loadRevisioneCorrente()\n    } else if (!cantiereLoading && !isValidCantiere) {\n\n      setCavi([])\n      setCaviSpare([])\n      setError(cantiereError || 'Nessun cantiere selezionato')\n    }\n  }, [cantiereId, isValidCantiere, cantiereLoading, cantiereError])\n\n  const loadRevisioneCorrente = async () => {\n    try {\n      const response = await fetch(`http://localhost:8001/api/cavi/${cantiereId}/revisione-corrente`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        setRevisioneCorrente(data.revisione_corrente || '00')\n      } else {\n        setRevisioneCorrente('00')\n      }\n    } catch (error) {\n      setRevisioneCorrente('00')\n    }\n  }\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      // Prima prova con l'API normale\n      try {\n        const data = await caviApi.getCavi(cantiereId!)\n\n        // Separa cavi attivi e spare\n        const caviAttivi = data.filter((cavo: Cavo) => !cavo.spare)\n        const caviSpareFiltered = data.filter((cavo: Cavo) => cavo.spare)\n\n        setCavi(caviAttivi)\n        setCaviSpare(caviSpareFiltered)\n\n        // Calcola statistiche\n        calculateStats(caviAttivi)\n\n      } catch (apiError: any) {\n        throw apiError\n      }\n\n    } catch (error: any) {\n      setError(`Errore nel caricamento dei cavi: ${error.response?.data?.detail || error.message}`)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateStats = (caviData: Cavo[]) => {\n    const totali = caviData.length\n    const installati = caviData.filter(c => (c.metri_posati || c.metratura_reale || 0) > 0).length\n    const collegati = caviData.filter(c => (c.collegamento || c.collegamenti) === 3).length // 3 = collegato\n    const certificati = caviData.filter(c => c.certificato).length\n\n    const metriTotali = caviData.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)\n    const metriInstallati = caviData.reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCollegati = caviData.filter(c => c.collegamento === 3).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n    const metriCertificati = caviData.filter(c => c.certificato).reduce((sum, c) => sum + (c.metri_posati || 0), 0)\n\n    setStats({\n      totali,\n      installati,\n      collegati,\n      certificati,\n      percentualeInstallazione: totali > 0 ? Math.round((installati / totali) * 100) : 0,\n      percentualeCollegamento: totali > 0 ? Math.round((collegati / totali) * 100) : 0,\n      percentualeCertificazione: totali > 0 ? Math.round((certificati / totali) * 100) : 0,\n      metriTotali,\n      metriInstallati,\n      metriCollegati,\n      metriCertificati\n    })\n  }\n\n  // Gestione azioni sui cavi con i nuovi modali migliorati\n  const handleStatusAction = (cavo: Cavo, action: string, label?: string) => {\n\n    switch (action) {\n      case 'insert_meters':\n        setInserisciMetriDialog({ open: true, cavo })\n        break\n      case 'modify_reel':\n        setModificaBobinaDialog({ open: true, cavo })\n        break\n      case 'view_command':\n        toast({\n          title: \"Visualizza Comanda\",\n          description: `Apertura comanda ${label} per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'connect_cable':\n      case 'connect_arrival':\n      case 'connect_departure':\n      case 'manage_connections':\n        // Usa il nuovo modal di certificazione per gestione collegamenti\n        setCertificationModal({ open: true, cavo })\n        break\n      case 'disconnect_cable':\n        // Usa il nuovo modal di disconnessione\n        setDisconnectModal({ open: true, cavo })\n        break\n      case 'create_certificate':\n        // Usa il nuovo modal di certificazione\n        setCertificationModal({ open: true, cavo })\n        break\n      case 'generate_pdf':\n        // Usa il nuovo modal di generazione PDF\n        setGeneratePdfModal({ open: true, cavo })\n        break\n    }\n  }\n\n  const handleContextMenuAction = (cavo: Cavo, action: string) => {\n\n    switch (action) {\n      case 'view_details':\n        toast({\n          title: \"Visualizza Dettagli\",\n          description: `Apertura dettagli per cavo ${cavo.id_cavo}`,\n        })\n        break\n      case 'edit':\n        toast({\n          title: \"Modifica Cavo\",\n          description: \"Funzione modifica cavo in sviluppo\",\n        })\n        break\n      case 'delete':\n        toast({\n          title: \"Elimina Cavo\",\n          description: \"Funzione eliminazione cavo in sviluppo\",\n          variant: \"destructive\"\n        })\n        break\n      case 'add_new':\n        toast({\n          title: \"Aggiungi Nuovo Cavo\",\n          description: \"Funzione aggiunta nuovo cavo in sviluppo\",\n        })\n        break\n      case 'select':\n        const isSelected = selectedCavi.includes(cavo.id_cavo)\n        if (isSelected) {\n          setSelectedCavi(selectedCavi.filter(id => id !== cavo.id_cavo))\n          toast({\n            title: \"Cavo Deselezionato\",\n            description: `Cavo ${cavo.id_cavo} deselezionato`,\n          })\n        } else {\n          setSelectedCavi([...selectedCavi, cavo.id_cavo])\n          toast({\n            title: \"Cavo Selezionato\",\n            description: `Cavo ${cavo.id_cavo} selezionato`,\n          })\n        }\n        break\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo)\n        toast({\n          title: \"ID Copiato\",\n          description: `ID cavo ${cavo.id_cavo} copiato negli appunti`,\n        })\n        break\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}, Tipologia: ${cavo.tipologia}, Formazione: ${cavo.formazione || cavo.sezione}, Metri: ${cavo.metri_teorici}`\n        navigator.clipboard.writeText(details)\n        toast({\n          title: \"Dettagli Copiati\",\n          description: \"Dettagli cavo copiati negli appunti\",\n        })\n        break\n      case 'add_to_command':\n        toast({\n          title: \"Aggiungi a Comanda\",\n          description: \"Funzione aggiunta a comanda in sviluppo\",\n        })\n        break\n      case 'remove_from_command':\n        toast({\n          title: \"Rimuovi da Comanda\",\n          description: \"Funzione rimozione da comanda in sviluppo\",\n        })\n        break\n      case 'create_command_posa':\n        setCreaComandaDialog({ open: true, tipoComanda: 'POSA' })\n        break\n      case 'create_command_collegamento_partenza':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_PARTENZA' })\n        break\n      case 'create_command_collegamento_arrivo':\n        setCreaComandaDialog({ open: true, tipoComanda: 'COLLEGAMENTO_ARRIVO' })\n        break\n      case 'create_command_certificazione':\n        setCreaComandaDialog({ open: true, tipoComanda: 'CERTIFICAZIONE' })\n        break\n      case 'add_multiple_to_command':\n        toast({\n          title: \"Aggiungi Tutti a Comanda\",\n          description: \"Funzione aggiunta multipla a comanda in sviluppo\",\n        })\n        break\n      case 'remove_multiple_from_commands':\n        toast({\n          title: \"Rimuovi Tutti dalle Comande\",\n          description: \"Funzione rimozione multipla dalle comande in sviluppo\",\n        })\n        break\n      default:\n        toast({\n          title: \"Azione non implementata\",\n          description: `Azione ${action} non ancora implementata`,\n        })\n        break\n    }\n  }\n\n  // Gestione successo/errore dialoghi\n  const handleDialogSuccess = (message: string) => {\n    toast({\n      title: \"Operazione completata\",\n      description: message,\n    })\n    // Ricarica i dati\n    loadCavi()\n  }\n\n  const handleDialogError = (message: string) => {\n    toast({\n      title: \"Errore\",\n      description: message,\n      variant: \"destructive\"\n    })\n  }\n\n  // Handler specifico per ModificaBobinaModal - USA LE API SPECIFICHE DEL BACKEND\n  const handleModificaBobina = async (cavoId: string, bobinaId: string, option: string) => {\n    try {\n      if (!cantiere) {\n        throw new Error('Cantiere non selezionato')\n      }\n\n      let message = ''\n\n      console.log('🚀 ModificaBobina: Operazione:', {\n        cantiere: cantiere.id_cantiere,\n        cavo: cavoId,\n        option,\n        bobinaId\n      })\n\n      switch (option) {\n        case 'cambia-bobina':\n          // Usa l'API specifica updateBobina del backend\n          await caviApi.updateBobina(\n            cantiere.id_cantiere,\n            cavoId,\n            bobinaId,\n            true // force_over per bobine incompatibili\n          )\n          message = `Bobina ${bobinaId} assegnata al cavo ${cavoId}`\n          break\n\n        case 'bobina-vuota':\n          // Usa l'API specifica updateBobina con BOBINA_VUOTA\n          await caviApi.updateBobina(\n            cantiere.id_cantiere,\n            cavoId,\n            'BOBINA_VUOTA',\n            false\n          )\n          message = `Bobina rimossa dal cavo ${cavoId}`\n          break\n\n        case 'annulla-posa':\n          // Usa l'API specifica cancelInstallation del backend\n          await caviApi.cancelInstallation(\n            cantiere.id_cantiere,\n            cavoId\n          )\n          message = `Installazione annullata per il cavo ${cavoId} - metri restituiti alla bobina`\n          break\n\n        default:\n          throw new Error('Operazione non riconosciuta')\n      }\n\n      handleDialogSuccess(message)\n      loadCavi() // Ricarica i dati per vedere le modifiche\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'\n      handleDialogError(errorMessage)\n    }\n  }\n\n  // Gestione dei nuovi modali migliorati\n  const handleDisconnectSuccess = () => {\n    setSuccessToast({ visible: true, message: 'Cavo scollegato con successo' })\n    setDisconnectModal({ open: false, cavo: null })\n    loadCavi() // Ricarica i dati\n  }\n\n  const handleDisconnectError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: disconnectModal.cavo,\n      error\n    })\n    setDisconnectModal({ open: false, cavo: null })\n  }\n\n  const handlePdfSuccess = () => {\n    setSuccessToast({ visible: true, message: 'PDF generato con successo' })\n    setGeneratePdfModal({ open: false, cavo: null })\n  }\n\n  const handlePdfError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: generatePdfModal.cavo,\n      error\n    })\n    setGeneratePdfModal({ open: false, cavo: null })\n  }\n\n  const handleCertificationSuccess = () => {\n    setSuccessToast({ visible: true, message: 'Certificazione completata con successo' })\n    setCertificationModal({ open: false, cavo: null })\n    loadCavi() // Ricarica i dati\n  }\n\n  const handleCertificationError = (error: string) => {\n    setCertificationErrorModal({\n      open: true,\n      cavo: certificationModal.cavo,\n      error\n    })\n    setCertificationModal({ open: false, cavo: null })\n  }\n\n  // Mostra loader se stiamo caricando i dati dei cavi\n  if (loading && isValidCantiere) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n        <span className=\"ml-2\">Caricamento cavi...</span>\n      </div>\n    )\n  }\n\n  return (\n    <CantiereErrorBoundary>\n      <div className=\"max-w-[90%] mx-auto p-6\">\n\n        {/* Mostra errore specifico dei cavi se presente */}\n        {error && (\n          <Alert variant=\"destructive\" className=\"mb-6\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        {/* Statistics */}\n        <CaviStatistics\n        cavi={cavi}\n        filteredCavi={filteredCavi}\n        revisioneCorrente={revisioneCorrente}\n        className=\"mb-2\"\n      />\n\n      {/* Tabella Cavi Attivi */}\n      <div className=\"mb-8\">\n        <CaviTable\n          cavi={cavi}\n          loading={loading}\n          selectionEnabled={selectionEnabled}\n          selectedCavi={selectedCavi}\n          onSelectionChange={setSelectedCavi}\n          onStatusAction={handleStatusAction}\n          onContextMenuAction={handleContextMenuAction}\n        />\n      </div>\n\n      {/* Tabella Cavi Spare */}\n      {caviSpare.length > 0 && (\n        <div className=\"mb-8\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Package className=\"h-5 w-5\" />\n                <span>Cavi Spare ({caviSpare.length})</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CaviTable\n                cavi={caviSpare}\n                loading={loading}\n                selectionEnabled={false}\n                onStatusAction={handleStatusAction}\n                onContextMenuAction={handleContextMenuAction}\n              />\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n\n\n      {/* Nuovi Modali Migliorati */}\n      <InserisciMetriDialogOld\n        open={inserisciMetriDialog.open}\n        onClose={() => setInserisciMetriDialog({ open: false, cavo: null })}\n        cavo={inserisciMetriDialog.cavo}\n        cantiere={cantiereForDialog}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ModificaBobinaModal\n        open={modificaBobinaDialog.open}\n        onClose={() => setModificaBobinaDialog({ open: false, cavo: null })}\n        cavo={modificaBobinaDialog.cavo}\n        onSave={handleModificaBobina}\n      />\n\n      <DisconnectCableModal\n        open={disconnectModal.open}\n        onClose={() => setDisconnectModal({ open: false, cavo: null })}\n        cavo={disconnectModal.cavo}\n        onConfirm={handleDisconnectSuccess}\n        onError={handleDisconnectError}\n      />\n\n      <GeneratePdfModal\n        open={generatePdfModal.open}\n        onClose={() => setGeneratePdfModal({ open: false, cavo: null })}\n        cavo={generatePdfModal.cavo}\n        onSuccess={handlePdfSuccess}\n        onError={handlePdfError}\n      />\n\n      <CertificationModal\n        open={certificationModal.open}\n        onClose={() => setCertificationModal({ open: false, cavo: null })}\n        cavo={certificationModal.cavo}\n        onSuccess={handleCertificationSuccess}\n        onError={handleCertificationError}\n      />\n\n      <CertificationErrorModal\n        open={certificationErrorModal.open}\n        onClose={() => setCertificationErrorModal({ open: false, cavo: null, error: '' })}\n        cavo={certificationErrorModal.cavo}\n        error={certificationErrorModal.error}\n        onRetry={() => {\n          setCertificationErrorModal({ open: false, cavo: null, error: '' })\n          // Riapri il modal appropriato basato sul contesto\n          if (certificationErrorModal.cavo) {\n            setCertificationModal({ open: true, cavo: certificationErrorModal.cavo })\n          }\n        }}\n      />\n\n      <SuccessToast\n        visible={successToast.visible}\n        message={successToast.message}\n        onClose={() => setSuccessToast({ visible: false, message: '' })}\n      />\n\n      <CreaComandaDialog\n        open={creaComandaDialog.open}\n        onClose={() => setCreaComandaDialog({ open: false })}\n        caviSelezionati={selectedCavi}\n        tipoComanda={creaComandaDialog.tipoComanda}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ImportExcelDialog\n        open={importExcelDialog.open}\n        onClose={() => setImportExcelDialog({ open: false })}\n        tipo={importExcelDialog.tipo || 'cavi'}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n\n      <ExportDataDialog\n        open={exportDataDialog}\n        onClose={() => setExportDataDialog(false)}\n        onSuccess={handleDialogSuccess}\n        onError={handleDialogError}\n      />\n      </div>\n    </CantiereErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA,gDAAgD;AAChD;AAOA;AAMA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AAAA;AAAA;AAnCA;;;;;;;;;;;;;;;;;;;;AAuDe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC9G,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAIvB,yBAAyB;IACzB,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAA4D;IACtG,2CAA2C;IAC7C;IACA,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;KAAK;IAET,uBAAuB;IACvB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5D;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,sCAAsC;IACtC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGlD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGpD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGxD;QAAE,MAAM;QAAO,MAAM;IAAK;IAE7B,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIlE;QAAE,MAAM;QAAO,MAAM;QAAM,OAAO;IAAG;IAExC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAG5C;QAAE,SAAS;QAAO,SAAS;IAAG;IAEjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGtD;QAAE,MAAM;IAAM;IAEjB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,aAAa;QACb,0BAA0B;QAC1B,yBAAyB;QACzB,2BAA2B;QAC3B,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;IACpB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,sCAAsC;IACtC,MAAM,oBAAoB,YAAY,CAAC,cAAc,aAAa,IAAI;QACpE,aAAa;QACb,UAAU,CAAC,SAAS,EAAE,YAAY;IACpC,IAAI,IAAI;IAER,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,cAAc,aAAa,KAAK,CAAC,iBAAiB;YAEvE;YACA;QACF,OAAO,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;YAE/C,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS,iBAAiB;QAC5B;IACF,GAAG;QAAC;QAAY;QAAiB;QAAiB;KAAc;IAEhE,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,+BAA+B,EAAE,WAAW,mBAAmB,CAAC,EAAE;gBAC9F,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,UAAU;oBAC1D,gBAAgB;gBAClB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,qBAAqB,KAAK,kBAAkB,IAAI;YAClD,OAAO;gBACL,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,qBAAqB;QACvB;IACF;IAEA,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,SAAS;YAET,gCAAgC;YAChC,IAAI;gBACF,MAAM,OAAO,MAAM,iHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAEnC,6BAA6B;gBAC7B,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,OAAe,CAAC,KAAK,KAAK;gBAC1D,MAAM,oBAAoB,KAAK,MAAM,CAAC,CAAC,OAAe,KAAK,KAAK;gBAEhE,QAAQ;gBACR,aAAa;gBAEb,sBAAsB;gBACtB,eAAe;YAEjB,EAAE,OAAO,UAAe;gBACtB,MAAM;YACR;QAEF,EAAE,OAAO,OAAY;YACnB,SAAS,CAAC,iCAAiC,EAAE,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,EAAE;QAC9F,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,SAAS,MAAM;QAC9B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,eAAe,IAAI,CAAC,IAAI,GAAG,MAAM;QAC9F,MAAM,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,YAAY,IAAI,EAAE,YAAY,MAAM,GAAG,MAAM,CAAC,gBAAgB;;QACxG,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAE9D,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,aAAa,IAAI,CAAC,GAAG;QAC9E,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QACjF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,GAAG,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAClH,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC,GAAG;QAE7G,SAAS;YACP;YACA;YACA;YACA;YACA,0BAA0B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,aAAa,SAAU,OAAO;YACjF,yBAAyB,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,SAAU,OAAO;YAC/E,2BAA2B,SAAS,IAAI,KAAK,KAAK,CAAC,AAAC,cAAc,SAAU,OAAO;YACnF;YACA;YACA;YACA;QACF;IACF;IAEA,yDAAyD;IACzD,MAAM,qBAAqB,CAAC,MAAY,QAAgB;QAEtD,OAAQ;YACN,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,wBAAwB;oBAAE,MAAM;oBAAM;gBAAK;gBAC3C;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,iBAAiB,EAAE,MAAM,UAAU,EAAE,KAAK,OAAO,EAAE;gBACnE;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,uCAAuC;gBACvC,mBAAmB;oBAAE,MAAM;oBAAM;gBAAK;gBACtC;YACF,KAAK;gBACH,uCAAuC;gBACvC,sBAAsB;oBAAE,MAAM;oBAAM;gBAAK;gBACzC;YACF,KAAK;gBACH,wCAAwC;gBACxC,oBAAoB;oBAAE,MAAM;oBAAM;gBAAK;gBACvC;QACJ;IACF;IAEA,MAAM,0BAA0B,CAAC,MAAY;QAE3C,OAAQ;YACN,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,2BAA2B,EAAE,KAAK,OAAO,EAAE;gBAC3D;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM,aAAa,aAAa,QAAQ,CAAC,KAAK,OAAO;gBACrD,IAAI,YAAY;oBACd,gBAAgB,aAAa,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,OAAO;oBAC7D,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,cAAc,CAAC;oBACnD;gBACF,OAAO;oBACL,gBAAgB;2BAAI;wBAAc,KAAK,OAAO;qBAAC;oBAC/C,MAAM;wBACJ,OAAO;wBACP,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,YAAY,CAAC;oBACjD;gBACF;gBACA;YACF,KAAK;gBACH,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,OAAO;gBAC1C,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,sBAAsB,CAAC;gBAC9D;gBACA;YACF,KAAK;gBACH,MAAM,UAAU,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,aAAa,EAAE,KAAK,SAAS,CAAC,cAAc,EAAE,KAAK,UAAU,IAAI,KAAK,OAAO,CAAC,SAAS,EAAE,KAAK,aAAa,EAAE;gBACjJ,UAAU,SAAS,CAAC,SAAS,CAAC;gBAC9B,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAO;gBACvD;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAwB;gBACxE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAsB;gBACtE;YACF,KAAK;gBACH,qBAAqB;oBAAE,MAAM;oBAAM,aAAa;gBAAiB;gBACjE;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA;YACF;gBACE,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,OAAO,EAAE,OAAO,wBAAwB,CAAC;gBACzD;gBACA;QACJ;IACF;IAEA,oCAAoC;IACpC,MAAM,sBAAsB,CAAC;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QACA,kBAAkB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;IACF;IAEA,gFAAgF;IAChF,MAAM,uBAAuB,OAAO,QAAgB,UAAkB;QACpE,IAAI;YACF,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,UAAU;YAEd,QAAQ,GAAG,CAAC,kCAAkC;gBAC5C,UAAU,SAAS,WAAW;gBAC9B,MAAM;gBACN;gBACA;YACF;YAEA,OAAQ;gBACN,KAAK;oBACH,+CAA+C;oBAC/C,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY,CACxB,SAAS,WAAW,EACpB,QACA,UACA,KAAK,sCAAsC;;oBAE7C,UAAU,CAAC,OAAO,EAAE,SAAS,mBAAmB,EAAE,QAAQ;oBAC1D;gBAEF,KAAK;oBACH,oDAAoD;oBACpD,MAAM,iHAAA,CAAA,UAAO,CAAC,YAAY,CACxB,SAAS,WAAW,EACpB,QACA,gBACA;oBAEF,UAAU,CAAC,wBAAwB,EAAE,QAAQ;oBAC7C;gBAEF,KAAK;oBACH,qDAAqD;oBACrD,MAAM,iHAAA,CAAA,UAAO,CAAC,kBAAkB,CAC9B,SAAS,WAAW,EACpB;oBAEF,UAAU,CAAC,oCAAoC,EAAE,OAAO,+BAA+B,CAAC;oBACxF;gBAEF;oBACE,MAAM,IAAI,MAAM;YACpB;YAEA,oBAAoB;YACpB,WAAW,0CAA0C;;QACvD,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU,MAAM,OAAO,IAAI;YACtE,kBAAkB;QACpB;IACF;IAEA,uCAAuC;IACvC,MAAM,0BAA0B;QAC9B,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAA+B;QACzE,mBAAmB;YAAE,MAAM;YAAO,MAAM;QAAK;QAC7C,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,2BAA2B;YACzB,MAAM;YACN,MAAM,gBAAgB,IAAI;YAC1B;QACF;QACA,mBAAmB;YAAE,MAAM;YAAO,MAAM;QAAK;IAC/C;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAA4B;QACtE,oBAAoB;YAAE,MAAM;YAAO,MAAM;QAAK;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,2BAA2B;YACzB,MAAM;YACN,MAAM,iBAAiB,IAAI;YAC3B;QACF;QACA,oBAAoB;YAAE,MAAM;YAAO,MAAM;QAAK;IAChD;IAEA,MAAM,6BAA6B;QACjC,gBAAgB;YAAE,SAAS;YAAM,SAAS;QAAyC;QACnF,sBAAsB;YAAE,MAAM;YAAO,MAAM;QAAK;QAChD,WAAW,kBAAkB;;IAC/B;IAEA,MAAM,2BAA2B,CAAC;QAChC,2BAA2B;YACzB,MAAM;YACN,MAAM,mBAAmB,IAAI;YAC7B;QACF;QACA,sBAAsB;YAAE,MAAM;YAAO,MAAM;QAAK;IAClD;IAEA,oDAAoD;IACpD,IAAI,WAAW,iBAAiB;QAC9B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAK,WAAU;8BAAO;;;;;;;;;;;;IAG7B;IAEA,qBACE,8OAAC,uJAAA,CAAA,wBAAqB;kBACpB,cAAA,8OAAC;YAAI,WAAU;;gBAGZ,uBACC,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;;sCACrC,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAKvB,8OAAC,4IAAA,CAAA,UAAc;oBACf,MAAM;oBACN,cAAc;oBACd,mBAAmB;oBACnB,WAAU;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,uIAAA,CAAA,UAAS;wBACR,MAAM;wBACN,SAAS;wBACT,kBAAkB;wBAClB,cAAc;wBACd,mBAAmB;wBACnB,gBAAgB;wBAChB,qBAAqB;;;;;;;;;;;gBAKxB,UAAU,MAAM,GAAG,mBAClB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;;gDAAK;gDAAa,UAAU,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAGxC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,uIAAA,CAAA,UAAS;oCACR,MAAM;oCACN,SAAS;oCACT,kBAAkB;oCAClB,gBAAgB;oCAChB,qBAAqB;;;;;;;;;;;;;;;;;;;;;;8BAU/B,8OAAC,kJAAA,CAAA,UAAuB;oBACtB,MAAM,qBAAqB,IAAI;oBAC/B,SAAS,IAAM,wBAAwB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBACjE,MAAM,qBAAqB,IAAI;oBAC/B,UAAU;oBACV,WAAW;oBACX,SAAS;;;;;;8BAGX,8OAAC,8JAAA,CAAA,sBAAmB;oBAClB,MAAM,qBAAqB,IAAI;oBAC/B,SAAS,IAAM,wBAAwB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBACjE,MAAM,qBAAqB,IAAI;oBAC/B,QAAQ;;;;;;8BAGV,8OAAC,yJAAA,CAAA,uBAAoB;oBACnB,MAAM,gBAAgB,IAAI;oBAC1B,SAAS,IAAM,mBAAmB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC5D,MAAM,gBAAgB,IAAI;oBAC1B,WAAW;oBACX,SAAS;;;;;;8BAGX,8OAAC,yJAAA,CAAA,mBAAgB;oBACf,MAAM,iBAAiB,IAAI;oBAC3B,SAAS,IAAM,oBAAoB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC7D,MAAM,iBAAiB,IAAI;oBAC3B,WAAW;oBACX,SAAS;;;;;;8BAGX,8OAAC,yJAAA,CAAA,qBAAkB;oBACjB,MAAM,mBAAmB,IAAI;oBAC7B,SAAS,IAAM,sBAAsB;4BAAE,MAAM;4BAAO,MAAM;wBAAK;oBAC/D,MAAM,mBAAmB,IAAI;oBAC7B,WAAW;oBACX,SAAS;;;;;;8BAGX,8OAAC,yJAAA,CAAA,0BAAuB;oBACtB,MAAM,wBAAwB,IAAI;oBAClC,SAAS,IAAM,2BAA2B;4BAAE,MAAM;4BAAO,MAAM;4BAAM,OAAO;wBAAG;oBAC/E,MAAM,wBAAwB,IAAI;oBAClC,OAAO,wBAAwB,KAAK;oBACpC,SAAS;wBACP,2BAA2B;4BAAE,MAAM;4BAAO,MAAM;4BAAM,OAAO;wBAAG;wBAChE,kDAAkD;wBAClD,IAAI,wBAAwB,IAAI,EAAE;4BAChC,sBAAsB;gCAAE,MAAM;gCAAM,MAAM,wBAAwB,IAAI;4BAAC;wBACzE;oBACF;;;;;;8BAGF,8OAAC,yJAAA,CAAA,eAAY;oBACX,SAAS,aAAa,OAAO;oBAC7B,SAAS,aAAa,OAAO;oBAC7B,SAAS,IAAM,gBAAgB;4BAAE,SAAS;4BAAO,SAAS;wBAAG;;;;;;8BAG/D,8OAAC,+IAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;wBAAM;oBAClD,iBAAiB;oBACjB,aAAa,kBAAkB,WAAW;oBAC1C,WAAW;oBACX,SAAS;;;;;;8BAGX,8OAAC,+IAAA,CAAA,UAAiB;oBAChB,MAAM,kBAAkB,IAAI;oBAC5B,SAAS,IAAM,qBAAqB;4BAAE,MAAM;wBAAM;oBAClD,MAAM,kBAAkB,IAAI,IAAI;oBAChC,WAAW;oBACX,SAAS;;;;;;8BAGX,8OAAC,8IAAA,CAAA,UAAgB;oBACf,MAAM;oBACN,SAAS,IAAM,oBAAoB;oBACnC,WAAW;oBACX,SAAS;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}