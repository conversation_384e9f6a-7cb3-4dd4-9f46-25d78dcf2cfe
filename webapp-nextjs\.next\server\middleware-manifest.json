{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "14367ff6e315b275600a32eeed6499d4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "32d9f8ad8ccdd62b925b559b2c21a7f9d30a0b00aaac991df37b321fe3dc18dc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "18a167caff60aa32da337a4518d435b9a4db02e6a7c14499eb4c2caf82537bc2"}}}, "sortedMiddleware": ["/"], "functions": {}}