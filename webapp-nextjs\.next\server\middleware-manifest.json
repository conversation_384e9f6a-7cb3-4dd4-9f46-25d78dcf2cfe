{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "cd921557eb8e15e9d742a4a2fb67deff", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "61997340975d54edac4c8d1834354cce1ff05a3fdf16f7d811188cd8534fe5fd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ad9ea7357586150d98a4fe18bb57ff40f39971d53587272f2d8a00d0ec46ec7e"}}}, "sortedMiddleware": ["/"], "functions": {}}