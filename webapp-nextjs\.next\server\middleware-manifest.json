{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "72cb0fd28828430c521bb0961a7c8253", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3864adeb73f8f1ec5c1ea3de5b3bb2d9b7d9f11d0b63951da1a7e09234aabe57", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "af0ab966cb5fc1f21ddcacc1294f7967018ee6380ade2f726763f867a75df540"}}}, "sortedMiddleware": ["/"], "functions": {}}