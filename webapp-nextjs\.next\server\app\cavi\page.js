(()=>{var e={};e.id=986,e.ids=[986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10698:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});let i=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11379:(e,a,t)=>{Promise.resolve().then(t.bind(t,89730))},12412:e=>{"use strict";e.exports=require("assert")},16023:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,t)=>{"use strict";t.d(a,{T:()=>l});var i=t(60687),s=t(43210),r=t(4780);let l=s.forwardRef(({className:e,...a},t)=>(0,i.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...a}));l.displayName="Textarea"},40510:()=>{},45583:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},52270:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var i=t(65239),s=t(48088),r=t(88170),l=t.n(r),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(a,o);let c={children:["",{children:["cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,10698)),"C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\cavi\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/cavi/page",pathname:"/cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,t)=>{"use strict";t.d(a,{S:()=>n});var i=t(60687);t(43210);var s=t(40211),r=t(13964),l=t(4780);function n({className:e,...a}){return(0,i.jsx)(s.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,i.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,i.jsx)(r.A,{className:"size-3.5"})})})}},58235:(e,a,t)=>{Promise.resolve().then(t.bind(t,10698))},61611:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>u,L3:()=>p,c7:()=>x,lG:()=>n,rr:()=>h,zM:()=>o});var i=t(60687);t(43210);var s=t(26134),r=t(11860),l=t(4780);function n({...e}){return(0,i.jsx)(s.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,i.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,i.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,i.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:t=!0,...n}){return(0,i.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,i.jsx)(d,{}),(0,i.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...n,children:[a,t&&(0,i.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(r.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function u({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function p({className:e,...a}){return(0,i.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...a})}function h({className:e,...a}){return(0,i.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...a})}},64021:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},86561:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},89730:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>ao});var i=t(60687),s=t(43210),r=t(16189),l=t(44493),n=t(91821),o=t(63213),c=t(76628),d=t(25653),m=t(62185),x=t(29523),u=t(96834),p=t(56896),h=t(15391),b=t(6211),g=t(89667),v=t(15079),f=t(70569),j=t(98599),N=t(11273),y=t(31355),C=t(1359),w=t(32547),_=t(96963),A=t(55509),z=t(25028),k=t(46059),S=t(14163),I=t(8730),O=t(65551),$=t(63376),E=t(42247),T="Popover",[M,F]=(0,N.A)(T,[A.Bk]),D=(0,A.Bk)(),[B,L]=M(T),R=e=>{let{__scopePopover:a,children:t,open:r,defaultOpen:l,onOpenChange:n,modal:o=!1}=e,c=D(a),d=s.useRef(null),[m,x]=s.useState(!1),[u,p]=(0,O.i)({prop:r,defaultProp:l??!1,onChange:n,caller:T});return(0,i.jsx)(A.bL,{...c,children:(0,i.jsx)(B,{scope:a,contentId:(0,_.B)(),triggerRef:d,open:u,onOpenChange:p,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),hasCustomAnchor:m,onCustomAnchorAdd:s.useCallback(()=>x(!0),[]),onCustomAnchorRemove:s.useCallback(()=>x(!1),[]),modal:o,children:t})})};R.displayName=T;var P="PopoverAnchor";s.forwardRef((e,a)=>{let{__scopePopover:t,...r}=e,l=L(P,t),n=D(t),{onCustomAnchorAdd:o,onCustomAnchorRemove:c}=l;return s.useEffect(()=>(o(),()=>c()),[o,c]),(0,i.jsx)(A.Mz,{...n,...r,ref:a})}).displayName=P;var V="PopoverTrigger",q=s.forwardRef((e,a)=>{let{__scopePopover:t,...s}=e,r=L(V,t),l=D(t),n=(0,j.s)(a,r.triggerRef),o=(0,i.jsx)(S.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":ea(r.open),...s,ref:n,onClick:(0,f.m)(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?o:(0,i.jsx)(A.Mz,{asChild:!0,...l,children:o})});q.displayName=V;var U="PopoverPortal",[G,J]=M(U,{forceMount:void 0}),Z=e=>{let{__scopePopover:a,forceMount:t,children:s,container:r}=e,l=L(U,a);return(0,i.jsx)(G,{scope:a,forceMount:t,children:(0,i.jsx)(k.C,{present:t||l.open,children:(0,i.jsx)(z.Z,{asChild:!0,container:r,children:s})})})};Z.displayName=U;var W="PopoverContent",H=s.forwardRef((e,a)=>{let t=J(W,e.__scopePopover),{forceMount:s=t.forceMount,...r}=e,l=L(W,e.__scopePopover);return(0,i.jsx)(k.C,{present:s||l.open,children:l.modal?(0,i.jsx)(Y,{...r,ref:a}):(0,i.jsx)(X,{...r,ref:a})})});H.displayName=W;var K=(0,I.TL)("PopoverContent.RemoveScroll"),Y=s.forwardRef((e,a)=>{let t=L(W,e.__scopePopover),r=s.useRef(null),l=(0,j.s)(a,r),n=s.useRef(!1);return s.useEffect(()=>{let e=r.current;if(e)return(0,$.Eq)(e)},[]),(0,i.jsx)(E.A,{as:K,allowPinchZoom:!0,children:(0,i.jsx)(Q,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,f.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.current||t.triggerRef.current?.focus()}),onPointerDownOutside:(0,f.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,t=0===a.button&&!0===a.ctrlKey;n.current=2===a.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,f.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),X=s.forwardRef((e,a)=>{let t=L(W,e.__scopePopover),r=s.useRef(!1),l=s.useRef(!1);return(0,i.jsx)(Q,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||t.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,"pointerdown"===a.detail.originalEvent.type&&(l.current=!0));let i=a.target;t.triggerRef.current?.contains(i)&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&l.current&&a.preventDefault()}})}),Q=s.forwardRef((e,a)=>{let{__scopePopover:t,trapFocus:s,onOpenAutoFocus:r,onCloseAutoFocus:l,disableOutsidePointerEvents:n,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:m,...x}=e,u=L(W,t),p=D(t);return(0,C.Oh)(),(0,i.jsx)(w.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:r,onUnmountAutoFocus:l,children:(0,i.jsx)(y.qW,{asChild:!0,disableOutsidePointerEvents:n,onInteractOutside:m,onEscapeKeyDown:o,onPointerDownOutside:c,onFocusOutside:d,onDismiss:()=>u.onOpenChange(!1),children:(0,i.jsx)(A.UC,{"data-state":ea(u.open),role:"dialog",id:u.contentId,...p,...x,ref:a,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),ee="PopoverClose";function ea(e){return e?"open":"closed"}s.forwardRef((e,a)=>{let{__scopePopover:t,...s}=e,r=L(ee,t);return(0,i.jsx)(S.sG.button,{type:"button",...s,ref:a,onClick:(0,f.m)(e.onClick,()=>r.onOpenChange(!1))})}).displayName=ee,s.forwardRef((e,a)=>{let{__scopePopover:t,...s}=e,r=D(t);return(0,i.jsx)(A.i3,{...r,...s,ref:a})}).displayName="PopoverArrow";var et=t(4780);let ei=s.forwardRef(({className:e,align:a="center",sideOffset:t=4,...s},r)=>(0,i.jsx)(Z,{children:(0,i.jsx)(H,{ref:r,align:a,sideOffset:t,className:(0,et.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));ei.displayName=H.displayName;var es=t(62688);let er=(0,es.A)("arrow-up-down",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]),el=(0,es.A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),en=(0,es.A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);var eo=t(11860);let ec=(0,es.A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),ed=(0,es.A)("chevrons-left",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]),em=(0,es.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),ex=(0,es.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),eu=(0,es.A)("chevrons-right",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);function ep({data:e=[],columns:a=[],loading:t=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:n,renderRow:o,className:c,pagination:d=!0,defaultRowsPerPage:m=25}){let[p,h]=(0,s.useState)({key:null,direction:null}),[g,f]=(0,s.useState)({}),[j,N]=(0,s.useState)({}),[y,C]=(0,s.useState)(0),[w,_]=(0,s.useState)(m),A=t=>{let i=a.find(e=>e.field===t);return i?.getFilterValue?[...new Set(e.map(e=>i.getFilterValue(e)).filter(Boolean))].sort():[...new Set(e.map(e=>e[t]).filter(Boolean))].sort()},z=(0,s.useMemo)(()=>{let t=[...e];return Object.entries(g).forEach(([e,i])=>{!i.value||Array.isArray(i.value)&&0===i.value.length||"string"==typeof i.value&&""===i.value.trim()||(t=t.filter(t=>{let s=a.find(a=>a.field===e),r=s?.getFilterValue?s.getFilterValue(t):t[e];if("select"===i.type)return(Array.isArray(i.value)?i.value:[i.value]).includes(r);if("text"===i.type){let e=i.value.toLowerCase(),a=String(r||"").toLowerCase();return"equals"===i.operator?a===e:a.includes(e)}if("number"===i.type){let e=parseFloat(r),a=parseFloat(i.value);if(isNaN(e)||isNaN(a))return!1;switch(i.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),p.key&&p.direction&&t.sort((e,a)=>{let t=e[p.key],i=a[p.key];if(null==t&&null==i)return 0;if(null==t)return"asc"===p.direction?-1:1;if(null==i)return"asc"===p.direction?1:-1;let s=parseFloat(t),r=parseFloat(i),l=!isNaN(s)&&!isNaN(r),n=0;return n=l?s-r:String(t).localeCompare(String(i)),"asc"===p.direction?n:-n}),t},[e,g,p]),k=(0,s.useMemo)(()=>{if(!d)return z;let e=y*w,a=e+w;return z.slice(e,a)},[z,y,w,d]),S=Math.ceil(z.length/w),I=y*w+1,O=Math.min((y+1)*w,z.length),$=e=>{let t=a.find(a=>a.field===e);t?.disableSort||h(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},E=(e,a)=>{f(t=>({...t,[e]:{...t[e],...a}}))},T=e=>{f(a=>{let t={...a};return delete t[e],t})},M=e=>p.key!==e?(0,i.jsx)(er,{className:"h-3 w-3"}):"asc"===p.direction?(0,i.jsx)(el,{className:"h-3 w-3"}):"desc"===p.direction?(0,i.jsx)(en,{className:"h-3 w-3"}):(0,i.jsx)(er,{className:"h-3 w-3"}),F=Object.keys(g).length>0;return t?(0,i.jsx)(l.Zp,{className:c,children:(0,i.jsx)(l.Wu,{className:"p-6",children:(0,i.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,i.jsxs)("div",{className:c,children:[F&&(0,i.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(g).map(([e,t])=>{let s=a.find(a=>a.field===e);if(!s)return null;let r=Array.isArray(t.value)?t.value.join(", "):String(t.value);return(0,i.jsxs)(u.E,{variant:"secondary",className:"gap-1",children:[s.headerName,": ",r,(0,i.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>T(e),children:(0,i.jsx)(eo.A,{className:"h-3 w-3"})})]},e)}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{f({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,i.jsx)(l.Zp,{children:(0,i.jsx)(l.Wu,{className:"p-0",children:(0,i.jsxs)(b.XI,{children:[(0,i.jsx)(b.A0,{children:(0,i.jsx)(b.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:a.map(a=>(0,i.jsx)(b.nd,{className:(0,et.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:{width:a.width,...a.headerStyle},children:a.renderHeader?a.renderHeader():(0,i.jsxs)("div",{className:"relative group",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,i.jsx)("span",{className:"truncate",children:a.headerName}),(0,i.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!a.disableSort&&(0,i.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>$(a.field),children:M(a.field)}),!a.disableFilter&&(0,i.jsxs)(R,{open:j[a.field],onOpenChange:e=>N(t=>({...t,[a.field]:e})),children:[(0,i.jsx)(q,{asChild:!0,children:(0,i.jsx)(x.$,{variant:"ghost",size:"sm",className:(0,et.cn)("h-4 w-4 p-0 hover:bg-mariner-100",g[a.field]&&"text-mariner-600 opacity-100"),children:(0,i.jsx)(ec,{className:"h-2.5 w-2.5"})})}),(0,i.jsx)(ei,{className:"w-64",align:"start",children:(0,i.jsx)(eh,{column:a,data:e,currentFilter:g[a.field],onFilterChange:e=>E(a.field,e),onClearFilter:()=>T(a.field),getUniqueValues:()=>A(a.field)})})]})]})]}),g[a.field]&&(0,i.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},a.field))})}),(0,i.jsx)(b.BF,{children:k.length>0?k.map((e,t)=>o?o(e,y*w+t):(0,i.jsx)(b.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:a.map(a=>(0,i.jsx)(b.nA,{className:(0,et.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},t)):(0,i.jsx)(b.Hj,{children:(0,i.jsx)(b.nA,{colSpan:a.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),d&&z.length>0&&(0,i.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,i.jsxs)(v.l6,{value:w.toString(),onValueChange:e=>{_(Number(e)),C(0)},children:[(0,i.jsx)(v.bq,{className:"w-20",children:(0,i.jsx)(v.yv,{})}),(0,i.jsxs)(v.gC,{children:[(0,i.jsx)(v.eb,{value:"10",children:"10"}),(0,i.jsx)(v.eb,{value:"25",children:"25"}),(0,i.jsx)(v.eb,{value:"50",children:"50"}),(0,i.jsx)(v.eb,{value:"100",children:"100"}),(0,i.jsx)(v.eb,{value:z.length.toString(),children:"Tutto"})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:z.length>0?`${I}-${O} di ${z.length}`:"0 di 0"}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(0),disabled:0===y,className:"h-8 w-8 p-0",children:(0,i.jsx)(ed,{className:"h-4 w-4"})}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.max(0,e-1)),disabled:0===y,className:"h-8 w-8 p-0",children:(0,i.jsx)(em,{className:"h-4 w-4"})}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(e=>Math.min(S-1,e+1)),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,i.jsx)(ex,{className:"h-4 w-4"})}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>C(S-1),disabled:y>=S-1,className:"h-8 w-8 p-0",children:(0,i.jsx)(eu,{className:"h-4 w-4"})})]})]})]})]})}function eh({column:e,currentFilter:a,onFilterChange:t,onClearFilter:r,getUniqueValues:l}){let[n,o]=(0,s.useState)(a?.value||""),[c,d]=(0,s.useState)(a?.operator||"contains"),m=l(),u="number"!==e.dataType&&m.length<=20,h="number"===e.dataType,b=()=>{u?t({type:"select",value:Array.isArray(n)?n:[n]}):h?t({type:"number",value:n,operator:c}):t({type:"text",value:n,operator:c})};return(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",e.headerName]}),u?(0,i.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:m.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(p.S,{id:`filter-${e}`,checked:Array.isArray(n)?n.includes(e):n===e,onCheckedChange:a=>{Array.isArray(n)?o(a?[...n,e]:n.filter(a=>a!==e)):o(a?[e]:[])}}),(0,i.jsx)("label",{htmlFor:`filter-${e}`,className:"text-sm",children:e})]},e))}):(0,i.jsxs)("div",{className:"space-y-2",children:[h&&(0,i.jsxs)(v.l6,{value:c,onValueChange:d,children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{})}),(0,i.jsxs)(v.gC,{children:[(0,i.jsx)(v.eb,{value:"equals",children:"Uguale a"}),(0,i.jsx)(v.eb,{value:"gt",children:"Maggiore di"}),(0,i.jsx)(v.eb,{value:"lt",children:"Minore di"}),(0,i.jsx)(v.eb,{value:"gte",children:"Maggiore o uguale"}),(0,i.jsx)(v.eb,{value:"lte",children:"Minore o uguale"})]})]}),!h&&(0,i.jsxs)(v.l6,{value:c,onValueChange:d,children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{})}),(0,i.jsxs)(v.gC,{children:[(0,i.jsx)(v.eb,{value:"contains",children:"Contiene"}),(0,i.jsx)(v.eb,{value:"equals",children:"Uguale a"})]})]}),(0,i.jsx)(g.p,{placeholder:`Cerca ${e.headerName.toLowerCase()}...`,value:n,onChange:e=>o(e.target.value),onKeyDown:e=>"Enter"===e.key&&b()})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(x.$,{size:"sm",onClick:b,children:"Applica"}),(0,i.jsx)(x.$,{size:"sm",variant:"outline",onClick:r,children:"Pulisci"})]})]})}var eb=t(99270);let eg=(0,es.A)("square-check",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),ev=(0,es.A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),ef=(0,es.A)("square-minus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);function ej({cavi:e=[],onFilteredDataChange:a,loading:t=!1,selectionEnabled:r=!1,onSelectionToggle:n,selectedCount:o=0,totalCount:c=0}){let[d,m]=(0,s.useState)(""),[u,p]=(0,s.useState)("contains"),h=e=>e?e.toString().toLowerCase().trim():"",b=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},f=(0,s.useCallback)((e,a,t)=>{let i=h(a);if(!i)return!0;let s=h(e.id_cavo),{prefix:r,number:l,suffix:n}=b(e.id_cavo||""),o=h(e.tipologia),c=h(e.formazione||e.sezione),d=h(e.utility),m=h(e.sistema),x=h(e.da||e.ubicazione_partenza),u=h(e.a||e.ubicazione_arrivo),p=h(e.utenza_partenza),g=h(e.utenza_arrivo),v=[s,r,l,n,o,c,d,m,x,u,p,g,h(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":h(e.id_bobina)],f=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],j=i.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(j){let e=j[1],a=parseFloat(j[2]);return f.some(t=>{if(null==t.value||isNaN(t.value))return!1;switch(e){case">":return t.value>a;case">=":return t.value>=a;case"<":return t.value<a;case"<=":return t.value<=a;case"=":return t.value===a;default:return!1}})}let N=parseFloat(i);return!!(!isNaN(N)&&f.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(t?v.some(e=>e===i):v.some(e=>e.includes(i)))},[]);(0,s.useCallback)(()=>{if(!d.trim())return void a?.(e);let t=d.split(",").map(e=>e.trim()).filter(e=>e.length>0),i=[];i="equals"===u?1===t.length?e.filter(e=>f(e,t[0],!0)):e.filter(e=>t.every(a=>f(e,a,!0))):e.filter(e=>t.some(a=>f(e,a,!1))),a?.(i)},[d,u,e,f]);let j=e=>{m(e)},N=()=>{m(""),p("contains")};return(0,i.jsx)(l.Zp,{className:"mb-1",children:(0,i.jsxs)(l.Wu,{className:"p-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsxs)("div",{className:"flex-1 relative",children:[(0,i.jsx)(eb.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,i.jsx)(g.p,{placeholder:"Cerca per ID, sistema, utility, tipologia, ubicazione...",value:d,onChange:e=>j(e.target.value),disabled:t,className:"pl-10 pr-10 h-8","aria-label":"Campo di ricerca intelligente per cavi"}),d&&(0,i.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:N,children:(0,i.jsx)(eo.A,{className:"h-2.5 w-2.5"})})]}),(0,i.jsx)("div",{className:"w-32",children:(0,i.jsxs)(v.l6,{value:u,onValueChange:e=>p(e),children:[(0,i.jsx)(v.bq,{className:"h-8",children:(0,i.jsx)(v.yv,{})}),(0,i.jsxs)(v.gC,{children:[(0,i.jsx)(v.eb,{value:"contains",children:"Contiene"}),(0,i.jsx)(v.eb,{value:"equals",children:"Uguale a"})]})]})}),d&&(0,i.jsxs)(x.$,{variant:"outline",size:"sm",onClick:N,disabled:t,className:"transition-all duration-200 hover:scale-105","aria-label":"Pulisci ricerca",children:[(0,i.jsx)(eo.A,{className:"h-4 w-4 mr-1"}),"Pulisci"]}),n&&c>0&&(0,i.jsxs)(x.$,{variant:r?"default":"outline",size:"sm",onClick:n,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105","aria-label":r?"Disabilita modalit\xe0 selezione":"Abilita modalit\xe0 selezione",children:[r?(0,i.jsx)(eg,{className:"h-4 w-4"}):(0,i.jsx)(ev,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]}),r&&o>0&&(0,i.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{},className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50","aria-label":`Deseleziona tutti i ${o} cavi selezionati`,children:[(0,i.jsx)(ef,{className:"h-4 w-4"}),"Deseleziona Tutto (",o,")"]})]}),d&&(0,i.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDCA1"}),(0,i.jsx)("span",{children:"• Virgole per multipli"}),(0,i.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function eN({text:e,maxLength:a=20,className:t=""}){let[r,l]=(0,s.useState)(!1),[n,o]=(0,s.useState)({x:0,y:0});if(!e)return(0,i.jsx)("span",{className:"text-gray-400",children:"-"});let c=e.length>a,d=c?`${e.substring(0,a)}...`:e;return c?(0,i.jsxs)("div",{className:"relative inline-block",children:[(0,i.jsx)("span",{className:`cursor-help ${t}`,style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{o({x:e.clientX,y:e.clientY}),l(!0)},onMouseMove:e=>{o({x:e.clientX,y:e.clientY})},onMouseLeave:()=>l(!1),title:e,children:d}),r&&(0,i.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:n.y-40,left:n.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[e,(0,i.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,i.jsx)("span",{className:t,children:e})}var ey=t(78272),eC=t(5336),ew=t(48730),e_=t(93613);let eA=(0,es.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var ez=t(96882);let ek=(0,es.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),eS=(0,es.A)("unlink",[["path",{d:"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71",key:"yqzxt4"}],["path",{d:"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71",key:"4qinb0"}],["line",{x1:"8",x2:"8",y1:"2",y2:"5",key:"1041cp"}],["line",{x1:"2",x2:"5",y1:"8",y2:"8",key:"14m1p5"}],["line",{x1:"16",x2:"16",y1:"19",y2:"22",key:"rzdirn"}],["line",{x1:"19",x2:"22",y1:"16",y2:"16",key:"ox905f"}]]);var eI=t(84027),eO=t(86561);function e$({cavi:e=[],loading:a=!1,selectionEnabled:t=!1,selectedCavi:r=[],onSelectionChange:l,onStatusAction:n,onContextMenuAction:o}){let[c,d]=(0,s.useState)(e),[m,g]=(0,s.useState)(e),[v,f]=(0,s.useState)(t),j=e=>{l&&l(e?m.map(e=>e.id_cavo):[])},N=(e,a)=>{l&&l(a?[...r,e]:r.filter(a=>a!==e))},y=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})});if(e.ok){let a=await e.blob(),t=window.URL.createObjectURL(a),i=document.createElement("a");i.href=t,i.download=`cavi_export_${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(t),document.body.removeChild(i)}else{let a=await e.json();alert(`Errore durante l'esportazione: ${a.error}`)}}catch(e){alert("Errore durante l'esportazione")}},C=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1,newStatus:e})}),t=await a.json();t.success?alert(t.message):alert(`Errore: ${t.error}`)}catch(e){alert("Errore durante il cambio stato")}},w=()=>{alert(`Assegnazione comanda per ${r.length} cavi`)},_=async()=>{if(confirm(`Sei sicuro di voler eliminare ${r.length} cavi?`))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:r,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert(`Errore: ${a.error}`)}catch(e){alert("Errore durante l'eliminazione")}},A=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,i.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,i.jsx)(eN,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,i.jsx)(eN,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,i.jsx)(eN,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,i.jsx)(eN,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,i.jsx)(eN,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>I(e)},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableSort:!0,getFilterValue:e=>z(e),renderCell:e=>O(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableSort:!0,getFilterValue:e=>k(e),renderCell:e=>$(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableSort:!0,getFilterValue:e=>S(e),renderCell:e=>E(e)}];return v&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,i.jsx)(p.S,{checked:r.length===m.length&&m.length>0,onCheckedChange:j}),renderCell:e=>(0,i.jsx)(p.S,{checked:r.includes(e.id_cavo),onCheckedChange:a=>N(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[v,r,m,j,N]),z=e=>{let a=e.metri_posati||e.metratura_reale||0,t=e.stato_installazione||"Da installare",i=e.comanda_posa,s=e.comanda_partenza,r=e.comanda_arrivo,l=e.comanda_certificazione,n=i||s||r||l;return n&&"In corso"===t?`In corso (${n})`:"Installato"===t||a>0?"Installato":t},k=e=>{let a=e.metri_posati>0||e.metratura_reale>0,t=e.collegamento||e.collegamenti||0;if(!a)return"Non disponibile";switch(t){case 0:return"Collega";case 1:return"Completa Arrivo";case 2:return"Completa Partenza";case 3:return"Scollega";default:return"Gestisci"}},S=e=>{let a=e.metri_posati>0||e.metratura_reale>0,t=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?t?"Genera PDF":"Certifica":"Non disponibile"},I=e=>{let a=e.id_bobina,t=(0,h.jc)();if(!a||"N/A"===a)return(0,i.jsx)("span",{className:`inline-flex items-center px-2 py-1 text-xs font-medium ${h.mU.NEUTRAL.text_light}`,children:"-"});if("BOBINA_VUOTA"===a||"VUOTA"===a)return(0,i.jsxs)("button",{className:t.button,onClick:a=>{a.stopPropagation(),n?.(e,"modify_reel")},title:"Bobina Vuota - Clicca per modificare",children:[(0,i.jsx)("span",{children:"Vuota"}),(0,i.jsx)(ey.A,{className:"w-3 h-3 opacity-70"})]});let s=a,r=a.match(/_B(.+)$/);return r||(r=a.match(/_b(.+)$/))||(r=a.match(/c\d+_[bB](\d+)$/))?s=r[1]:(r=a.match(/(\d+)$/))&&(s=r[1]),(0,i.jsxs)("button",{className:t.button,onClick:a=>{a.stopPropagation(),n?.(e,"modify_reel")},title:`Bobina ${s} - Clicca per modificare`,children:[(0,i.jsx)("span",{children:s}),(0,i.jsx)(ey.A,{className:"w-3 h-3 opacity-70"})]})},O=e=>{let a=(e.metri_posati||e.metratura_reale||0)>0,t=e.stato_installazione||"Da installare",s=e.comanda_posa,r=e.comanda_partenza,l=e.comanda_arrivo,o=e.comanda_certificazione,c=s||r||l||o,d=t,m=(0,h.Tr)(t);c&&"In corso"===t&&(d=c,m=(0,h.Tr)("IN_CORSO")),a&&"Installato"!==t&&(d="Installato",m=(0,h.Tr)("INSTALLATO"));let x=e=>{switch(e.toLowerCase()){case"installato":return(0,i.jsx)(eC.A,{className:"w-3 h-3"});case"in corso":return(0,i.jsx)(ew.A,{className:"w-3 h-3"});case"da installare":return(0,i.jsx)(e_.A,{className:"w-3 h-3"});default:return c?(0,i.jsx)(eA,{className:"w-3 h-3"}):(0,i.jsx)(e_.A,{className:"w-3 h-3"})}},u=(0,h.jc)();return"da installare"!==t.toLowerCase()||a?(0,i.jsxs)("span",{className:`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${m.text} ${m.bg} ${m.border}`,title:c?`Comanda attiva: ${c}`:`Stato: ${d}`,children:[x(d),(0,i.jsx)("span",{children:d})]}):(0,i.jsxs)("button",{className:u.button,onClick:a=>{a.stopPropagation(),n?.(e,"insert_meters")},title:"Clicca per inserire metri posati",children:[x(d),(0,i.jsx)("span",{children:d})]})},$=e=>{let a=e.metri_posati>0||e.metratura_reale>0,t=e.collegamento||e.collegamenti||0;(0,h.Nj)();let s=(0,h.NM)();if(!a)return(0,i.jsxs)("span",{className:s.text,title:"Collegamento disponibile solo per cavi installati",children:[(0,i.jsx)(ez.A,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:"Non disponibile"})]});let r=(a,t,s)=>{let r=(0,h.jc)();return(0,i.jsxs)("button",{className:r.button,onClick:a=>{a.stopPropagation(),n?.(e,t)},title:`Clicca per ${a.toLowerCase()}`,children:[s&&s,(0,i.jsx)("span",{children:a})]})};switch(t){case 0:return r("Collega","connect_cable",(0,i.jsx)(ek,{className:"w-3 h-3"}));case 1:return r("Completa Arrivo","connect_arrival",(0,i.jsx)(ek,{className:"w-3 h-3"}));case 2:return r("Completa Partenza","connect_departure",(0,i.jsx)(ek,{className:"w-3 h-3"}));case 3:return r("Scollega","disconnect_cable",(0,i.jsx)(eS,{className:"w-3 h-3"}));default:return r("Gestisci","manage_connections",(0,i.jsx)(eI.A,{className:"w-3 h-3"}))}},E=e=>{let a=e.metri_posati>0||e.metratura_reale>0,t=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato,s=(0,h.jc)(),r=(0,h.NM)();return a?t?(0,i.jsxs)("button",{className:s.button,onClick:a=>{a.stopPropagation(),n?.(e,"generate_pdf")},title:"Certificato - Clicca per generare PDF",children:[(0,i.jsx)(eC.A,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:"PDF"})]}):(0,i.jsxs)("button",{className:s.button,onClick:a=>{a.stopPropagation(),n?.(e,"create_certificate")},title:"Clicca per certificare il cavo",children:[(0,i.jsx)(eO.A,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:"Certifica"})]}):(0,i.jsxs)("span",{className:r.text,title:"Certificazione disponibile solo per cavi installati",children:[(0,i.jsx)(ez.A,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:"Non disponibile"})]})};return(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(ej,{cavi:e,onFilteredDataChange:e=>{d(e)},loading:a,selectionEnabled:v,onSelectionToggle:()=>{f(!v)}}),(0,i.jsx)(ep,{data:c,columns:A,loading:a,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{g(e)},renderRow:(e,a)=>{let t=r.includes(e.id_cavo);return(0,i.jsx)(b.Hj,{className:`
          ${t?"bg-blue-50 border-blue-200":"bg-white"}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${t?"ring-1 ring-blue-300":""}
        `,onClick:()=>v&&N(e.id_cavo,!t),onContextMenu:a=>{a.preventDefault(),o?.(e,"context_menu")},children:A.map(a=>(0,i.jsx)(b.nA,{className:`
              py-2 px-2 text-sm text-left
              ${t?"text-blue-900":"text-gray-900"}
              transition-colors duration-200
            `,style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,i.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),v&&r.length>0&&(0,i.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,i.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsxs)(u.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[r.length," cavi selezionati"]}),(0,i.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>j(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>y(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDCCA"}),(0,i.jsx)("span",{children:"Esporta"})]}),(0,i.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>C(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDD04"}),(0,i.jsx)("span",{children:"Cambia Stato"})]}),(0,i.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDCCB"}),(0,i.jsx)("span",{children:"Assegna Comanda"})]}),(0,i.jsxs)(x.$,{variant:"destructive",size:"sm",onClick:()=>_(),className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,i.jsx)("span",{children:"Elimina"})]})]})]})})]})}var eE=t(53411),eT=t(23361),eM=t(43649),eF=t(45583),eD=t(19080),eB=t(51215);let eL=({content:e,children:a,position:t="auto",delay:r=500,className:l="",disabled:n=!1,maxWidth:o=250})=>{let[c,d]=(0,s.useState)(!1),[m,x]=(0,s.useState)(null),u=(0,s.useRef)(null),p=(0,s.useRef)(null),h=(0,s.useRef)(null),b=()=>{if(!u.current)return null;let e=u.current.getBoundingClientRect(),a=window.innerWidth,i=window.innerHeight,s=t,r=0,l=0;if("auto"===t){let t=e.top,r=i-e.bottom;e.left;let l=a-e.right;s=t>40&&t>r?"top":r>40?"bottom":l>o?"right":"left"}switch(s){case"top":r=e.top-40-8,l=e.left+e.width/2-o/2;break;case"bottom":r=e.bottom+8,l=e.left+e.width/2-o/2;break;case"left":r=e.top+e.height/2-20,l=e.left-o-8;break;case"right":r=e.top+e.height/2-20,l=e.right+8}return l=Math.max(8,Math.min(l,a-o-8)),{top:r=Math.max(8,Math.min(r,i-40-8)),left:l,position:s}},g=()=>{n||(h.current&&clearTimeout(h.current),h.current=setTimeout(()=>{let e=b();e&&(x(e),d(!0))},r))},v=()=>{h.current&&(clearTimeout(h.current),h.current=null),d(!1),x(null)};(0,s.useEffect)(()=>()=>{h.current&&clearTimeout(h.current)},[]);let f=c&&m?(0,i.jsxs)("div",{ref:p,className:`fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ${l}`,style:{top:m.top,left:m.left,maxWidth:o,wordWrap:"break-word",whiteSpace:"normal"},role:"tooltip","aria-hidden":!c,children:[e,(0,i.jsx)("div",{className:(e=>{let a="absolute w-0 h-0 border-solid";switch(e){case"top":return`${a} top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900`;case"bottom":return`${a} bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900`;case"left":return`${a} left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900`;case"right":return`${a} right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900`;default:return a}})(m.position)})]}):null;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{ref:u,onMouseEnter:g,onMouseLeave:v,onFocus:g,onBlur:v,className:"inline-block","aria-describedby":c?"tooltip":void 0,children:a}),"undefined"!=typeof document&&f&&(0,eB.createPortal)(f,document.body)]})},eR=({type:e,count:a,percentage:t,children:s})=>(0,i.jsx)(eL,{content:(()=>{let i=`${a} cavi`,s=void 0!==t?` (${t.toFixed(1)}%)`:"";switch(e){case"total":return`Totale cavi nel progetto: ${i}`;case"installed":return`Cavi fisicamente installati: ${i}${s}`;case"in_progress":return`Cavi in corso di installazione: ${i}${s}`;case"to_install":return`Cavi ancora da installare: ${i}${s}`;case"connected":return`Cavi completamente collegati: ${i}${s}`;case"certified":return`Cavi certificati e collaudati: ${i}${s}`;default:return i}})(),delay:200,position:"bottom",children:s});function eP({cavi:e,filteredCavi:a,className:t,revisioneCorrente:r}){let n=(0,s.useMemo)(()=>{let t=e.length,i=a.length,s=a.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,r=a.filter(e=>"In corso"===e.stato_installazione).length,l=a.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,n=a.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=a.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=a.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=a.reduce((e,a)=>e+(a.metri_teorici||0),0),m=a.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===i?0:Math.round(100*(((s-l)*2+(l-c)*3.5+4*c)/(4*i)*100))/100;return{totalCavi:t,filteredCount:i,installati:s,inCorso:r,daInstallare:i-s-r,collegati:l,parzialmenteCollegati:n,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[e,a]);return(0,i.jsx)(l.Zp,{className:t,children:(0,i.jsxs)(l.Wu,{className:"p-1.5",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,i.jsx)(eE.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),(0,i.jsx)("div",{className:"flex items-center space-x-1",children:r&&(0,i.jsxs)(u.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,i.jsx)(eR,{type:"total",count:n.totalCavi,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)(eT.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:n.filteredCount}),(0,i.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",n.totalCavi," cavi"]})]})]})}),(0,i.jsx)(eR,{type:"installed",count:n.installati,percentage:n.installati/n.filteredCount*100,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi installati: ${n.installati} cavi`,children:[(0,i.jsx)(eC.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-green-700 text-sm",children:n.installati}),(0,i.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]})}),(0,i.jsx)(eR,{type:"in_progress",count:n.inCorso,percentage:n.inCorso/n.filteredCount*100,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi in corso: ${n.inCorso} cavi`,children:[(0,i.jsx)(ew.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:n.inCorso}),(0,i.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]})}),(0,i.jsx)(eR,{type:"to_install",count:n.daInstallare,percentage:n.daInstallare/n.filteredCount*100,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi da installare: ${n.daInstallare} cavi`,children:[(0,i.jsx)(eM.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:n.daInstallare}),(0,i.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]})}),(0,i.jsx)(eR,{type:"connected",count:n.collegati,percentage:n.collegati/n.filteredCount*100,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi collegati: ${n.collegati} cavi`,children:[(0,i.jsx)(eF.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:n.collegati}),(0,i.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]})}),(0,i.jsx)(eR,{type:"certified",count:n.certificati,percentage:n.certificati/n.filteredCount*100,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg","aria-label":`Cavi certificati: ${n.certificati} cavi`,children:[(0,i.jsx)(eD.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:n.certificati}),(0,i.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]})}),(0,i.jsx)(eR,{type:"total",count:n.metriInstallati,children:(0,i.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,i.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,i.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[n.metriInstallati.toLocaleString(),"m"]}),(0,i.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",n.metriTotali.toLocaleString(),"m"]})]})]})})]}),n.filteredCount>0&&(0,i.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,i.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,i.jsxs)("span",{className:`font-bold ${n.percentualeInstallazione>=80?"text-emerald-700":n.percentualeInstallazione>=50?"text-yellow-700":n.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"}`,children:[n.percentualeInstallazione.toFixed(1),"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${n.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":n.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":n.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"}`,style:{width:`${Math.min(n.percentualeInstallazione,100)}%`}})}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,i.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,i.jsxs)("span",{children:[n.installati,"I + ",n.collegati,"C + ",n.certificati,"Cert"]})]})]})]})})}var eV=t(63503),eq=t(80013);let eU=(0,es.A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var eG=t(41862),eJ=t(10022),eZ=t(31158),eW=t(64021);let eH=({icon:e,title:a,cableId:t,description:s})=>(0,i.jsxs)(eV.c7,{children:[(0,i.jsxs)(eV.L3,{className:"flex items-center gap-2",children:[e,(0,i.jsxs)("span",{className:"flex items-center gap-2",children:[a,(0,i.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold",children:t})]})]}),s&&(0,i.jsx)(eV.rr,{className:"text-sm text-muted-foreground",children:s})]}),eK=({children:e,className:a="sm:max-w-md",onKeyDown:t,ariaLabelledBy:s,ariaDescribedBy:r})=>(0,i.jsx)(eV.Cf,{className:a,onKeyDown:t,"aria-labelledby":s,"aria-describedby":r,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{t&&t(e)},children:e}),eY=({open:e,onClose:a,cavo:t,onConfirm:r})=>{let[l,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)(!1),m=async()=>{if(t){o(!0);try{await r(t.id_cavo),a(),d(!1)}catch(e){console.error("Error disconnecting cable:",e)}finally{o(!1)}}},u=()=>{d(!1),a()};return t?(0,i.jsx)(eV.lG,{open:e,onOpenChange:u,children:(0,i.jsxs)(eK,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&u()},ariaLabelledBy:"disconnect-modal-title",ariaDescribedBy:"disconnect-modal-description",children:[(0,i.jsx)(eH,{icon:(0,i.jsx)(eF.A,{className:"h-5 w-5 text-orange-500"}),title:"Gestione Collegamenti",cableId:t.id_cavo,description:"Gestisci le connessioni del cavo selezionato"}),c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"py-4 text-center",children:[(0,i.jsx)("div",{className:"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,i.jsx)(eM.A,{className:"h-6 w-6 text-red-600"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Conferma Scollegamento"}),(0,i.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Sei veramente sicuro di voler scollegare completamente il cavo ",(0,i.jsx)("strong",{children:t.id_cavo}),"?"]}),(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,i.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ Questa azione \xe8 irreversibile"})})]}),(0,i.jsxs)(eV.Es,{className:"gap-2",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:()=>d(!1),disabled:l,className:"flex-1",children:"No, Annulla"}),(0,i.jsx)(x.$,{variant:"destructive",onClick:m,disabled:l,className:"flex-1",children:l?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eG.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"py-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,i.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]}),(0,i.jsxs)("span",{className:"text-sm font-medium text-green-700",children:["Completamente collegato",(0,i.jsx)(eU,{className:"inline h-4 w-4 ml-1 cursor-help",title:"Cavo collegato sia all'origine che alla destinazione"})]})]}),(0,i.jsx)("div",{className:"space-y-3",children:(0,i.jsxs)("div",{children:[(0,i.jsx)(eq.J,{htmlFor:"responsabile-collegamento",className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,i.jsxs)("select",{id:"responsabile-collegamento",className:"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",defaultValue:"",children:[(0,i.jsx)("option",{value:"",disabled:!0,children:"Seleziona responsabile..."}),(0,i.jsx)("option",{value:"cantiere",children:"Cantiere"}),(0,i.jsx)("option",{value:"tecnico1",children:"Tecnico 1"}),(0,i.jsx)("option",{value:"tecnico2",children:"Tecnico 2"})]})]})})]}),(0,i.jsxs)(n.Fc,{className:"my-4 bg-orange-50 border-orange-200",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4 text-orange-600"}),(0,i.jsxs)(n.TN,{className:"text-orange-800",children:[(0,i.jsx)("strong",{children:"Attenzione:"})," Lo scollegamento rimuover\xe0 tutte le connessioni attive del cavo. Questa azione potrebbe influenzare altri componenti collegati."]})]}),(0,i.jsxs)(eV.Es,{className:"gap-2",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:u,disabled:l,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,i.jsxs)(x.$,{variant:"destructive",onClick:()=>{d(!0)},disabled:l,className:"flex-1 hover:bg-red-600",children:[(0,i.jsx)(eM.A,{className:"mr-2 h-4 w-4"}),"Scollega Completamente"]})]})]})]})}):null},eX=({open:e,onClose:a,cavo:t,onGenerate:r})=>{let[l,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)({fileName:"",includeTestData:!0,format:"standard",emailRecipient:""}),[d,m]=(0,s.useState)({});(0,s.useEffect)(()=>{t&&e&&(c(e=>({...e,fileName:`Certificato_${t.id_cavo}_${new Date().toISOString().split("T")[0]}.pdf`})),m({}))},[t,e]);let u=()=>{let e={};return o.fileName.trim()?/^[a-zA-Z0-9_\-\s]+\.pdf$/i.test(o.fileName)||(e.fileName="Il nome del file deve terminare con .pdf e contenere solo caratteri validi"):e.fileName="Il nome del file \xe8 obbligatorio",o.emailRecipient&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.emailRecipient)&&(e.emailRecipient="Inserisci un indirizzo email valido"),m(e),0===Object.keys(e).length},h=async()=>{if(t&&u()){n(!0);try{await r(t.id_cavo,o),a()}catch(e){console.error("Error generating PDF:",e)}finally{n(!1)}}},b=o.fileName.trim()&&0===Object.keys(d).length;return t?(0,i.jsx)(eV.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(eK,{className:"sm:max-w-lg",onKeyDown:e=>{"Escape"===e.key&&a()},ariaLabelledBy:"pdf-modal-title",children:[(0,i.jsx)(eH,{icon:(0,i.jsx)(eJ.A,{className:"h-5 w-5 text-blue-500"}),title:"Genera Certificato",cableId:t.id_cavo,description:"Configura le opzioni per la generazione del certificato PDF"}),(0,i.jsxs)("div",{className:"space-y-4 py-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"fileName",className:"text-sm font-medium",children:"Nome File *"}),(0,i.jsx)(g.p,{id:"fileName",value:o.fileName,onChange:e=>{c(a=>({...a,fileName:e.target.value})),d.fileName&&m(e=>({...e,fileName:""}))},onBlur:u,placeholder:"Certificato_C001_2025-06-29.pdf",className:d.fileName?"border-red-500 focus:ring-red-500":""}),d.fileName&&(0,i.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,i.jsx)(e_.A,{className:"h-3 w-3"}),d.fileName]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Formato Certificato"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,i.jsx)("input",{type:"radio",name:"format",value:"standard",checked:"standard"===o.format,onChange:e=>c(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Standard"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con informazioni essenziali"})]})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,i.jsx)("input",{type:"radio",name:"format",value:"detailed",checked:"detailed"===o.format,onChange:e=>c(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Dettagliato"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con tutti i dati tecnici"})]})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3 p-2 border rounded-md",children:[(0,i.jsx)(p.S,{id:"includeTestData",checked:o.includeTestData,onCheckedChange:e=>c(a=>({...a,includeTestData:e}))}),(0,i.jsxs)("div",{children:[(0,i.jsx)(eq.J,{htmlFor:"includeTestData",className:"text-sm font-medium cursor-pointer",children:"Includi Dati di Collaudo"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Aggiunge i risultati dei test al certificato"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"emailRecipient",className:"text-sm font-medium",children:"Email Destinatario (Opzionale)"}),(0,i.jsx)(g.p,{id:"emailRecipient",type:"email",value:o.emailRecipient,onChange:e=>{c(a=>({...a,emailRecipient:e.target.value})),d.emailRecipient&&m(e=>({...e,emailRecipient:""}))},onBlur:u,placeholder:"<EMAIL>",className:d.emailRecipient?"border-red-500 focus:ring-red-500":""}),d.emailRecipient&&(0,i.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,i.jsx)(e_.A,{className:"h-3 w-3"}),d.emailRecipient]})]})]}),(0,i.jsxs)(eV.Es,{className:"gap-2",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:a,disabled:l,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,i.jsx)(x.$,{onClick:h,disabled:l||!b,className:`flex-1 ${!b?"opacity-50 cursor-not-allowed":"hover:bg-blue-600"}`,children:l?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eG.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generando..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eZ.A,{className:"mr-2 h-4 w-4"}),"Genera PDF"]})})]})]})}):null},eQ=({open:e,onClose:a,cavo:t,errorMessage:s,missingRequirements:r=[]})=>{let l=r.length>0?r:['Il cavo deve essere nello stato "Installato"',"Il cavo deve essere completamente collegato","Tutti i dati di collaudo devono essere presenti"];return t?(0,i.jsx)(eV.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(eK,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&a()},ariaLabelledBy:"certification-error-title",children:[(0,i.jsx)(eH,{icon:(0,i.jsx)(e_.A,{className:"h-5 w-5 text-red-500"}),title:"Impossibile Certificare Cavo",cableId:t.id_cavo,description:"Il cavo non pu\xf2 essere certificato nel suo stato attuale"}),(0,i.jsxs)("div",{className:"py-4",children:[s&&(0,i.jsxs)(n.Fc,{className:"mb-4 bg-red-50 border-red-200",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(n.TN,{className:"text-red-800",children:s})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4 text-amber-500"}),"Requisiti mancanti:"]}),(0,i.jsx)("ul",{className:"space-y-3",children:l.map((e,a)=>(0,i.jsxs)("li",{className:"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md",children:[(0,i.jsx)("div",{className:"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5",children:(0,i.jsx)(eo.A,{className:"h-3 w-3 text-red-600"})}),(0,i.jsx)("span",{className:"text-sm text-red-800",children:e})]},a))})]}),(0,i.jsxs)(n.Fc,{className:"bg-blue-50 border-blue-200",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4 text-blue-600"}),(0,i.jsxs)(n.TN,{className:"text-blue-800",children:[(0,i.jsx)("strong",{children:"Prossimi passi:"})," Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo."]})]})]})]}),(0,i.jsx)(eV.Es,{children:(0,i.jsxs)(x.$,{onClick:a,className:"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500",children:[(0,i.jsx)(eC.A,{className:"mr-2 h-4 w-4"}),"Ho Capito"]})})]})}):null},e0=({open:e,onClose:a,cavo:t,onCertify:r})=>{let[l,n]=(0,s.useState)(!1),[o,c]=(0,s.useState)({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),[d,m]=(0,s.useState)({});(0,s.useEffect)(()=>{e&&t&&(c({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),m({}))},[e,t]);let u=()=>{let e={};if(o.responsabile.trim()||(e.responsabile="Il responsabile \xe8 obbligatorio"),o.dataCertificazione){let a=new Date(o.dataCertificazione),t=new Date;t.setHours(0,0,0,0),a>t&&(e.dataCertificazione="La data non pu\xf2 essere futura")}else e.dataCertificazione="La data di certificazione \xe8 obbligatoria";return m(e),0===Object.keys(e).length},p=async()=>{if(t&&u()){n(!0);try{await r(t.id_cavo,o),a()}catch(e){console.error("Error certifying cable:",e)}finally{n(!1)}}},h=o.responsabile.trim()&&o.dataCertificazione&&0===Object.keys(d).length;if(!t)return null;let b={installato:!0,collegato:!0,certificato:!1};return(0,i.jsx)(eV.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(eK,{className:"sm:max-w-lg",onKeyDown:e=>{"Escape"===e.key&&a()},ariaLabelledBy:"certification-modal-title",children:[(0,i.jsx)(eH,{icon:(0,i.jsx)(eW.A,{className:"h-5 w-5 text-blue-500"}),title:"Gestione Certificazione",cableId:t.id_cavo,description:"Certifica il cavo dopo aver completato tutti i controlli"}),(0,i.jsxs)("div",{className:"space-y-4 py-4",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Stato Cavo:"}),(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-md",children:[(0,i.jsx)("div",{className:`w-3 h-3 rounded-full ${b.installato?"bg-green-500":"bg-red-500"}`}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[b.installato?"✓":"✗"," Installato"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-md",children:[(0,i.jsx)("div",{className:`w-3 h-3 rounded-full ${b.collegato?"bg-green-500":"bg-red-500"}`}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[b.collegato?"✓":"✗"," Collegato"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-md",children:[(0,i.jsx)("div",{className:`w-3 h-3 rounded-full ${b.certificato?"bg-green-500":"bg-orange-500"}`}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[b.certificato?"✓":"⚠"," Non certificato"]})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"responsabile",className:"text-sm font-medium",children:"Responsabile Certificazione *"}),(0,i.jsxs)("select",{id:"responsabile",value:o.responsabile,onChange:e=>{c(a=>({...a,responsabile:e.target.value})),d.responsabile&&m(e=>({...e,responsabile:""}))},onBlur:u,className:`w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${d.responsabile?"border-red-500":"border-gray-300"}`,children:[(0,i.jsx)("option",{value:"",disabled:!0,children:"Seleziona responsabile..."}),(0,i.jsx)("option",{value:"cantiere",children:"Cantiere"}),(0,i.jsx)("option",{value:"tecnico_1",children:"Tecnico 1"}),(0,i.jsx)("option",{value:"tecnico_2",children:"Tecnico 2"}),(0,i.jsx)("option",{value:"supervisore",children:"Supervisore"})]}),d.responsabile&&(0,i.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,i.jsx)(e_.A,{className:"h-3 w-3"}),d.responsabile]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"dataCertificazione",className:"text-sm font-medium",children:"Data Certificazione *"}),(0,i.jsx)(g.p,{id:"dataCertificazione",type:"date",value:o.dataCertificazione,onChange:e=>{c(a=>({...a,dataCertificazione:e.target.value})),d.dataCertificazione&&m(e=>({...e,dataCertificazione:""}))},onBlur:u,max:new Date().toISOString().split("T")[0],className:d.dataCertificazione?"border-red-500 focus:ring-red-500":""}),d.dataCertificazione&&(0,i.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,i.jsx)(e_.A,{className:"h-3 w-3"}),d.dataCertificazione]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Esito Certificazione"}),(0,i.jsxs)("select",{value:o.esitoCertificazione,onChange:e=>c(a=>({...a,esitoCertificazione:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,i.jsx)("option",{value:"CONFORME",children:"CONFORME"}),(0,i.jsx)("option",{value:"NON_CONFORME",children:"NON CONFORME"}),(0,i.jsx)("option",{value:"PARZIALMENTE_CONFORME",children:"PARZIALMENTE CONFORME"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"note",className:"text-sm font-medium",children:"Note (opzionale)"}),(0,i.jsx)("textarea",{id:"note",value:o.note,onChange:e=>c(a=>({...a,note:e.target.value})),placeholder:"Inserisci eventuali note sulla certificazione...",rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"})]})]}),(0,i.jsxs)(eV.Es,{className:"gap-2",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:a,disabled:l,className:"flex-1 hover:bg-gray-50",children:"Chiudi"}),(0,i.jsx)(x.$,{onClick:p,disabled:l||!h,className:`flex-1 ${!h?"opacity-50 cursor-not-allowed bg-gray-400":"bg-blue-600 hover:bg-blue-700"}`,children:l?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eG.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Certificando..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eW.A,{className:"mr-2 h-4 w-4"}),"Certifica Cavo"]})})]})]})})},e1=({message:e,visible:a,onClose:t})=>((0,s.useEffect)(()=>{if(a){let e=setTimeout(()=>{t()},3e3);return()=>clearTimeout(e)}},[a,t]),a)?(0,i.jsx)("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2",children:(0,i.jsxs)(n.Fc,{className:"bg-green-50 border-green-200 text-green-800 shadow-lg",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{className:"font-medium",children:e}),(0,i.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100",onClick:t,children:(0,i.jsx)(eo.A,{className:"h-3 w-3"})})]})}):null,e2=({icon:e,title:a,cableId:t,description:s})=>(0,i.jsxs)(eV.c7,{children:[(0,i.jsxs)(eV.L3,{className:"flex items-center gap-2",children:[e,(0,i.jsxs)("span",{className:"flex items-center gap-2",children:[a,(0,i.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold",children:t})]})]}),s&&(0,i.jsx)(eV.rr,{className:"text-sm text-muted-foreground",children:s})]}),e5=({children:e,className:a="sm:max-w-md",onKeyDown:t,ariaLabelledBy:s,ariaDescribedBy:r})=>(0,i.jsx)(eV.Cf,{className:a,onKeyDown:t,"aria-labelledby":s,"aria-describedby":r,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{t&&t(e)},children:e}),e4=({cavo:e})=>(0,i.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,i.jsx)(eD.A,{className:"h-5 w-5 text-blue-600"}),(0,i.jsxs)("h3",{className:"font-semibold text-blue-800",children:["Informazioni Cavo ",e.id_cavo]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Tipologia"}),(0,i.jsx)("span",{className:"text-gray-900 font-medium",children:e.tipologia||"N/A"})]}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Formazione"}),(0,i.jsx)("span",{className:"text-gray-900 font-medium",children:e.sezione||"N/A"})]}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Da"}),(0,i.jsx)("span",{className:"text-gray-900 font-medium",children:e.ubicazione_partenza||"N/A"})]}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"A"}),(0,i.jsx)("span",{className:"text-gray-900 font-medium",children:e.ubicazione_arrivo||"N/A"})]}),(0,i.jsxs)("div",{className:"flex flex-col col-span-2",children:[(0,i.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Metri Posati"}),(0,i.jsxs)("span",{className:"text-blue-600 font-bold text-lg",children:[e.metratura_reale||0," m"]})]})]})]}),e3=({open:e,onClose:a,cavo:t,onSave:r})=>{let{cantiere:l}=(0,o.A)(),[c,d]=(0,s.useState)(!1),[u,p]=(0,s.useState)(!1),[h,b]=(0,s.useState)(""),[v,f]=(0,s.useState)(""),[j,N]=(0,s.useState)("compatible"),[y,C]=(0,s.useState)(""),[w,_]=(0,s.useState)([]),[A,z]=(0,s.useState)(""),k=async()=>{if(console.log("\uD83C\uDFAF ModificaBobinaModal: Caricamento bobine:",{cavo:!!t,cantiere:!!l,cavoId:t?.id_cavo,cantiereId:l?.id_cantiere}),t&&l)try{p(!0);let e=await m.Fw.getBobine(l.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(t){console.log("\uD83D\uDD0D ModificaBobinaModal: Filtro per cavo:",{tipologia:t.tipologia,sezione:t.sezione});let e=i.map(e=>({...e,compatible:e.tipologia===t.tipologia&&e.sezione===t.sezione}));_(e),console.log("✅ ModificaBobinaModal: Bobine caricate:",e.length)}else _([])}catch(e){console.error("Errore caricamento bobine:",e),z("Errore durante il caricamento delle bobine")}finally{p(!1)}};(0,s.useEffect)(()=>{e&&t&&(l?k():console.log("❌ ModificaBobinaModal: Cantiere non disponibile"),b(""),C(""),f(""),N("compatible"),z(""))},[e,t,l]);let S=w.filter(e=>{let a=e.numero_bobina?.toLowerCase().includes(v.toLowerCase())||e.tipologia?.toLowerCase().includes(v.toLowerCase()),t="compatible"===j?e.compatible:!e.compatible;return a&&t}),I=w.filter(e=>e.compatible).length,O=w.filter(e=>!e.compatible).length;console.log("\uD83D\uDD0D ModificaBobinaModal: Stato filtri",{totaleBobine:w.length,compatibili:I,incompatibili:O,filtrate:S.length,activeTab:j,searchTerm:v});let $=async()=>{if(t&&h){d(!0);try{await r(t.id_cavo,y,h),E()}catch(e){console.error("Error saving bobina modification:",e)}finally{d(!1)}}},E=()=>{b(""),C(""),f(""),N("compatible"),z(""),_([]),a()};return t?(0,i.jsx)(eV.lG,{open:e,onOpenChange:E,children:(0,i.jsxs)(e5,{className:"sm:max-w-3xl max-h-[85vh] overflow-hidden",onKeyDown:e=>{"Escape"===e.key&&E()},ariaLabelledBy:"modifica-bobina-title",children:[(0,i.jsx)(e2,{icon:(0,i.jsx)(eD.A,{className:"h-5 w-5 text-blue-500"}),title:"Modifica Bobina Cavo",cableId:t.id_cavo,description:"Seleziona una nuova bobina per il cavo o modifica i parametri"}),(0,i.jsxs)("div",{className:"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]",children:[(0,i.jsx)(e4,{cavo:t}),A&&(0,i.jsxs)(n.Fc,{className:"bg-red-50 border-red-200",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsx)(n.TN,{className:"text-red-800",children:A})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(eq.J,{className:"text-sm font-semibold",children:"Opzioni di modifica"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,i.jsx)("input",{type:"radio",name:"modifica-option",value:"cambia-bobina",checked:"cambia-bobina"===h,onChange:e=>b(e.target.value),className:"text-blue-600 focus:ring-blue-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cambia bobina"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Assegna una bobina diversa al cavo"})]})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,i.jsx)("input",{type:"radio",name:"modifica-option",value:"bobina-vuota",checked:"bobina-vuota"===h,onChange:e=>b(e.target.value),className:"text-blue-600 focus:ring-blue-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Bobina vuota"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Rimuovi l'associazione con la bobina attuale"})]})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,i.jsx)("input",{type:"radio",name:"modifica-option",value:"annulla-posa",checked:"annulla-posa"===h,onChange:e=>b(e.target.value),className:"text-red-600 focus:ring-red-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-sm font-medium text-red-700",children:"Annulla posa"}),(0,i.jsx)("p",{className:"text-xs text-red-500",children:"Annulla l'installazione e restituisci i metri alla bobina"})]})]})]})]}),"cambia-bobina"===h&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"search-bobina",className:"text-sm font-medium",children:"Cerca bobina"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eb.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,i.jsx)(g.p,{id:"search-bobina",value:v,onChange:e=>f(e.target.value),placeholder:"Cerca bobina per ID, tipologia o numero...",className:"pl-10 h-8"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex border-b",children:[(0,i.jsxs)("button",{onClick:()=>N("compatible"),className:`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${"compatible"===j?"border-green-500 text-green-700 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[(0,i.jsx)(eC.A,{className:"h-3 w-3 text-green-500"}),"Compatibili (",I,")"]}),(0,i.jsxs)("button",{onClick:()=>N("incompatible"),className:`flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ${"incompatible"===j?"border-yellow-500 text-yellow-700 bg-yellow-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[(0,i.jsx)(eM.A,{className:"h-3 w-3 text-yellow-500"}),"Incompatibili (",O,")"]})]}),(0,i.jsx)("div",{className:"h-32 overflow-y-auto border rounded-md bg-gray-50",children:u?(0,i.jsxs)("div",{className:"p-3 text-center",children:[(0,i.jsx)(eG.A,{className:"h-6 w-6 text-blue-500 mx-auto mb-2 animate-spin"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"Caricamento bobine..."})]}):0===S.length?(0,i.jsx)("div",{className:"p-3 text-center",children:(0,i.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-3",children:[(0,i.jsx)(eM.A,{className:"h-6 w-6 text-yellow-500 mx-auto mb-1"}),(0,i.jsxs)("p",{className:"text-xs text-yellow-800 font-medium mb-1",children:["Nessuna bobina ","compatible"===j?"compatibile":"incompatibile"," trovata"]}),(0,i.jsx)("p",{className:"text-xs text-yellow-700",children:"Prova a modificare i criteri di ricerca"})]})}):(0,i.jsx)("div",{className:"space-y-1 p-1",children:S.map(e=>(0,i.jsxs)("label",{className:"flex items-center space-x-2 p-2 border rounded-md hover:bg-white cursor-pointer transition-colors bg-white",children:[(0,i.jsx)("input",{type:"radio",name:"selected-bobina",value:e.id_bobina,checked:y===e.id_bobina,onChange:e=>C(e.target.value),className:"text-blue-600 focus:ring-blue-500 w-3 h-3"}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center gap-1",children:[(0,i.jsx)("span",{className:"font-medium text-xs truncate",children:e.numero_bobina}),e.compatible?(0,i.jsx)(eC.A,{className:"h-3 w-3 text-green-500 flex-shrink-0"}):(0,i.jsx)(eM.A,{className:"h-3 w-3 text-yellow-500 flex-shrink-0"})]}),(0,i.jsxs)("p",{className:"text-xs text-gray-500 truncate",children:[e.tipologia," - ",e.sezione," - ",e.metri_residui,"m"]})]})]},e.id_bobina))})})]})]}),"annulla-posa"===h&&(0,i.jsxs)(n.Fc,{className:"bg-red-50 border-red-200",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4 text-red-600"}),(0,i.jsxs)(n.TN,{className:"text-red-800",children:[(0,i.jsx)("strong",{children:"ATTENZIONE:"}),' Questa operazione annuller\xe0 completamente l\'installazione del cavo. Tutti i metri posati saranno restituiti alla bobina originale e lo stato del cavo sar\xe0 resettato a "Da installare".']})]})]}),(0,i.jsxs)(eV.Es,{className:"gap-2 pt-4 border-t",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:E,disabled:c,className:"px-6 py-2 hover:bg-gray-50",children:"Annulla"}),(0,i.jsx)(x.$,{onClick:$,disabled:c||!h||"cambia-bobina"===h&&!y,className:`px-6 py-2 ${"annulla-posa"===h?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,variant:"annulla-posa"===h?"destructive":"default",children:c?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eG.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):"annulla-posa"===h?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eM.A,{className:"mr-2 h-4 w-4"}),"Annulla Posa"]}):"Salva Modifiche"})]})]})}):null};t(40510);let e6=(0,es.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]);function e8({open:e,onClose:a,cavo:t,cantiere:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),u=r||d,[p,h]=(0,s.useState)("assegna_nuova"),[b,v]=(0,s.useState)(""),[f,j]=(0,s.useState)([]),[N,y]=(0,s.useState)(!1),[C,w]=(0,s.useState)(!1),[_,A]=(0,s.useState)(""),[z,k]=(0,s.useState)(""),[S,I]=(0,s.useState)("compatibili"),O=(()=>{if(!t)return[];let e=f.filter(e=>{let a=e.tipologia===t.tipologia&&e.sezione===t.sezione,i=""===z||e.id_bobina.toLowerCase().includes(z.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(z.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(z.toLowerCase());return a&&i&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:t.tipologia,cavoSezione:t.sezione,totaleBobine:f.length,bobineCompatibili:e.length,searchText:z}),e})(),$=t?f.filter(e=>{let a=e.tipologia!==t.tipologia||e.sezione!==t.sezione,i=""===z||e.id_bobina.toLowerCase().includes(z.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(z.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(z.toLowerCase());return a&&i&&e.metri_residui>0}):[],E=()=>{h("assegna_nuova"),v(""),k(""),A(""),a()},T=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:p,selectedBobina:b,cavoId:t?.id_cavo,cantiereId:u?.id_cantiere}),t)try{if(w(!0),A(""),"assegna_nuova"===p){if(!b)return void c("Selezionare una bobina");let e=await m.At.updateMetriPosati({id_cavo:t.id_cavo,metri_posati:t.metratura_reale||0,id_bobina:b,force_over:!0});e.success?(l(`Bobina aggiornata con successo per il cavo ${t.id_cavo}`),E()):c(e.message||"Errore durante l'aggiornamento della bobina")}else if("rimuovi_bobina"===p){let e=await m.At.updateMetriPosati({id_cavo:t.id_cavo,metri_posati:t.metratura_reale||0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(l(`Bobina rimossa dal cavo ${t.id_cavo}`),E()):c(e.message||"Errore durante la rimozione della bobina")}else if("annulla_installazione"===p){let e=await m.At.updateMetriPosati({id_cavo:t.id_cavo,metri_posati:0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(l(`Installazione annullata per il cavo ${t.id_cavo}`),E()):c(e.message||"Errore durante l'annullamento dell'installazione")}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),c("Errore durante il salvataggio")}finally{w(!1)}};return t?(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(eV.lG,{open:e,onOpenChange:E,children:(0,i.jsxs)(eV.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,i.jsx)(eV.c7,{children:(0,i.jsxs)(eV.L3,{children:["Modifica Bobina Cavo ",t.id_cavo]})}),(0,i.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eD.A,{className:"h-5 w-5 text-blue-600"}),(0,i.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,i.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,i.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",t.tipologia||"N/A"," / Da: ",t.ubicazione_partenza||"N/A"," / Formazione: ",t.sezione||"N/A"," / A: ",t.ubicazione_arrivo||"N/A"," / Metri Posati: ",t.metratura_reale||0," m"]})})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===p,onChange:e=>h(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===p,onChange:e=>h(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]}),(0,i.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,i.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===p,onChange:e=>h(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("span",{className:"text-sm text-red-600",children:"Annulla posa"})]})]})]}),"assegna_nuova"===p&&(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eb.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,i.jsx)(g.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:z,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,i.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,i.jsx)("button",{onClick:()=>I("compatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"compatibili"===S?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Bobine Compatibili (",O.length,")"]})]})}),(0,i.jsx)("button",{onClick:()=>I("incompatibili"),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${"incompatibili"===S?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"}`,children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{children:["Bobine Incompatibili (",$.length,")"]})]})})]}),(0,i.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:N?(0,i.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(eG.A,{className:"h-4 w-4 animate-spin"}),(0,i.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,i.jsx)("div",{className:"p-2",children:"compatibili"===S?0===O.length?(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,i.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,i.jsx)("strong",{children:t.tipologia})," e formazione ",(0,i.jsx)("strong",{children:t.sezione})]})]}):(0,i.jsx)("div",{className:"space-y-2",children:O.map(e=>(0,i.jsx)("div",{onClick:()=>v(e.id_bobina),className:`p-3 rounded-lg cursor-pointer transition-all duration-200 ${b===e.id_bobina?"bg-blue-100 border-2 border-blue-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"}`,children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,i.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),e.stato_bobina&&(0,i.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"Disponibile"===e.stato_bobina?"bg-green-100 text-green-800":"In uso"===e.stato_bobina?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"}`,children:e.stato_bobina})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,i.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,i.jsx)("span",{children:e.sezione})]})]}),(0,i.jsxs)("div",{className:"text-right ml-3",children:[(0,i.jsxs)("div",{className:`text-sm font-medium ${e.metri_residui>0?"text-green-600":"text-gray-500"}`,children:[e.metri_residui,"m"]}),(0,i.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))}):0===$.length?(0,i.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,i.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,i.jsx)("div",{className:"font-medium mb-1",children:"Bobine Incompatibili"}),(0,i.jsx)("div",{className:"text-xs",children:"Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate."})]})]})}),$.map(e=>(0,i.jsx)("div",{onClick:()=>v(e.id_bobina),className:`p-3 rounded-lg cursor-pointer transition-all duration-200 ${b===e.id_bobina?"bg-orange-100 border-2 border-orange-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"}`,children:(0,i.jsxs)("div",{className:"flex justify-between items-start",children:[(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,i.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),(0,i.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"INCOMPATIBILE"})]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,i.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,i.jsx)("span",{children:e.sezione})]})]}),(0,i.jsxs)("div",{className:"text-right ml-3",children:[(0,i.jsxs)("div",{className:`text-sm font-medium ${e.metri_residui>0?"text-orange-600":"text-gray-500"}`,children:[e.metri_residui,"m"]}),(0,i.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))]})})})]})]}),_&&(0,i.jsxs)(n.Fc,{variant:"destructive",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{children:_})]}),(0,i.jsxs)(eV.Es,{className:"flex justify-end space-x-2",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:E,disabled:C,children:"Annulla"}),(0,i.jsxs)(x.$,{onClick:T,disabled:C||"assegna_nuova"===p&&!b,children:[C&&(0,i.jsx)(eG.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function e7({open:e,onClose:a,cavo:t,cantiere:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),p=r||d,[h,b]=(0,s.useState)({metri_posati:"",id_bobina:""}),[v,f]=(0,s.useState)({}),[j,N]=(0,s.useState)({}),[y,C]=(0,s.useState)(!1),[w,_]=(0,s.useState)([]),[A,z]=(0,s.useState)(!1),[k,S]=(0,s.useState)(""),[I,O]=(0,s.useState)(!1),$=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=w.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},E=t?w.filter(e=>{let a=e.tipologia===t.tipologia&&e.sezione===t.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],T=t?w.filter(e=>{let a=e.tipologia!==t.tipologia||e.sezione!==t.sezione,i=""===k||e.id_bobina.toLowerCase().includes(k.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(k.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(k.toLowerCase());return a&&i&&e.metri_residui>0}):[],M=e=>{b(a=>({...a,id_bobina:e.id_bobina})),f(e=>{let a={...e};return delete a.id_bobina,a})},F=async()=>{if(console.log("\uD83D\uDCBE InserisciMetriDialog: Salvataggio metri:",{cavo:t?.id_cavo,metri_posati:h.metri_posati,id_bobina:h.id_bobina}),!t)return;if(!h.metri_posati||0>parseFloat(h.metri_posati))return void c("Inserire metri posati validi (≥ 0)");if(!h.id_bobina)return void c("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(h.metri_posati);if("BOBINA_VUOTA"!==h.id_bobina){let e=w.find(e=>e.id_bobina===h.id_bobina);e&&e.metri_residui}try{if(C(!0),!p)throw Error("Cantiere non selezionato");console.log("\uD83D\uDE80 InserisciMetriDialog: Chiamata API updateMetriPosati:",{cantiere:p.id_cantiere,cavo:t.id_cavo,metri:e,bobina:h.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===h.id_bobina}),await m.At.updateMetriPosati(p.id_cantiere,t.id_cavo,e,h.id_bobina,!0),l(`Metri posati aggiornati con successo per il cavo ${t.id_cavo}: ${e}m`),a()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante il salvataggio dei metri posati")}finally{C(!1)}},D=()=>{y||(b({metri_posati:"",id_bobina:""}),f({}),N({}),S(""),a())};return t?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eV.lG,{open:e,onOpenChange:D,children:(0,i.jsxs)(eV.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,i.jsxs)(eV.c7,{className:"flex-shrink-0",children:[(0,i.jsxs)(eV.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(e6,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",t.id_cavo]}),(0,i.jsx)(eV.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,i.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,i.jsx)("div",{className:"lg:col-span-2",children:(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipologia:"})," ",t.tipologia||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Da:"})," ",t.ubicazione_partenza||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Formazione:"})," ",t.sezione||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"A:"})," ",t.ubicazione_arrivo||"N/A"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Metri teorici:"})," ",t.metri_teorici||"N/A"," m"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Gi\xe0 posati:"})," ",t.metratura_reale||0," m"]})]})]})}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(g.p,{id:"metri",type:"number",value:h.metri_posati,onChange:e=>b(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:y,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,i.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),v.metri_posati&&(0,i.jsx)("p",{className:"text-sm text-red-600",children:v.metri_posati}),j.metri_posati&&(0,i.jsx)("p",{className:"text-sm text-amber-600",children:j.metri_posati})]})]})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,i.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,i.jsx)("div",{className:"sm:col-span-5",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(eb.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,i.jsx)(g.p,{placeholder:"ID, tipologia, formazione...",value:k,onChange:e=>S(e.target.value),className:"pl-10",disabled:y}),k&&(0,i.jsx)(x.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>S(""),children:(0,i.jsx)(eo.A,{className:"h-4 w-4"})})]})}),(0,i.jsx)("div",{className:"sm:col-span-7",children:(0,i.jsxs)(x.$,{type:"button",variant:"BOBINA_VUOTA"===h.id_bobina?"default":"outline",className:`w-full h-10 font-bold flex items-center justify-center gap-2 ${"BOBINA_VUOTA"===h.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"}`,onClick:()=>{b(e=>({...e,id_bobina:"BOBINA_VUOTA"})),f(e=>{let a={...e};return delete a.id_bobina,a}),console.log("\uD83D\uDD04 InserisciMetriDialog: Usando BOBINA_VUOTA:",{saving:!1,metri_posati:h.metri_posati,id_bobina:"BOBINA_VUOTA",errorsCount:Object.keys(v).length})},disabled:y,children:["BOBINA_VUOTA"===h.id_bobina&&(0,i.jsx)(eC.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]})}),A?(0,i.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,i.jsx)(eG.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Caricamento bobine..."})]}):(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4"}),"Bobine Compatibili (",E.length,")"]}),(0,i.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===E.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,i.jsx)("div",{className:"divide-y",children:E.map(e=>(0,i.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${h.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"}`,onClick:()=>M(e),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[h.id_bobina===e.id_bobina&&(0,i.jsx)(eC.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,i.jsx)("div",{className:"font-bold text-base min-w-fit",children:$(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,i.jsxs)(u.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",T.length,")"]}),(0,i.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===T.length?(0,i.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,i.jsx)("div",{className:"divide-y",children:T.map(e=>(0,i.jsx)("div",{className:`p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ${h.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"}`,onClick:()=>M(e),children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[h.id_bobina===e.id_bobina&&(0,i.jsx)(eC.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,i.jsx)("div",{className:"font-bold text-base min-w-fit",children:$(e.id_bobina)}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,i.jsxs)(u.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===w.length&&!A&&(0,i.jsxs)(n.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,i.jsx)(eM.A,{className:"h-4 w-4 text-amber-600"}),(0,i.jsx)(n.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),v.id_bobina&&(0,i.jsxs)(n.Fc,{variant:"destructive",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{children:v.id_bobina})]})]})]}),(0,i.jsxs)(eV.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,i.jsx)("div",{children:"installato"===t.stato_installazione&&t.id_bobina&&(0,i.jsx)(x.$,{variant:"outline",onClick:()=>{O(!0)},disabled:y,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(x.$,{variant:"outline",onClick:D,disabled:y,children:"Annulla"}),(0,i.jsxs)(x.$,{onClick:F,disabled:y||!h.metri_posati||0>parseFloat(h.metri_posati)||!h.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[y&&(0,i.jsx)(eG.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,i.jsx)(e8,{open:I,onClose:()=>O(!1),cavo:t,onSuccess:e=>{l(e),O(!1),a()},onError:c})]}):null}var e9=t(34729),ae=t(6727),aa=t(41312);function at({open:e,onClose:a,caviSelezionati:t,tipoComanda:r,onSuccess:l,onError:c}){let{cantiere:d}=(0,o.A)(),[u,p]=(0,s.useState)({tipo_comanda:r||"POSA",responsabile:"",note:""}),[h,b]=(0,s.useState)([]),[g,f]=(0,s.useState)(!1),[j,N]=(0,s.useState)(!1),[y,C]=(0,s.useState)(""),w=async()=>{if(d){if(!u.responsabile)return void C("Seleziona un responsabile per la comanda");if(0===t.length)return void C("Seleziona almeno un cavo per la comanda");try{f(!0),C("");let e={tipo_comanda:u.tipo_comanda,responsabile:u.responsabile,note:u.note||null},i=await m.CV.createComandaWithCavi(d.id_cantiere,e,t);l(`Comanda ${i.data.codice_comanda} creata con successo per ${t.length} cavi`),a()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{f(!1)}}};return(0,i.jsx)(eV.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(eV.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(eV.c7,{children:[(0,i.jsxs)(eV.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(ae.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,i.jsxs)(eV.rr,{children:["Crea una nuova comanda per ",t.length," cavi selezionati"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)(eq.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",t.length,")"]}),(0,i.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,i.jsxs)("div",{className:"flex flex-wrap gap-1",children:[t.slice(0,10).map(e=>(0,i.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),t.length>10&&(0,i.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",t.length-10," altri..."]})]})})]}),y&&(0,i.jsxs)(n.Fc,{variant:"destructive",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{children:y})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,i.jsxs)(v.l6,{value:u.tipo_comanda,onValueChange:e=>p(a=>({...a,tipo_comanda:e})),children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{})}),(0,i.jsxs)(v.gC,{children:[(0,i.jsx)(v.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,i.jsx)(v.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,i.jsx)(v.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,i.jsx)(v.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,i.jsxs)(v.l6,{value:u.responsabile,onValueChange:e=>p(a=>({...a,responsabile:e})),disabled:j,children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{placeholder:"Seleziona responsabile..."})}),(0,i.jsx)(v.gC,{children:h.map(e=>(0,i.jsx)(v.eb,{value:e.nome_responsabile,children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(aa.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,i.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,i.jsx)(e9.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:u.note,onChange:e=>p(a=>({...a,note:e.target.value})),rows:3})]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(u.tipo_comanda)]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Responsabile:"})," ",u.responsabile||"Non selezionato"]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cavi:"})," ",t.length," selezionati"]}),u.note&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Note:"})," ",u.note]})]})]})]}),(0,i.jsxs)(eV.Es,{children:[(0,i.jsx)(x.$,{variant:"outline",onClick:a,disabled:g,children:"Annulla"}),(0,i.jsxs)(x.$,{onClick:w,disabled:g||!u.responsabile||0===t.length,children:[g?(0,i.jsx)(eG.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(ae.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var ai=t(16023);let as=(0,es.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);function ar({open:e,onClose:a,tipo:t,onSuccess:r,onError:l}){let{cantiere:c}=(0,o.A)(),[d,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(""),[b,v]=(0,s.useState)(!1),[f,j]=(0,s.useState)(""),[N,y]=(0,s.useState)(0),C=(0,s.useRef)(null),w=async()=>{if(d&&c){if("cavi"===t&&!p.trim())return void j("Inserisci il codice revisione per l'importazione cavi");try{let e;if(v(!0),j(""),y(0),e="cavi"===t?await m.mg.importCavi(c.id_cantiere,d,p.trim()):await m.mg.importBobine(c.id_cantiere,d),y(100),e.data.success){let i=e.data.details,s=e.data.message;"cavi"===t&&i?.cavi_importati?s+=` (${i.cavi_importati} cavi importati)`:"bobine"===t&&i?.bobine_importate&&(s+=` (${i.bobine_importate} bobine importate)`),r(s),a()}else l(e.data.message||"Errore durante l'importazione")}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'importazione del file")}finally{v(!1),y(0)}}},_=()=>{b||(u(null),h(""),j(""),y(0),C.current&&(C.current.value=""),a())},A=()=>"cavi"===t?"Cavi":"Bobine";return(0,i.jsx)(eV.lG,{open:e,onOpenChange:_,children:(0,i.jsxs)(eV.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(eV.c7,{children:[(0,i.jsxs)(eV.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(ai.A,{className:"h-5 w-5"}),"Importa ",A()," da Excel"]}),(0,i.jsxs)(eV.rr,{children:["Carica un file Excel per importare ",A().toLowerCase()," nel cantiere"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,i.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===t?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,i.jsxs)("li",{className:"flex items-start gap-2",children:[(0,i.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,i.jsx)("span",{children:e})]},a))})]}),f&&(0,i.jsxs)(n.Fc,{variant:"destructive",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{children:f})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"file",children:"File Excel *"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(g.p,{ref:C,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{let a=e.target.files?.[0];if(a){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(a.type)&&!a.name.toLowerCase().endsWith(".xlsx")&&!a.name.toLowerCase().endsWith(".xls"))return void j("Seleziona un file Excel valido (.xlsx o .xls)");u(a),j("")}},disabled:b,className:"flex-1"}),d&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,i.jsx)(eC.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),d&&(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)(as,{className:"h-4 w-4 inline mr-1"}),d.name," (",(d.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===t&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(eq.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,i.jsx)(g.p,{id:"revisione",value:p,onChange:e=>h(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:b}),(0,i.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),b&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(eG.A,{className:"h-4 w-4 animate-spin"}),(0,i.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),d&&(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Tipo:"})," ",A()]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"File:"})," ",d.name]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Dimensione:"})," ",(d.size/1024/1024).toFixed(2)," MB"]}),"cavi"===t&&p&&(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Revisione:"})," ",p]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cantiere:"})," ",c?.nome_cantiere]})]})]})]}),(0,i.jsxs)(eV.Es,{children:[(0,i.jsx)(x.$,{variant:"outline",onClick:_,disabled:b,children:"Annulla"}),(0,i.jsxs)(x.$,{onClick:w,disabled:b||!d||"cavi"===t&&!p.trim(),children:[b?(0,i.jsx)(eG.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(ai.A,{className:"h-4 w-4 mr-2"}),"Importa ",A()]})]})]})})}var al=t(61611);function an({open:e,onClose:a,onSuccess:t,onError:r}){let{cantiere:l}=(0,o.A)(),[c,d]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[u,h]=(0,s.useState)(!1),[b,g]=(0,s.useState)(""),v=(e,a)=>{d(t=>({...t,[e]:a}))},f=async()=>{if(l)try{h(!0);let e=await m.mg.exportCavi(l.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download",`cavi_${l.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),t("Export cavi completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei cavi")}finally{h(!1)}},j=async()=>{if(l)try{h(!0);let e=await m.mg.exportBobine(l.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download",`bobine_${l.nome_cantiere}_${new Date().toISOString().split("T")[0]}.xlsx`),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),t("Export bobine completato con successo")}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export delle bobine")}finally{h(!1)}},N=async()=>{if(l)try{h(!0),g("");let e=[];c.cavi&&e.push(f()),c.bobine&&e.push(j()),c.comande,c.certificazioni,c.responsabili,await Promise.all(e);let i=Object.values(c).filter(Boolean).length;t(`Export completato: ${i} file scaricati`),a()}catch(e){r(e.response?.data?.detail||e.message||"Errore durante l'export dei dati")}finally{h(!1)}},y=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,i.jsx)(al.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,i.jsx)(as,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,i.jsx)(as,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,i.jsx)(as,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,i.jsx)(as,{className:"h-4 w-4"}),available:!1}];return(0,i.jsx)(eV.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(eV.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(eV.c7,{children:[(0,i.jsxs)(eV.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(eZ.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,i.jsxs)(eV.rr,{children:["Seleziona i dati da esportare dal cantiere ",l?.nome_cantiere]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[b&&(0,i.jsxs)(n.Fc,{variant:"destructive",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{children:b})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),y.map(e=>(0,i.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border ${e.available?"bg-white":"bg-gray-50"}`,children:[(0,i.jsx)(p.S,{id:e.key,checked:c[e.key],onCheckedChange:a=>v(e.key,a),disabled:!e.available||u}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,i.jsxs)(eq.J,{htmlFor:e.key,className:`font-medium ${!e.available?"text-gray-500":""}`,children:[e.label,!e.available&&(0,i.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,i.jsx)("p",{className:`text-sm mt-1 ${!e.available?"text-gray-400":"text-gray-600"}`,children:e.description})]})]},e.key))]}),(0,i.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,i.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,i.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,i.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,i.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,i.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(c).filter(Boolean).length>0&&(0,i.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)(eq.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Cantiere:"})," ",l?.nome_cantiere]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(c).filter(Boolean).length]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,i.jsxs)(eV.Es,{children:[(0,i.jsx)(x.$,{variant:"outline",onClick:a,disabled:u,children:"Annulla"}),(0,i.jsxs)(x.$,{onClick:N,disabled:u||0===Object.values(c).filter(Boolean).length,children:[u?(0,i.jsx)(eG.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,i.jsx)(eZ.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(c).filter(Boolean).length>0?`(${Object.values(c).filter(Boolean).length})`:""]})]})]})})}function ao(){let{user:e,isAuthenticated:a,isLoading:t}=(0,o.A)(),{cantiereId:x,cantiere:u,isValidCantiere:p,isLoading:h,error:b}=(0,c.jV)();(0,r.useRouter)();let g=({title:e,description:a,variant:t})=>{},[v,f]=(0,s.useState)([]),[j,N]=(0,s.useState)([]),[y,C]=(0,s.useState)(!0),[w,_]=(0,s.useState)(""),[A,z]=(0,s.useState)([]),[k,S]=(0,s.useState)(!0),[I,O]=(0,s.useState)([]),[$,E]=(0,s.useState)(""),[T,M]=(0,s.useState)({open:!1,cavo:null}),[F,D]=(0,s.useState)({open:!1,cavo:null}),[B,L]=(0,s.useState)({open:!1,cavo:null}),[R,P]=(0,s.useState)({open:!1,cavo:null}),[V,q]=(0,s.useState)({open:!1,cavo:null}),[U,G]=(0,s.useState)({open:!1,cavo:null}),[J,Z]=(0,s.useState)({open:!1,cavo:null,error:""}),[W,H]=(0,s.useState)({visible:!1,message:""}),[K,Y]=(0,s.useState)({open:!1}),[X,Q]=(0,s.useState)({open:!1}),[ee,ea]=(0,s.useState)(!1),[et,ei]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0}),es=u||(x&&x>0?{id_cantiere:x,commessa:`Cantiere ${x}`}:null),er=async()=>{try{C(!0),_("");try{let e=await m.At.getCavi(x),a=e.filter(e=>!e.spare),t=e.filter(e=>e.spare);f(a),N(t),el(a)}catch(e){throw e}}catch(e){_(`Errore nel caricamento dei cavi: ${e.response?.data?.detail||e.message}`)}finally{C(!1)}},el=e=>{let a=e.length,t=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,i=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,r=e.reduce((e,a)=>e+(a.metri_teorici||0),0),l=e.reduce((e,a)=>e+(a.metri_posati||0),0);ei({totali:a,installati:t,collegati:i,certificati:s,percentualeInstallazione:a>0?Math.round(t/a*100):0,percentualeCollegamento:a>0?Math.round(i/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:r,metriInstallati:l,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},en=(e,a,t)=>{switch(a){case"insert_meters":M({open:!0,cavo:e});break;case"modify_reel":D({open:!0,cavo:e});break;case"view_command":g({title:"Visualizza Comanda",description:`Apertura comanda ${t} per cavo ${e.id_cavo}`});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"manage_connections":case"create_certificate":G({open:!0,cavo:e});break;case"disconnect_cable":P({open:!0,cavo:e});break;case"generate_pdf":q({open:!0,cavo:e})}},eo=(e,a)=>{switch(a){case"view_details":g({title:"Visualizza Dettagli",description:`Apertura dettagli per cavo ${e.id_cavo}`});break;case"edit":g({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":g({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":g({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":A.includes(e.id_cavo)?(z(A.filter(a=>a!==e.id_cavo)),g({title:"Cavo Deselezionato",description:`Cavo ${e.id_cavo} deselezionato`})):(z([...A,e.id_cavo]),g({title:"Cavo Selezionato",description:`Cavo ${e.id_cavo} selezionato`}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),g({title:"ID Copiato",description:`ID cavo ${e.id_cavo} copiato negli appunti`});break;case"copy_details":let t=`ID: ${e.id_cavo}, Tipologia: ${e.tipologia}, Formazione: ${e.formazione||e.sezione}, Metri: ${e.metri_teorici}`;navigator.clipboard.writeText(t),g({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":g({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":g({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":Y({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":Y({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":Y({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":Y({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":g({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":g({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:g({title:"Azione non implementata",description:`Azione ${a} non ancora implementata`})}},ec=e=>{g({title:"Operazione completata",description:e}),er()},ed=e=>{g({title:"Errore",description:e,variant:"destructive"})},em=async(e,a,t)=>{try{if(!u)throw Error("Cantiere non selezionato");let i="";switch(console.log("\uD83D\uDE80 ModificaBobina: Operazione:",{cantiere:u.id_cantiere,cavo:e,option:t,bobinaId:a}),t){case"cambia-bobina":await m.At.updateBobina(u.id_cantiere,e,a,!0),i=`Bobina ${a} assegnata al cavo ${e}`;break;case"bobina-vuota":await m.At.updateBobina(u.id_cantiere,e,"BOBINA_VUOTA",!1),i=`Bobina rimossa dal cavo ${e}`;break;case"annulla-posa":await m.At.cancelInstallation(u.id_cantiere,e),i=`Installazione annullata per il cavo ${e} - metri restituiti alla bobina`;break;default:throw Error("Operazione non riconosciuta")}ec(i),er()}catch(e){ed(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}};return y&&p?(0,i.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,i.jsx)(eG.A,{className:"h-8 w-8 animate-spin"}),(0,i.jsx)("span",{className:"ml-2",children:"Caricamento cavi..."})]}):(0,i.jsx)(d.u,{children:(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[w&&(0,i.jsxs)(n.Fc,{variant:"destructive",className:"mb-6",children:[(0,i.jsx)(e_.A,{className:"h-4 w-4"}),(0,i.jsx)(n.TN,{children:w})]}),(0,i.jsx)(eP,{cavi:v,filteredCavi:I,revisioneCorrente:$,className:"mb-2"}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)(e$,{cavi:v,loading:y,selectionEnabled:k,selectedCavi:A,onSelectionChange:z,onStatusAction:en,onContextMenuAction:eo})}),j.length>0&&(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsxs)(l.Zp,{children:[(0,i.jsx)(l.aR,{children:(0,i.jsxs)(l.ZB,{className:"flex items-center space-x-2",children:[(0,i.jsx)(eD.A,{className:"h-5 w-5"}),(0,i.jsxs)("span",{children:["Cavi Spare (",j.length,")"]})]})}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)(e$,{cavi:j,loading:y,selectionEnabled:!1,onStatusAction:en,onContextMenuAction:eo})})]})}),(0,i.jsx)(e7,{open:T.open,onClose:()=>M({open:!1,cavo:null}),cavo:T.cavo,cantiere:es,onSuccess:ec,onError:ed}),(0,i.jsx)(e3,{open:F.open,onClose:()=>D({open:!1,cavo:null}),cavo:F.cavo,onSave:em}),(0,i.jsx)(eY,{open:R.open,onClose:()=>P({open:!1,cavo:null}),cavo:R.cavo,onConfirm:()=>{H({visible:!0,message:"Cavo scollegato con successo"}),P({open:!1,cavo:null}),er()},onError:e=>{Z({open:!0,cavo:R.cavo,error:e}),P({open:!1,cavo:null})}}),(0,i.jsx)(eX,{open:V.open,onClose:()=>q({open:!1,cavo:null}),cavo:V.cavo,onSuccess:()=>{H({visible:!0,message:"PDF generato con successo"}),q({open:!1,cavo:null})},onError:e=>{Z({open:!0,cavo:V.cavo,error:e}),q({open:!1,cavo:null})}}),(0,i.jsx)(e0,{open:U.open,onClose:()=>G({open:!1,cavo:null}),cavo:U.cavo,onSuccess:()=>{H({visible:!0,message:"Certificazione completata con successo"}),G({open:!1,cavo:null}),er()},onError:e=>{Z({open:!0,cavo:U.cavo,error:e}),G({open:!1,cavo:null})}}),(0,i.jsx)(eQ,{open:J.open,onClose:()=>Z({open:!1,cavo:null,error:""}),cavo:J.cavo,error:J.error,onRetry:()=>{Z({open:!1,cavo:null,error:""}),J.cavo&&G({open:!0,cavo:J.cavo})}}),(0,i.jsx)(e1,{visible:W.visible,message:W.message,onClose:()=>H({visible:!1,message:""})}),(0,i.jsx)(at,{open:K.open,onClose:()=>Y({open:!1}),caviSelezionati:A,tipoComanda:K.tipoComanda,onSuccess:ec,onError:ed}),(0,i.jsx)(ar,{open:X.open,onClose:()=>Q({open:!1}),tipo:X.tipo||"cavi",onSuccess:ec,onError:ed}),(0,i.jsx)(an,{open:ee,onClose:()=>ea(!1),onSuccess:ec,onError:ed})]})})}},94735:e=>{"use strict";e.exports=require("events")},96882:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),i=a.X(0,[447,991,658,462,400,818,988,223,109,653],()=>t(52270));module.exports=i})();